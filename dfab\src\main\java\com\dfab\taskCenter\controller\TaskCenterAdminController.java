package com.dfab.taskCenter.controller;

import com.dfab.taskCenter.entity.TaskCenter;
import com.dfab.taskCenter.service.TaskCenterService;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 任务中心管理Controller（后台管理）
 */
@RestController
@RequestMapping("/admin/taskCenter")
@RequiredArgsConstructor
@Tag(name = "TaskCenterAdminController", description = "后台任务中心管理接口，提供对任务信息的增删改查功能")
public class TaskCenterAdminController {

    private final TaskCenterService taskCenterService;

    /**
     * 分页查询任务列表
     * @return 分页任务列表
     */
    @Operation(summary = "分页查询任务列表", description = "分页查询任务列表，支持按类型、状态、关键信息筛选")
    @PostMapping("/page")
//    @Log(title = "任务中心管理-分页查询任务", businessType = BusinessType.QUERY)
    public ResponseEntity<PageInfo<TaskCenter>> getTasksPage(@RequestBody TaskCenter queryParams) {
        try {
            // 设置默认分页参数
            if (queryParams.getPageNum() == null || queryParams.getPageNum() <= 0) {
                queryParams.setPageNum(1);
            }
            if (queryParams.getPageSize() == null || queryParams.getPageSize() <= 0) {
                queryParams.setPageSize(10);
            }

            PageInfo<TaskCenter> pageInfo = taskCenterService.getTasksPage(
                queryParams.getPageNum(),
                queryParams.getPageSize(),
                queryParams
            );
            return ResponseEntity.ok(pageInfo);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 查询所有任务列表
     * @return 任务列表
     */
    @Operation(summary = "查询所有任务列表", description = "查询所有任务列表")
    @PostMapping("/list")
//    @Log(title = "任务中心管理-查询任务列表", businessType = BusinessType.QUERY)
    public ResponseEntity<List<TaskCenter>> getTaskList() {
        try {
            List<TaskCenter> tasks = taskCenterService.list();
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据ID获取任务详情
     * @param id 任务ID
     * @return 任务详情
     */
    @Operation(summary = "根据ID获取任务详情", description = "通过任务ID查询对应的任务详细信息")
    @PostMapping("/detail")
    @Log(title = "任务中心管理-获取任务详情", businessType = BusinessType.QUERY)
    public ResponseEntity<TaskCenter> getTaskDetail(
            @Parameter(description = "任务ID", required = true) @RequestParam Long id) {
        try {
            TaskCenter task = taskCenterService.getById(id);
            if (task == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 处理任务
     * @param taskProcessRequest 任务处理请求
     * @return 处理结果
     */
    @Operation(summary = "处理任务", description = "处理指定的任务，处理人员自动从登录用户获取")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "任务处理成功", content = @Content),
            @ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PostMapping("/process")
    @Log(title = "任务中心管理-处理任务", businessType = BusinessType.UPDATE)
    public ResponseEntity<Map<String, Object>> processTask(@Valid @RequestBody TaskProcessRequest taskProcessRequest) {
        try {
            boolean result = taskCenterService.processTask(
                    taskProcessRequest.getTaskId(),
                    taskProcessRequest.getProcessRemark()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "任务处理成功" : "任务处理失败");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量处理任务
     * @param batchProcessRequest 批量处理请求
     * @return 处理结果
     */
    @Operation(summary = "批量处理任务", description = "批量处理多个任务")
    @PostMapping("/batchProcess")
    @Log(title = "任务中心管理-批量处理任务", businessType = BusinessType.UPDATE)
    public ResponseEntity<Map<String, Object>> batchProcessTasks(@Valid @RequestBody BatchProcessRequest batchProcessRequest) {
        try {
            boolean result = taskCenterService.batchProcessTasks(
                    batchProcessRequest.getTaskIds(),
                    batchProcessRequest.getProcessRemark()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "批量处理成功" : "批量处理部分失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新任务优先级
     * @param priorityUpdateRequest 优先级更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新任务优先级", description = "更新指定任务的优先级")
    @PostMapping("/updatePriority")
    @Log(title = "任务中心管理-更新任务优先级", businessType = BusinessType.UPDATE)
    public ResponseEntity<Map<String, Object>> updateTaskPriority(@Valid @RequestBody PriorityUpdateRequest priorityUpdateRequest) {
        try {
            boolean result = taskCenterService.updateTaskPriority(
                    priorityUpdateRequest.getTaskId(),
                    priorityUpdateRequest.getPriority()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "优先级更新成功" : "优先级更新失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取任务统计信息
     * @return 统计信息
     */
    @Operation(summary = "获取任务统计信息", description = "获取任务中心的统计信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取统计信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PostMapping("/statistics")
    @Log(title = "任务中心管理-获取统计信息", businessType = BusinessType.QUERY)
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            int totalTasks = (int) taskCenterService.count();
            int pendingTasks = taskCenterService.getPendingTaskCount();
            int processedTasks = totalTasks - pendingTasks;
            int todayTasks = taskCenterService.getTodayTaskCount();
            
            List<TaskCenter> allTasks = taskCenterService.list();

            // 车牌新增任务统计
            List<TaskCenter> carAddTaskList = allTasks.stream().filter(task -> task.getTaskType() == 1).toList();
            long carAddTasks = carAddTaskList.size();
            long carAddPending = carAddTaskList.stream().filter(task -> task.getStatus() == 0).count();
            long carAddProcessed = carAddTaskList.stream().filter(task -> task.getStatus() == 1).count();

            // 车牌删除任务统计（包含原来的离所处理，因为现在都归类为车牌删除）
            List<TaskCenter> carDeleteTaskList = allTasks.stream().filter(task -> task.getTaskType() == 2 || task.getTaskType() == 3).toList();
            long carDeleteTasks = carDeleteTaskList.size();
            long carDeletePending = carDeleteTaskList.stream().filter(task -> task.getStatus() == 0).count();
            long carDeleteProcessed = carDeleteTaskList.stream().filter(task -> task.getStatus() == 1).count();

            // 预产期提醒任务统计
            List<TaskCenter> expectedDeliveryTaskList = allTasks.stream().filter(task -> task.getTaskType() == 7).toList();
            long expectedDeliveryTasks = expectedDeliveryTaskList.size();
            long expectedDeliveryPending = expectedDeliveryTaskList.stream().filter(task -> task.getStatus() == 0).count();
            long expectedDeliveryProcessed = expectedDeliveryTaskList.stream().filter(task -> task.getStatus() == 1).count();

            // 入院待产通知任务统计
            List<TaskCenter> hospitalAdmissionTaskList = allTasks.stream().filter(task -> task.getTaskType() == 8).toList();
            long hospitalAdmissionTasks = hospitalAdmissionTaskList.size();
            long hospitalAdmissionPending = hospitalAdmissionTaskList.stream().filter(task -> task.getStatus() == 0).count();
            long hospitalAdmissionProcessed = hospitalAdmissionTaskList.stream().filter(task -> task.getStatus() == 1).count();

            // 出院手续办理通知任务统计
            List<TaskCenter> dischargeTaskList = allTasks.stream().filter(task -> task.getTaskType() == 9).toList();
            long dischargeTasks = dischargeTaskList.size();
            long dischargePending = dischargeTaskList.stream().filter(task -> task.getStatus() == 0).count();
            long dischargeProcessed = dischargeTaskList.stream().filter(task -> task.getStatus() == 1).count();

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalTasks", totalTasks);
            statistics.put("pendingTasks", pendingTasks);
            statistics.put("processedTasks", processedTasks);
            statistics.put("todayTasks", todayTasks);
            statistics.put("carAddTasks", carAddTasks);
            statistics.put("carAddPending", carAddPending);
            statistics.put("carAddProcessed", carAddProcessed);
            statistics.put("carDeleteTasks", carDeleteTasks);
            statistics.put("carDeletePending", carDeletePending);
            statistics.put("carDeleteProcessed", carDeleteProcessed);
            statistics.put("expectedDeliveryTasks", expectedDeliveryTasks);
            statistics.put("expectedDeliveryPending", expectedDeliveryPending);
            statistics.put("expectedDeliveryProcessed", expectedDeliveryProcessed);
            statistics.put("hospitalAdmissionTasks", hospitalAdmissionTasks);
            statistics.put("hospitalAdmissionPending", hospitalAdmissionPending);
            statistics.put("hospitalAdmissionProcessed", hospitalAdmissionProcessed);
            statistics.put("dischargeTasks", dischargeTasks);
            statistics.put("dischargePending", dischargePending);
            statistics.put("dischargeProcessed", dischargeProcessed);
            
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 任务处理请求DTO
     */
    @Data
    public static class TaskProcessRequest {
        private Long taskId;
        private String processRemark;
    }

    /**
     * 批量处理请求DTO
     */
    public static class BatchProcessRequest {
        private List<Long> taskIds;
        private String processRemark;

        // Getters and Setters
        public List<Long> getTaskIds() { return taskIds; }
        public void setTaskIds(List<Long> taskIds) { this.taskIds = taskIds; }

        public String getProcessRemark() { return processRemark; }
        public void setProcessRemark(String processRemark) { this.processRemark = processRemark; }
    }

    /**
     * 优先级更新请求DTO
     */
    public static class PriorityUpdateRequest {
        private Long taskId;
        private Integer priority;

        // Getters and Setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }

        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
    }

    /**
     * 更新任务基本信息
     * @param basicInfoUpdateRequest 基本信息更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新任务基本信息", description = "更新任务的标题、描述、关键信息等基本信息")
    @PostMapping("/updateBasicInfo")
    @Log(title = "任务中心管理-更新基本信息", businessType = BusinessType.UPDATE)
    public ResponseEntity<Map<String, Object>> updateTaskBasicInfo(@Valid @RequestBody BasicInfoUpdateRequest basicInfoUpdateRequest) {
        try {
            boolean result = taskCenterService.updateTaskBasicInfo(
                    basicInfoUpdateRequest.getTaskId(),
                    basicInfoUpdateRequest.getTitle(),
                    basicInfoUpdateRequest.getDescription(),
                    basicInfoUpdateRequest.getKeyInfo(),
                    basicInfoUpdateRequest.getPriority()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "基本信息更新成功" : "基本信息更新失败");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新任务客户信息
     * @param customerInfoUpdateRequest 客户信息更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新任务客户信息", description = "更新任务的客户姓名、电话等信息")
    @PostMapping("/updateCustomerInfo")
    @Log(title = "任务中心管理-更新客户信息", businessType = BusinessType.UPDATE)
    public ResponseEntity<Map<String, Object>> updateTaskCustomerInfo(@Valid @RequestBody CustomerInfoUpdateRequest customerInfoUpdateRequest) {
        try {
            boolean result = taskCenterService.updateTaskCustomerInfo(
                    customerInfoUpdateRequest.getTaskId(),
                    customerInfoUpdateRequest.getCustomerName(),
                    customerInfoUpdateRequest.getCustomerPhone()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "客户信息更新成功" : "客户信息更新失败");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 基本信息更新请求DTO
     */
    public static class BasicInfoUpdateRequest {
        private Long taskId;
        private String title;
        private String description;
        private String keyInfo;
        private Integer priority;

        // Getters and Setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getKeyInfo() { return keyInfo; }
        public void setKeyInfo(String keyInfo) { this.keyInfo = keyInfo; }

        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
    }

    /**
     * 客户信息更新请求DTO
     */
    public static class CustomerInfoUpdateRequest {
        private Long taskId;
        private String customerName;
        private String customerPhone;

        // Getters and Setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }

        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }

        public String getCustomerPhone() { return customerPhone; }
        public void setCustomerPhone(String customerPhone) { this.customerPhone = customerPhone; }
    }
}
