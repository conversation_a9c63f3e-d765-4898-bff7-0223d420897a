package com.dfab.taskCenter.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.dfab.taskCenter.entity.TaskCenter;
import com.dfab.taskCenter.service.TaskCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 孕产期提醒定时任务
 * 每天检查会员的预产期、入院待产日期、出院日期，在这些日期前三天自动生成相应的提醒任务
 */
@Slf4j
@Component
public class MaternityReminderTask {

    @Autowired
    private MemberCheckinService memberCheckinService;

    @Autowired
    private TaskCenterService taskCenterService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 执行孕产期提醒任务检查
     * 每天早上8点执行一次，检查所有会员的孕产期相关日期
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void executeMaternityReminderCheck() {
        log.info("开始执行孕产期提醒任务检查");
        
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();
            LocalDate threeDaysLater = today.plusDays(3);
            
            // 查询所有已入住的会员
            LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MemberCheckin::getStatus, 0); // 0表示已入住
            List<MemberCheckin> memberList = memberCheckinService.list(queryWrapper);
            
            log.info("找到{}个已入住会员，开始检查孕产期相关日期", memberList.size());
            
            int reminderCount = 0;
            
            for (MemberCheckin member : memberList) {
                try {
                    // 检查预产期提醒
                    if (checkAndCreateExpectedDeliveryReminder(member, threeDaysLater)) {
                        reminderCount++;
                    }
                    
                    // 检查入院待产通知
                    if (checkAndCreateHospitalAdmissionNotification(member, threeDaysLater)) {
                        reminderCount++;
                    }
                    
                    // 检查出院手续办理通知
                    if (checkAndCreateDischargeNotification(member, threeDaysLater)) {
                        reminderCount++;
                    }
                    
                } catch (Exception e) {
                    log.error("处理会员{}(ID:{})的孕产期提醒时发生错误: {}", 
                             member.getMemberName(), member.getId(), e.getMessage(), e);
                }
            }
            
            log.info("孕产期提醒任务检查完成，共生成{}个提醒任务", reminderCount);
            
        } catch (Exception e) {
            log.error("执行孕产期提醒任务检查时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查并创建预产期提醒任务
     */
    private boolean checkAndCreateExpectedDeliveryReminder(MemberCheckin member, LocalDate threeDaysLater) {
        if (member.getExpectedDeliveryDate() == null) {
            return false;
        }
        
        // 如果预产期是三天后，创建提醒任务
        if (member.getExpectedDeliveryDate().equals(threeDaysLater)) {
            // 检查是否已经存在相同的任务，避免重复创建
            if (isTaskAlreadyExists(member.getId(), 7, member.getExpectedDeliveryDate().format(DATE_FORMATTER))) {
                log.debug("会员{}的预产期提醒任务已存在，跳过创建", member.getMemberName());
                return false;
            }
            
            try {
                taskCenterService.createExpectedDeliveryReminderTask(
                    member.getId(),
                    member.getMemberName(),
                    member.getPhoneNumber(),
                    member.getOpenId(),
                    member.getUserId(),
                    member.getExpectedDeliveryDate().format(DATE_FORMATTER)
                );
                
                log.info("为会员{}创建预产期提醒任务，预产期: {}", 
                        member.getMemberName(), member.getExpectedDeliveryDate());
                return true;
                
            } catch (Exception e) {
                log.error("为会员{}创建预产期提醒任务失败: {}", member.getMemberName(), e.getMessage(), e);
            }
        }
        
        return false;
    }

    /**
     * 检查并创建入院待产通知任务
     */
    private boolean checkAndCreateHospitalAdmissionNotification(MemberCheckin member, LocalDate threeDaysLater) {
        if (member.getHospitalAdmissionDate() == null) {
            return false;
        }
        
        // 如果入院待产日期是三天后，创建通知任务
        if (member.getHospitalAdmissionDate().equals(threeDaysLater)) {
            // 检查是否已经存在相同的任务，避免重复创建
            if (isTaskAlreadyExists(member.getId(), 8, member.getHospitalAdmissionDate().format(DATE_FORMATTER))) {
                log.debug("会员{}的入院待产通知任务已存在，跳过创建", member.getMemberName());
                return false;
            }
            
            try {
                taskCenterService.createHospitalAdmissionNotificationTask(
                    member.getId(),
                    member.getMemberName(),
                    member.getPhoneNumber(),
                    member.getOpenId(),
                    member.getUserId(),
                    member.getHospitalAdmissionDate().format(DATE_FORMATTER)
                );
                
                log.info("为会员{}创建入院待产通知任务，入院日期: {}", 
                        member.getMemberName(), member.getHospitalAdmissionDate());
                return true;
                
            } catch (Exception e) {
                log.error("为会员{}创建入院待产通知任务失败: {}", member.getMemberName(), e.getMessage(), e);
            }
        }
        
        return false;
    }

    /**
     * 检查并创建出院手续办理通知任务
     */
    private boolean checkAndCreateDischargeNotification(MemberCheckin member, LocalDate threeDaysLater) {
        if (member.getDischargeDate() == null) {
            return false;
        }
        
        // 如果出院日期是三天后，创建通知任务
        if (member.getDischargeDate().equals(threeDaysLater)) {
            // 检查是否已经存在相同的任务，避免重复创建
            if (isTaskAlreadyExists(member.getId(), 9, member.getDischargeDate().format(DATE_FORMATTER))) {
                log.debug("会员{}的出院手续办理通知任务已存在，跳过创建", member.getMemberName());
                return false;
            }
            
            try {
                taskCenterService.createDischargeNotificationTask(
                    member.getId(),
                    member.getMemberName(),
                    member.getPhoneNumber(),
                    member.getOpenId(),
                    member.getUserId(),
                    member.getDischargeDate().format(DATE_FORMATTER)
                );
                
                log.info("为会员{}创建出院手续办理通知任务，出院日期: {}", 
                        member.getMemberName(), member.getDischargeDate());
                return true;
                
            } catch (Exception e) {
                log.error("为会员{}创建出院手续办理通知任务失败: {}", member.getMemberName(), e.getMessage(), e);
            }
        }
        
        return false;
    }

    /**
     * 检查指定类型的任务是否已经存在
     */
    private boolean isTaskAlreadyExists(Long memberCheckinId, Integer taskType, String keyInfo) {
        try {
            LambdaQueryWrapper<TaskCenter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TaskCenter::getMemberCheckinId, memberCheckinId)
                       .eq(TaskCenter::getTaskType, taskType)
                       .eq(TaskCenter::getKeyInfo, keyInfo)
                       .eq(TaskCenter::getStatus, 0); // 只检查待处理的任务
            
            List<TaskCenter> existingTasks = taskCenterService.list(queryWrapper);
            return !existingTasks.isEmpty();
            
        } catch (Exception e) {
            log.error("检查任务是否存在时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }
}
