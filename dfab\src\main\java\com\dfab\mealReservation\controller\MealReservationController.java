package com.dfab.mealReservation.controller;

import com.dfab.mealReservation.entity.MealReservation;
import com.dfab.mealReservation.service.MealReservationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * 餐食预约 Controller 类，提供餐食预约信息的增删改查接口。
 */
@RestController
@RequestMapping("/api/mealReservation")
@Tag(name = "MealReservationController", description = "小程序餐食预约接口，提供对餐食预约信息的增删改查功能")
public class MealReservationController {

    @Autowired
    private MealReservationService mealReservationService;


    @Autowired
    private HttpServletRequest request;

    /**
     * 根据 ID 获取餐食预约信息
     * @return 对应的餐食预约信息
     */
    @Operation(summary = "根据 ID 获取餐食预约信息", description = "通过指定的餐食预约记录 ID 查询对应的预约信息")
    @Log(title = "餐食预约-根据 ID 获取餐食预约信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    @PostMapping("/get")
    public MealReservation getReservationById(@Parameter(description = "餐食预约记录的 ID", required = true)  @RequestBody MealReservation reservation) {
        String openId = (String)request.getAttribute("openId");
        reservation.setOpenId(openId);
        return mealReservationService.get(reservation);
    }

    /**
     * 创建新的餐食预约信息
     * @param reservation 餐食预约信息实体
     * @return 创建成功的餐食预约信息
     */
    @Operation(summary = "创建新的餐食预约信息", description = "将新的餐食预约信息保存到数据库中")
    @Log(title = "餐食预约-创建新的餐食预约信息", businessType = BusinessType.INSERT,operatorType = OperatorType.MOBILE)
    @PostMapping("/save")
    public MealReservation createReservation(@Parameter(description = "餐食预约信息实体", required = true)  @RequestBody MealReservation reservation) {
        String openId = (String)request.getAttribute("openId");
        reservation.setOpenId(openId);
        mealReservationService.add(reservation);
        return reservation;
    }

    /**
     * 更新餐食预约信息
     * @param reservation 新的餐食预约信息实体
     * @return 更新后的餐食预约信息
     */
    @Operation(summary = "更新餐食预约信息", description = "根据指定的 ID 更新对应的餐食预约信息")
    @Log(title = "餐食预约-更新餐食预约信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MOBILE)
    @PostMapping("/update")
    public MealReservation updateReservation( @Parameter(description = "餐食预约信息实体", required = true) @RequestBody MealReservation reservation) {
        String openId = (String)request.getAttribute("openId");
        reservation.setOpenId(openId);
        mealReservationService.update(reservation);
        return reservation;
    }

    /**
     * 删除餐食预约信息
     */
    @Operation(summary = "删除餐食预约信息", description = "根据指定的 ID 删除对应的餐食预约信息")
    @Log(title = "餐食预约-删除餐食预约信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MOBILE)
    @PostMapping("/remove")
    public void deleteReservation( @RequestBody MealReservation reservation) {
        String openId = (String)request.getAttribute("openId");
        reservation.setOpenId(openId);
        mealReservationService.remove(reservation);
    }

    /**
     * 根据 openId 获取餐食预约信息
     * @return 对应 openId 的餐食预约信息列表
     */
    @Operation(summary = "根据 openId 获取餐食预约信息", description = "通过用户的微信 openId 查询对应的餐食预约信息")
    @Log(title = "餐食预约-获取餐食预约信息列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    @PostMapping("/list")
    public List<MealReservation> getAllReservationsByOpenId() {
        String openId = (String)request.getAttribute("openId");
        return mealReservationService.getAllReservationsByOpenId(openId);
    }


    /**
     * 根据 openId 获取当天的餐食预约信息
     * @return 对应 openId 的当天餐食预约信息列表
     */
    @Operation(summary = "根据 openId 获取当天的餐食预约信息", description = "通过用户的微信 openId 查询当天对应的餐食预约信息")
    @Log(title = "餐食预约-获取当天的餐食预约信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    @PostMapping("/today")
    public List<MealReservation> getTodayReservations() {
        String openId = (String)request.getAttribute("openId");
        LocalDate today = LocalDate.now();
        return mealReservationService.getReservationsByOpenIdAndDate(openId, today);
    }
}