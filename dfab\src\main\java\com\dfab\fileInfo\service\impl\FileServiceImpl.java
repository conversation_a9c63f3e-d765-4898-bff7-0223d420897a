package com.dfab.fileInfo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.fileInfo.entity.FileInfo;
import com.dfab.fileInfo.mapper.FileInfoMapper;
import com.dfab.fileInfo.service.FileService;
import jakarta.servlet.http.HttpServletResponse;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;

import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
public class FileServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements FileService {

    @Override
    public FileInfo uploadFile(MultipartFile file, String packageName) throws IOException {
        if (file.isEmpty()) {
            throw new IOException("上传的文件为空");
        }

        // 生成唯一的文件 ID
        Long id = IdWorker.getId();
        String fileName = file.getOriginalFilename();

        // 根据操作系统类型区分根目录
        String rootDir;
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            rootDir = "d:\\file\\uploads";
        } else {
            rootDir = "/dfab/uploads";
        }

        String packagePath = rootDir + File.separator + packageName;
        String originalFilePath = packagePath + File.separator + id + "_" + fileName;

        // 创建上传目录
        File packageDir = new File(packagePath);
        if (!packageDir.exists()) {
            packageDir.mkdirs();
        }

        // 保存原图
        Path originalPath = Paths.get(originalFilePath);
        Files.copy(file.getInputStream(), originalPath);

        // 检查是否为图片
        if (isImage(fileName)) {
            saveResizedImages(file, id, packagePath, fileName);
        }

        // 创建文件信息对象
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(id);
        fileInfo.setFileName(fileName);
        fileInfo.setFilePath(originalFilePath);
        fileInfo.setFileSize(file.getSize());
        fileInfo.setVisualFileSize(formatFileSize(file.getSize()));
        fileInfo.setFileType(file.getContentType());
        fileInfo.setBucket(packageName);

        // 保存文件信息到数据库
        this.save(fileInfo);

        return fileInfo;
    }

    private boolean isImage(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return extension.matches("(?i)(jpg|jpeg|png|gif|bmp)");
    }

    private void saveResizedImages(MultipartFile file, Long id, String packagePath, String fileName) {
        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                // 记录日志，表明无法读取图片
                System.err.println("无法读取图片: " + fileName);
                return;
            }

            // 小图：宽度 200px
            String smallFilePath = packagePath + File.separator + id + "_small_" + fileName;
            try {
                Thumbnails.of(originalImage)
                          .width(200)
                          .toFile(smallFilePath);
            } catch (IOException e) {
                e.printStackTrace(); 
            }

            // 中图：宽度 400px
            String mediumFilePath = packagePath + File.separator + id + "_medium_" + fileName;
            try {
                Thumbnails.of(originalImage)
                          .width(400)
                          .toFile(mediumFilePath);
            } catch (IOException e) {
                e.printStackTrace(); 
            }

            // 大图：宽度 800px
            String largeFilePath = packagePath + File.separator + id + "_large_" + fileName;
            try {
                Thumbnails.of(originalImage)
                          .width(800)
                          .toFile(largeFilePath);
            } catch (IOException e) {
                e.printStackTrace(); 
            }

            // 新增 larger 尺寸，宽度 1200px
            String largerFilePath = packagePath + File.separator + id + "_larger_" + fileName;
            try {
                Thumbnails.of(originalImage)
                          .width(1200)
                          .toFile(largerFilePath);
            } catch (IOException e) {
                e.printStackTrace(); 
            }

            // 新增 morelarge 尺寸，宽度 1600px
            String moreLargeFilePath = packagePath + File.separator + id + "_morelarge_" + fileName;
            try {
                Thumbnails.of(originalImage)
                          .width(1600)
                          .toFile(moreLargeFilePath);
            } catch (IOException e) {
                e.printStackTrace(); 
            }
        } catch (IOException e) {
            // 记录读取输入流时的异常
            System.err.println("读取文件输入流时出错: " + fileName);
            e.printStackTrace();
        }
    }

    @Override
    public void downloadFile(String id, HttpServletResponse response) throws IOException {
        FileInfo fileInfo = getFileInfoById(id);
        if (fileInfo == null) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        File file = new File(fileInfo.getFilePath());
        if (!file.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        response.setContentType(fileInfo.getFileType());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileInfo.getFileName() + "\"");
        response.setContentLengthLong(fileInfo.getFileSize());

        try (InputStream inputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    @Override
    public void previewFile(String id, HttpServletResponse response) throws IOException {
        FileInfo fileInfo = getFileInfoById(id);
        if (fileInfo == null) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        File file = new File(fileInfo.getFilePath());
        if (!file.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        response.setContentType(fileInfo.getFileType());
        response.setContentLengthLong(fileInfo.getFileSize());

        try (InputStream inputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    private FileInfo getFileInfoById(String id) {
        // 这里需要实现根据文件 ID 查询文件信息的逻辑，可使用数据库操作
        return this.getById(id);
    }

    private String formatFileSize(long size) {
        if (size <= 0) return "0 B";
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
}