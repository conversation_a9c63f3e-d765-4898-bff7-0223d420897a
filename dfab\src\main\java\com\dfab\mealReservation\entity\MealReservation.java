package com.dfab.mealReservation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 餐食预约实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("meal_reservation")
@Schema(description = "餐食预约实体类")
@Builder
public class MealReservation extends BaseEntity {
    /**
     * 预约记录的唯一标识，系统自动分配的 ID。
     * 使用JsonSerialize将Long转为String，避免前端精度丢失
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "预约记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户的微信 openId
     */
    @Schema(description = "用户的微信 openId", example = "wx1234567890", required = true)
    private String openId;


     /**
     * 用户id userId
     */
    @Schema(description = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @Schema(description = "入住会员ID", example = "1")
    private Long memberCheckinId;

    /**
     * 预约日期
     */
    @Schema(description = "预约日期", example = "2024-01-01", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private LocalDate reservationDate;

    @TableField(exist = false)
    @Schema(description = "预约日期范围开始", example = "2024-01-01")
    private LocalDate reservationDateBegin;

    @TableField(exist = false)
    @Schema(description = "预约日期范围结束", example = "2024-01-01")
    private LocalDate reservationDateEnd;

    /**
     * 用餐时段 可取值为 Breakfast（早餐） Lunch（午餐） Dinner（晚餐）
     */
    @Schema(description = "用餐时段，可取值为 'breakfast'（早餐）, 'lunch'（午餐）, 'dinner'（晚餐）", example = "lunch", required = true)
    private String mealTime;

    /**
     * 预约份数
     */
    @Schema(description = "预约份数", example = "2", required = true)
    private Integer quantity;

    /**
     * 用户姓名
     */
    @Schema(description = "姓名")
    private String userName;

    /**
     * 用户电话
     */
    @Schema(description = "用户电话")
    private String userPhone;

    /**
     * 房间号
     */
    @Schema(description = "房间号")
    private String roomNumber;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注")
    private String remark;

    /**
     * 冗余字段后续删除
     */
    @TableField(exist = false)
    private Boolean userNameDisabled;

    /**
     * 冗余字段后续删除
     */
    @TableField(exist = false)
    private Boolean roomNumberDisabled;

    /**
     * <el-option label="已入住" value="0" />
     * <el-option label="待入住" value="3" />
     *
     *  <el-option label="已退住" value="1" />
     */
    @TableField(exist = false)
    private Integer memberStatus;
}