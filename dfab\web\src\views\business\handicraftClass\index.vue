<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课程类型" prop="classType">
        <el-select v-model="queryParams.classType" placeholder="请选择课程类型" clearable style="width: 200px">
          <el-option
            v-for="(name, code) in classTypes"
            :key="code"
            :label="name"
            :value="parseInt(code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择课程状态" clearable style="width: 200px">
          <el-option label="待开始" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程名称" prop="className">
        <el-input
          v-model="queryParams.className"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="授课老师" prop="teacher">
        <el-input
          v-model="queryParams.teacher"
          placeholder="请输入授课老师"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程时间" prop="classTimeRange">
        <el-date-picker
          v-model="queryParams.classTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:handicraftClass:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:handicraftClass:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:handicraftClass:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:handicraftClass:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 手工课表单弹窗 -->
    <HandicraftClassForm
      v-model:visible="formVisible"
      :classId="selectedClassId"
      @success="getList"
    />

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">今日课程</div>
            <div class="statistic-value">{{ statistics.todayCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">待开始</div>
            <div class="statistic-value">{{ statistics.pendingCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="handicraftClassList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column label="课程类型" align="center" prop="classType" width="100">
        <template #default="scope">
          <el-tag :type="getClassTypeTag(scope.row.classType).type">
            {{ getClassTypeTag(scope.row.classType).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="课程名称" align="center" prop="className" width="150" show-overflow-tooltip />
      <el-table-column label="授课老师" align="center" width="120">
        <template #default="scope">
          <div v-if="scope.row.teacher">{{ scope.row.teacher }}</div>
          <div v-if="scope.row.teacherPhone" style="font-size: 12px; color: #999;">{{ scope.row.teacherPhone }}</div>
          <span v-if="!scope.row.teacher" style="color: #999;">未分配</span>
        </template>
      </el-table-column>
      <el-table-column label="课程时间" align="center" prop="classTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.classTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程地点" align="center" prop="location" width="120" show-overflow-tooltip />
      <el-table-column label="时长" align="center" prop="duration" width="80">
        <template #default="scope">
          <span v-if="scope.row.duration">{{ scope.row.duration }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="参与人数" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.currentParticipants || 0 }}</span>
          <span v-if="scope.row.maxParticipants">/{{ scope.row.maxParticipants }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status).type">
            {{ getStatusTag(scope.row.status).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="课程难度" align="center" prop="difficulty" width="80">
        <template #default="scope">
          <el-tag v-if="scope.row.difficulty" :type="getDifficultyTag(scope.row.difficulty).type" size="small">
            {{ getDifficultyTag(scope.row.difficulty).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['business:handicraftClass:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['business:handicraftClass:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['business:handicraftClass:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="手工课详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="课程类型">
          <el-tag :type="getClassTypeTag(detailForm.classType).type">
            {{ getClassTypeTag(detailForm.classType).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="课程状态">
          <el-tag :type="getStatusTag(detailForm.status).type">
            {{ getStatusTag(detailForm.status).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="课程名称" :span="2">{{ detailForm.className || '无' }}</el-descriptions-item>
        <el-descriptions-item label="课程描述" :span="2">{{ detailForm.description || '无' }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(detailForm.startTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ parseTime(detailForm.endTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        <el-descriptions-item label="最大参与人数">{{ detailForm.maxParticipants || '无限制' }}</el-descriptions-item>
        <el-descriptions-item label="当前参与人数">{{ detailForm.currentParticipants || 0 }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailForm.modifyTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HandicraftClass">
import {
  listHandicraftClass,
  delHandicraftClass,
  getClassTypes,
  getClassStatistics
} from "@/api/business/handicraftClass";
import HandicraftClassForm from './form.vue';
import { getCurrentInstance, ref, reactive, toRefs, onMounted } from 'vue';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance();

const handicraftClassList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const classTypes = ref({});
const statistics = ref({});
const formVisible = ref(false);
const selectedClassId = ref(null);
const detailOpen = ref(false);
const detailForm = ref({});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    classType: undefined,
    status: undefined,
    className: undefined,
    teacher: undefined,
    classTimeRange: undefined,
    classTimeStart: undefined,
    classTimeEnd: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询手工课列表 */
function getList() {
  loading.value = true;
  
  // 处理时间范围
  if (queryParams.value.classTimeRange && queryParams.value.classTimeRange.length === 2) {
    queryParams.value.classTimeStart = queryParams.value.classTimeRange[0];
    queryParams.value.classTimeEnd = queryParams.value.classTimeRange[1];
  } else {
    queryParams.value.classTimeStart = undefined;
    queryParams.value.classTimeEnd = undefined;
  }
  
  // 创建查询参数副本，移除classTimeRange字段
  const queryData = { ...queryParams.value };
  delete queryData.classTimeRange;
  
  listHandicraftClass(queryData).then(response => {
    if (response && response.records) {
      handicraftClassList.value = response.records;
      total.value = response.total;
    } else {
      handicraftClassList.value = [];
      total.value = 0;
    }
    loading.value = false;
  });
}

/** 获取课程类型 */
function getClassTypesList() {
  getClassTypes().then(response => {
    classTypes.value = response.types || {};
  });
}

/** 获取统计信息 */
function getStatistics() {
  getClassStatistics({}).then(response => {
    statistics.value = response;
  });
}

/** 课程类型标签 */
function getClassTypeTag(type) {
  const typeMap = {
    1: { text: '手工编织', type: 'success' },
    2: { text: '手工制作', type: 'warning' },
    3: { text: '绘画课程', type: 'primary' },
    4: { text: '其他手工', type: 'info' }
  };
  return typeMap[type] || { text: '未知', type: 'danger' };
}

/** 状态标签 */
function getStatusTag(status) {
  const statusMap = {
    0: { text: '待开始', type: 'warning' },
    1: { text: '进行中', type: 'primary' },
    2: { text: '已完成', type: 'success' },
    3: { text: '已取消', type: 'danger' }
  };
  return statusMap[status] || { text: '未知', type: 'info' };
}

/** 难度标签 */
function getDifficultyTag(difficulty) {
  const difficultyMap = {
    1: { text: '初级', type: 'success' },
    2: { text: '中级', type: 'warning' },
    3: { text: '高级', type: 'danger' }
  };
  return difficultyMap[difficulty] || { text: '未知', type: 'info' };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  selectedClassId.value = null;
  formVisible.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row.id || ids.value[0];
  selectedClassId.value = id;
  formVisible.value = true;
}

/** 详情按钮操作 */
function handleDetail(row) {
  detailForm.value = row;
  detailOpen.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const deleteIds = row.id ? [row.id] : ids.value;
  proxy.$modal.confirm('是否确认删除手工课编号为"' + deleteIds + '"的数据项？').then(function() {
    const promises = deleteIds.map(id => delHandicraftClass({ id: id }));
    return Promise.all(promises);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/handicraftClass/export', {
    ...queryParams.value
  }, `handicraftClass_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  getClassTypesList();
  getStatistics();
});
</script>

<style scoped>
.statistic-item {
  text-align: center;
  padding: 20px;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.box-card {
  margin-bottom: 20px;
}
</style>
