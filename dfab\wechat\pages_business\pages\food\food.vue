<template>
	<view class="food-container">
		<custom-nav-bar title="营养餐食"></custom-nav-bar>
		<view class="food-content">
			<!-- 营养餐食介绍 -->
			<view class="food-intro">
				<view class="intro-text">
					<view class="intro-title">专业营养餐食</view>
					<view class="intro-desc">
						我们提供专业的月子餐饮服务，由营养师精心设计，厨师精心烹饪，为产妇提供营养均衡、口味适宜的月子餐，促进产后恢复。
					</view>
				</view>
			</view>

			<!-- 营养餐食图片展示 -->
			<view class="food-section">
				<view class="food-grid">
					<view
						class="food-item"
						v-for="(item, index) in foodList"
						:key="index"
						@click="previewImage(item.mediumImage, item.largeImage)"
					>
						<image
							class="food-image"
							:src="item.mediumImage"
							mode="aspectFill"
							:lazy-load="true"
						></image>
						<view class="food-name">{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import imageApi from '@/config/api/image.js';
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 营养餐食页面
	 * 展示月子中心的营养餐食图片，从后端读取
	 */

	// 后端图片ID列表
	const imageIds = [
		'1927002340661948417',
		'1927002441602068481',
		'1927002506135629825',
		'1927002553761951746',
		'1927002645076144130',
		'1927002700268990466',
		'1927002742711242753',
		'1927002794821275650'
	];

	// 营养餐食名称列表
	const foodNames = [
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' '
	];

	// 营养餐食图片列表 - 使用封装的API生成
	const foodList = ref(imageApi.generateImageList(imageIds, foodNames));

	// 图片预览功能 - 使用封装的API
	const previewImage = (currentImage, largeImage) => {
		imageApi.previewImage(currentImage, largeImage);
	};

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 营养月子餐',
			path: '/pages_business/pages/food/food',
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所营养月子餐，科学搭配助力产后恢复',
		});
	}

	onMounted(() => {
		console.log('营养餐食页面加载完成');
		console.log('营养餐食图片列表:', foodList.value);
	});
</script>

<style>
	.food-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.food-content {
		padding: 20rpx;
	}

	.food-intro {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.intro-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.intro-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		text-align: center;
	}

	.food-section {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.food-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.food-item {
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		cursor: pointer;
	}

	.food-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	}

	.food-image {
		width: 100%;
		height: 400rpx;
		object-fit: cover;
	}

	.food-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #8b5a2b;
		text-align: center;
		padding: 20rpx 15rpx 10rpx;
	}
</style>