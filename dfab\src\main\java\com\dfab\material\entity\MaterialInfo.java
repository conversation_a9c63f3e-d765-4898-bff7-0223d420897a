package com.dfab.material.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 物料信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("material_info")
@Schema(description = "物料信息实体类")
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaterialInfo extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "物料信息ID", example = "1")
    private Long id;
    
    /**
     * 物料名称
     */
    @Schema(description = "物料名称", example = "一次性无纺布内裤", required = true)
    private String materialName;
    
    /**
     * 规格型号
     */
    @Schema(description = "规格型号", example = "XXXL", required = true)
    private String specification;
    
    /**
     * 单位
     */
    @Schema(description = "单位", example = "盒", required = true)
    private String unit;
    
    /**
     * 单价
     */
    @Schema(description = "单价", example = "14.5", required = true)
    private Double unitPrice;

    /**
     * 是否套餐物料，1 是，0 不是
     */
    @Schema(description = "是否套餐物料，1 是，0 不是", example = "0")
    private Integer isPackageMaterial;

    /**
     * 套餐包含数量
     */
    @Schema(description = "套餐包含数量", example = "5")
    private Integer packageQuantity;

    /**
     * 套餐包含数量
     */
    @Schema(description = "库存数量", example = "5")
    @TableField(exist = false)
    private Integer quantity;

    /**
     * 套餐包含数量
     */
    @Schema(description = "提醒数量", example = "5")
    private Integer warnQuantity;

    /**
     * 会员id 會員套餐关联
     */
    @Schema(description = "会员id", example = "1")
    private Long memberId;

    /**
     * 套餐关联物料id
     */
    @Schema(description = "套餐关联物料id", example = "1")
    private Long materialId;

    /**
     * 会员实际使用数量
     */
    @Schema(description = "会员实际使用数量", example = "1")
    @TableField(exist = false)
    private Long memberHaveUseNum;

    /**
     * 会员实际使用数量
     */
    @Schema(description = "会员需要补充价格", example = "1")
    @TableField(exist = false)
    private BigDecimal memberNeedPay;
    /**
     * 会员实际使用数量
     */
    @Schema(description = "实际使用金额", example = "1")
    @TableField(exist = false)
    private BigDecimal truePay;

    /**
     * 会员实际使用数量
     */
    @Schema(description = "套餐金额", example = "1")
    @TableField(exist = false)
    private BigDecimal packagePay;


}