/**
 * 微信小程序分享功能工具类
 * 提供统一的分享配置和方法
 */

/**
 * 默认分享配置
 */
const DEFAULT_SHARE_CONFIG = {
	// 分享给朋友的默认配置
	appMessage: {
		title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务',
		path: '/pages/index/index',
	},
	// 分享到朋友圈的默认配置
	timeline: {
		title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
	}
};

/**
 * 页面特定的分享配置
 */
const PAGE_SHARE_CONFIGS = {
	// 首页分享配置
	'/pages/index/index': {
		appMessage: {
			title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务',
			path: '/pages/index/index',
		},
		timeline: {
			title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
		}
	},
	// 房型展示页面分享配置
	'/pages_business/pages/room/room': {
		appMessage: {
			title: '东方爱堡月子会所 - 豪华房型展示',
			path: '/pages_business/pages/room/room',
		},
		timeline: {
			title: '东方爱堡月子会所豪华房型，为您提供舒适的月子环境',
		}
	},
	// 营养餐食页面分享配置
	'/pages_business/pages/food/food': {
		appMessage: {
			title: '东方爱堡月子会所 - 营养月子餐',
			path: '/pages_business/pages/food/food',
			
		},
		timeline: {
			title: '东方爱堡月子会所营养月子餐，科学搭配助力产后恢复',
			
		}
	},
	// 产康护理页面分享配置
	'/pages_business/pages/recovery/recovery': {
		appMessage: {
			title: '东方爱堡月子会所 - 专业产康护理',
			path: '/pages_business/pages/recovery/recovery',
			
		},
		timeline: {
			title: '东方爱堡月子会所专业产康护理，助力妈妈快速恢复',
			
		}
	},
	// 宝宝护理页面分享配置
	'/pages_business/pages/baby/baby': {
		appMessage: {
			title: '东方爱堡月子会所 - 专业宝宝护理',
			path: '/pages_business/pages/baby/baby',
			
		},
		timeline: {
			title: '东方爱堡月子会所专业宝宝护理，给宝宝最贴心的照顾',
			
		}
	},
	// 优质环境页面分享配置
	'/pages_business/pages/intro/intro': {
		appMessage: {
			title: '东方爱堡月子会所 - 优质环境展示',
			path: '/pages_business/pages/intro/intro',
			
		},
		timeline: {
			title: '东方爱堡月子会所优质环境，为您提供舒适温馨的月子体验',
			
		}
	},
	// 专业团队页面分享配置
	'/pages_business/pages/team/team': {
		appMessage: {
			title: '东方爱堡月子会所 - 专业团队',
			path: '/pages_business/pages/team/team',
			
		},
		timeline: {
			title: '东方爱堡月子会所专业团队，为您提供贴心专业的月子服务',
			
		}
	},
	// 入院服务页面分享配置
	'/pages/enroll/enroll': {
		appMessage: {
			title: '东方爱堡月子会所 - 入院服务',
			path: '/pages/enroll/enroll',
			
		},
		timeline: {
			title: '东方爱堡月子会所入院服务，专业贴心的月子护理等您体验',
			
		}
	},
	// 入住服务页面分享配置
	'/pages/enroll/service': {
		appMessage: {
			title: '东方爱堡月子会所 - 入住服务',
			path: '/pages/enroll/service',
			
		},
		timeline: {
			title: '东方爱堡月子会所入住服务，让您的月子生活更加舒心',
			
		}
	},
	// 个人中心页面分享配置
	'/pages/my/my': {
		appMessage: {
			title: '东方爱堡月子会所 - 专业月子护理服务',
			path: '/pages/index/index', // 个人中心分享跳转到首页
			
		},
		timeline: {
			title: '东方爱堡月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
			
		}
	},
	// 用餐忌口页面分享配置
	'/pages_business/pages/enroll/allergy': {
		appMessage: {
			title: '东方爱堡月子会所 - 个性化营养配餐',
			path: '/pages_business/pages/food/food', // 跳转到营养餐食页面
			
		},
		timeline: {
			title: '东方爱堡月子会所个性化营养配餐，根据您的需求定制专属月子餐',
			
		}
	},
	// 陪护餐预约页面分享配置
	'/pages_business/pages/enroll/meal': {
		appMessage: {
			title: '东方爱堡月子会所 - 陪护餐预约',
			path: '/pages_business/pages/food/food', // 跳转到营养餐食页面
			
		},
		timeline: {
			title: '东方爱堡月子会所陪护餐预约，营养美味的月子餐等您品尝',
			
		}
	},
	// 陪护餐预约列表页面分享配置
	'/pages_business/pages/enroll/meal_list': {
		appMessage: {
			title: '东方爱堡月子会所 - 营养月子餐',
			path: '/pages_business/pages/food/food', // 跳转到营养餐食页面
			
		},
		timeline: {
			title: '东方爱堡月子会所营养月子餐，科学搭配助力产后恢复',
			
		}
	},
	// 个人信息页面分享配置
	'/pages_business/pages/my/info': {
		appMessage: {
			title: '东方爱堡月子会所 - 专业月子护理服务',
			path: '/pages/index/index', // 跳转到首页
			
		},
		timeline: {
			title: '东方爱堡月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
			
		}
	},
	// 我的车辆页面分享配置
	'/pages_business/pages/my/car': {
		appMessage: {
			title: '东方爱堡月子会所 - 贴心服务',
			path: '/pages/index/index', // 跳转到首页
			
		},
		timeline: {
			title: '东方爱堡月子会所贴心服务，让您的月子生活更加便利',
			
		}
	}
};

/**
 * 获取当前页面路径
 * @returns {string} 当前页面路径
 */
function getCurrentPagePath() {
	const pages = getCurrentPages();
	if (pages.length > 0) {
		const currentPage = pages[pages.length - 1];
		return '/' + currentPage.route;
	}
	return '/pages/index/index';
}

/**
 * 获取分享给朋友的配置
 * @param {Object} customConfig 自定义配置
 * @param {string} pagePath 页面路径，不传则自动获取当前页面
 * @returns {Object} 分享配置
 */
export function getShareAppMessageConfig(customConfig = {}, pagePath = null) {
	const currentPath = pagePath || getCurrentPagePath();
	const pageConfig = PAGE_SHARE_CONFIGS[currentPath];
	const baseConfig = pageConfig ? pageConfig.appMessage : DEFAULT_SHARE_CONFIG.appMessage;
	
	return {
		...baseConfig,
		...customConfig
	};
}

/**
 * 获取分享到朋友圈的配置
 * @param {Object} customConfig 自定义配置
 * @param {string} pagePath 页面路径，不传则自动获取当前页面
 * @returns {Object} 分享配置
 */
export function getShareTimelineConfig(customConfig = {}, pagePath = null) {
	const currentPath = pagePath || getCurrentPagePath();
	const pageConfig = PAGE_SHARE_CONFIGS[currentPath];
	const baseConfig = pageConfig ? pageConfig.timeline : DEFAULT_SHARE_CONFIG.timeline;
	
	return {
		...baseConfig,
		...customConfig
	};
}

/**
 * 创建页面分享方法的mixin
 * 可以在页面中使用这个mixin来快速添加分享功能
 * @param {Object} customConfig 自定义分享配置
 * @returns {Object} 包含分享方法的对象
 */
export function createShareMixin(customConfig = {}) {
	return {
		// 分享给朋友
		onShareAppMessage(res) {
			console.log('分享给朋友', res);
			
			// 如果是从按钮分享，可以获取按钮的dataset
			if (res.from === 'button') {
				console.log('从按钮分享:', res.target);
			}
			
			return getShareAppMessageConfig(customConfig.appMessage);
		},
		
		// 分享到朋友圈
		onShareTimeline(res) {
			console.log('分享到朋友圈', res);
			
			return getShareTimelineConfig(customConfig.timeline);
		}
	};
}

/**
 * 手动触发分享给朋友
 * 注意：这个方法只能在用户主动触发的事件中调用（如点击按钮）
 */
export function triggerShareAppMessage() {
	// 微信小程序不支持主动调用分享，只能通过button的open-type="share"或右上角菜单
	console.warn('微信小程序不支持主动调用分享，请使用button的open-type="share"');
}

/**
 * 检查是否支持分享到朋友圈
 * @returns {Promise<boolean>} 是否支持分享到朋友圈
 */
export function checkShareTimelineSupport() {
	return new Promise((resolve) => {
		// 检查微信版本是否支持分享到朋友圈
		uni.getSystemInfo({
			success: (res) => {
				// 微信版本 7.0.12 及以上支持分享到朋友圈
				const version = res.version;
				const versionArray = version.split('.');
				const majorVersion = parseInt(versionArray[0]);
				const minorVersion = parseInt(versionArray[1]);
				const patchVersion = parseInt(versionArray[2]);
				
				const isSupported = majorVersion > 7 || 
					(majorVersion === 7 && minorVersion > 0) ||
					(majorVersion === 7 && minorVersion === 0 && patchVersion >= 12);
				
				resolve(isSupported);
			},
			fail: () => {
				resolve(false);
			}
		});
	});
}

export default {
	getShareAppMessageConfig,
	getShareTimelineConfig,
	createShareMixin,
	triggerShareAppMessage,
	checkShareTimelineSupport
};
