<template>
	<view class="meal-list-container">
		<!-- 自定义导航栏 -->
		<custom-nav-bar title="我的陪护餐预约" showBack></custom-nav-bar>
		
		<!-- 日期筛选 -->
		<view class="filter-section">
			<view class="date-filter">
				<text class="filter-label">日期筛选：</text>
				<picker mode="date" @change="dateFilterChange" :value="filterDate">
					<view class="picker-value">{{ filterDate || '全部' }}</view>
				</picker>
				<view class="clear-filter" v-if="filterDate" @click="clearDateFilter">
					<text class="clear-text">清除</text>
				</view>
			</view>
		</view>
		
		<!-- 下拉刷新提示 -->
		<view class="refresh-tip" v-if="isRefreshing">
			<text class="refresh-text">正在刷新...</text>
		</view>
		
		<!-- 无数据提示 -->
		<view class="empty-container" v-if="reservations.length === 0">
			<image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image>
			<text class="empty-text">暂无陪护餐预约记录</text>
			<button class="add-btn" @click="goToReservation">立即预约</button>
		</view>
		
		<!-- 预约列表 -->
		<view class="reservation-list" v-else>
			<view class="reservation-item" v-for="(item, index) in reservations" :key="index">
				<view class="reservation-header">
					<text class="reservation-date">{{ item.reservationDate }}</text>
					<view class="header-right">
						<view class="action-buttons" v-if="canEdit(item)">
							<view class="action-btn edit-btn" @click="editReservation(item)">
								<text class="btn-text">编辑</text>
							</view>
							<view class="action-btn delete-btn" @click="deleteReservation(item)">
								<text class="btn-text">删除</text>
							</view>
						</view>
						<!-- <text class="reservation-status" :class="getStatusClass(item)">{{ getStatusText(item) }}</text> -->
					</view>
				</view>
				<view class="reservation-content">
					<view class="info-row">
						<text class="info-label">宝妈姓名：</text>
						<text class="info-value">{{ item.userName || '未填写' }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">房间号：</text>
						<text class="info-value">{{ item.roomNumber || '未填写' }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">用餐时段：</text>
						<text class="info-value">{{ getMealTimeText(item.mealTime) }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">用餐份数：</text>
						<text class="info-value">{{ item.quantity }}份</text>
					</view>
					<view class="info-row">
						<text class="info-label">预约时间：</text>
						<text class="info-value">{{ formatDateTime(item.createTime) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-btn-container" v-if="reservations.length > 0">
			<button class="add-more-btn" @click="goToReservation">前往预约</button>
		</view>
	</view>
</template>

<script>
// 导入meal_reservation模块中的API函数
import { getMealReservationInfo, deleteMealReservationInfo, updateMealReservationInfo } from '@/config/api/meal_reservation.js';
// 导入自定义导航栏组件
import CustomNavBar from '@/components/custom-nav-bar/custom-nav-bar.vue';
// 导入分享功能
import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';
// 也可以使用通用API模块（如果需要）
// import { API, request } from '../../../config/api.js';

export default {
	components: {
		CustomNavBar
	},
	data() {
		return {
			// 预约列表数据
			reservations: [],
			// 日期筛选
			filterDate: '',
			// 所有预约数据（用于筛选）
			allReservations: [],
			// 是否正在刷新
			isRefreshing: false
		}
	},
	
	onLoad() {
		// 检查登录状态
		this.checkLoginStatus()
	},
	
	onShow() {
		// 页面每次显示时检查登录状态
		this.checkLoginStatus();
	},
	
	// 页面下拉刷新处理函数
	onPullDownRefresh() {
		this.fetchReservations();
	},
	
	methods: {
		/**
		 * 检查登录状态
		 */
		checkLoginStatus() {
			const userInfo = uni.getStorageSync('userInfo');
			const token = uni.getStorageSync('token');

			if (!userInfo || !token) {
				// 未登录，跳转到登录页面
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});

				// 停止所有加载动画
				uni.hideLoading();
				uni.stopPullDownRefresh();

				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}, 1500);
				return false;
			}

			// 已登录，获取预约列表
			this.fetchReservations();
			return true;
		},
		
		// 格式化日期时间
		formatDateTime(dateTimeStr) {
			if (!dateTimeStr) return '';
			// 修复iOS日期格式兼容性问题，将连字符替换为斜杠
			const dateStr = dateTimeStr.replace(/-/g, '/');
			const date = new Date(dateStr);
			return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
		},
		
		// 用餐时段文本映射
		getMealTimeText(mealTime) {
			const mealTimeMap = {
				'breakfast': '早餐',
				'lunch': '午餐',
				'dinner': '晚餐',
				'Breakfast': '早餐',
				'Lunch': '午餐',
				'Dinner': '晚餐'
			};

			// 如果包含逗号，说明是多个时段，需要分别转换
			if (mealTime && mealTime.includes(',')) {
				const mealTimes = mealTime.split(',');
				return mealTimes.map(time => mealTimeMap[time.trim()] || time.trim()).join('、');
			}

			return mealTimeMap[mealTime] || mealTime;
		},
		
		// 获取状态样式类
		getStatusClass(item) {
			const now = new Date();
			// 修复iOS日期格式兼容性问题，将连字符替换为斜杠
			const reservationDateStr = item.reservationDate.replace(/-/g, '/');
			const reservationDate = new Date(reservationDateStr);

			// 设置两个日期的时间部分为相同，只比较日期
			reservationDate.setHours(0, 0, 0, 0);
			const today = new Date(now);
			today.setHours(0, 0, 0, 0);

			// 过期
			if (reservationDate < today) {
				return 'status-expired';
			}

			// 今日
			if (reservationDate.getTime() === today.getTime()) {
				// 判断具体时段是否已过
				const hour = now.getHours();
				const minute = now.getMinutes();

				// 处理多个用餐时段的情况
				const mealTimes = item.mealTime.includes(',') ? item.mealTime.split(',') : [item.mealTime];
				let hasAvailableTime = false;

				for (const mealTime of mealTimes) {
					const mealTimeLC = mealTime.trim().toLowerCase();

					// 检查每个时段是否还可用
					if (mealTimeLC === 'breakfast' && !(hour > 8 || (hour === 8 && minute > 30))) {
						hasAvailableTime = true;
						break;
					}

					if (mealTimeLC === 'lunch' && !(hour > 13 || (hour === 13 && minute > 30))) {
						hasAvailableTime = true;
						break;
					}

					if (mealTimeLC === 'dinner' && !(hour > 19 || (hour === 19 && minute > 30))) {
						hasAvailableTime = true;
						break;
					}
				}

				return hasAvailableTime ? 'status-today' : 'status-expired';
			}

			// 未来 (返回空，不显示蓝色的"即将到来"状态)
			return '';
		},
		
		// 获取状态文本
		getStatusText(item) {
			const statusClass = this.getStatusClass(item);
			
			if (statusClass === 'status-expired') {
				return '已过期';
			}
			
			if (statusClass === 'status-today') {
				return '今日可用';
			}
			
			// 即将到来的状态不显示文本
			return '';
		},
		
		// 判断是否可以编辑和删除预约
		canEdit(item) {
			const now = new Date();
			// 修复iOS日期格式兼容性问题，将连字符替换为斜杠
			const reservationDateStr = item.reservationDate.replace(/-/g, '/');
			const reservationDate = new Date(reservationDateStr);

			// 设置两个日期的时间部分为相同，只比较日期
			reservationDate.setHours(0, 0, 0, 0);
			const today = new Date(now);
			today.setHours(0, 0, 0, 0);

			// 如果是过期预约，不能编辑
			if (reservationDate < today) {
				return false;
			}

			// 如果是今天的预约，需要判断是否已超过截止时间
			if (reservationDate.getTime() === today.getTime()) {
				const hour = now.getHours();
				const minute = now.getMinutes();

				// 处理多个用餐时段的情况
				const mealTimes = item.mealTime.includes(',') ? item.mealTime.split(',') : [item.mealTime];

				// 只要有一个时段还没截止，就可以编辑
				for (const mealTime of mealTimes) {
					const mealTimeLC = mealTime.trim().toLowerCase();

					// 早餐 7:30 截止
					if (mealTimeLC === 'breakfast' && !(hour > 7 || (hour === 7 && minute >= 30))) {
						return true;
					}

					// 午餐 11:00 截止
					if (mealTimeLC === 'lunch' && hour < 11) {
						return true;
					}

					// 晚餐 16:30 截止
					if (mealTimeLC === 'dinner' && !(hour > 16 || (hour === 16 && minute >= 30))) {
						return true;
					}
				}

				// 所有时段都已截止
				return false;
			}

			return true;
		},
		
		// 日期筛选变更
		dateFilterChange(e) {
			this.filterDate = e.detail.value;
			this.filterReservations();
		},
		
		// 清除日期筛选
		clearDateFilter() {
			this.filterDate = '';
			this.filterReservations();
		},
		
		// 筛选预约列表
		filterReservations() {
			if (!this.filterDate) {
				// 如果没有筛选日期，显示所有预约
				this.reservations = [...this.allReservations];
			} else {
				// 根据日期筛选
				this.reservations = this.allReservations.filter(item => 
					item.reservationDate === this.filterDate
				);
			}
		},
		
		// 获取预约列表
		async fetchReservations() {
			try {
				this.isRefreshing = true;
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				
				const res = await getMealReservationInfo();
				
				// 检查不同的数据格式
				let resData = null;
				if (res && res.data) {
					resData = res.data;
				} else if (res && res.rows) {
					resData = res.rows;
				} else if (Array.isArray(res)) {
					resData = res;
				} else if (res && typeof res === 'object') {
					// 尝试找到数组类型的字段
					for (const key in res) {
						if (Array.isArray(res[key])) {
							resData = res[key];
							break;
						}
					}
				}
				
				// 如果找到了数据
				if (resData && resData.length > 0) {
					// 处理和转换数据
					const processedData = resData.map(item => {
						// 创建基本对象
						const processedItem = {
							id: item.id,
							mealTime: item.mealTime || item.meal_time || 'breakfast',
							quantity: item.quantity || 1,
							createTime: item.createTime || item.create_time || new Date().toISOString()
						};
						
						// 处理日期 - 确保格式为 YYYY-MM-DD
						if (item.reservationDate) {
							processedItem.reservationDate = item.reservationDate;
						} else if (item.reservation_date) {
							processedItem.reservationDate = item.reservation_date;
						} else {
							// 如果没有日期，使用今天
							const today = new Date();
							processedItem.reservationDate = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
						}
						
						// 处理用户名 - 增加对更多可能字段的支持
						processedItem.userName = item.userName || item.user_name || item.motherName || item.mother_name || item.name || item.creator || '';
						
						// 处理房间号
						processedItem.roomNumber = item.roomNumber || item.room_number || '';
						
						return processedItem;
					});
					
					// 按日期倒序排序，最近的日期排在前面
					this.allReservations = processedData.sort((a, b) => {
						// 修复iOS日期格式兼容性问题，将连字符替换为斜杠
						const dateA = new Date(a.reservationDate.replace(/-/g, '/'));
						const dateB = new Date(b.reservationDate.replace(/-/g, '/'));
						return dateB - dateA;
					});
					
					this.filterReservations();
					
					// 如果数据为空，显示提示
					if (this.reservations.length === 0) {
						uni.showToast({
							title: '暂无符合条件的预约',
							icon: 'none'
						});
					}
				} else {
					// 如果没有找到数据，显示一个提示
					uni.showToast({
						title: '没有找到预约记录',
						icon: 'none'
					});
				}
				
				uni.hideLoading();
				this.isRefreshing = false;
				// 如果是下拉刷新，停止下拉刷新动画
				uni.stopPullDownRefresh();
			} catch (error) {
				uni.hideLoading();
				this.isRefreshing = false;
				uni.stopPullDownRefresh();
				console.error('获取预约列表失败:', error);
				uni.showToast({
					title: '获取预约列表失败',
					icon: 'none'
				});
			}
		},
		
		// 编辑预约
		// 编辑预约
		editReservation(item) {
		// 跳转到编辑页面，并传递预约信息
		uni.navigateTo({
			url: `/pages_business/pages/enroll/meal?id=${item.id}&edit=true`
		});
		},
		
		// 删除预约
		deleteReservation(item) {
			uni.showModal({
				title: '提示',
				content: `确定要删除${item.reservationDate} ${this.getMealTimeText(item.mealTime)}的预约吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({
								title: '删除中...',
								mask: true
							});
							
							const params = {
								id: item.id
							};
							
							await deleteMealReservationInfo(params);
							
							uni.hideLoading();
							uni.showToast({
								title: '删除预约成功',
								icon: 'success'
							});
							
							// 重新获取预约列表
							this.fetchReservations();
						} catch (error) {
							uni.hideLoading();
							console.error('删除预约失败:', error);
							uni.showToast({
								title: '删除预约失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 跳转到预约页面
		goToReservation() {
			uni.navigateTo({
				url: '/pages_business/pages/enroll/meal'
			});
		}
	},
	// 分享给朋友
	onShareAppMessage() {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 营养月子餐',
			path: '/pages_business/pages/food/food' // 跳转到营养餐食页面
		});
	},
	// 分享到朋友圈
	onShareTimeline() {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所营养月子餐，科学搭配助力产后恢复'
		});
	}
}
</script>

<style lang="scss" scoped>
	.meal-list-container {
		padding: 0 20rpx 120rpx;
		background-color: #FAFAF5;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}
	
	.header {
		text-align: center;
		margin-bottom: 40rpx;
		
		.header-title {
			font-size: 36rpx;
			color: #8b5a2b;
			font-weight: 600;
		}
	}
	
	.refresh-tip {
		text-align: center;
		padding: 20rpx 0;
		
		.refresh-text {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.filter-section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 20rpx 30rpx;
		margin: 20rpx 0 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.date-filter {
			display: flex;
			align-items: center;
			
			.filter-label {
				font-size: 28rpx;
				color: #666;
				margin-right: 20rpx;
			}
			
			.picker-value {
				font-size: 28rpx;
				color: #333;
				padding: 10rpx 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				flex: 1;
			}
			
			.clear-filter {
				margin-left: 20rpx;
				
				.clear-text {
					font-size: 24rpx;
					color: #8b5a2b;
				}
			}
		}
	}
	
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		
		.empty-image {
			width: 600rpx;
			height: 600rpx;
		}
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 40rpx;
		}
		
		.add-btn {
			width: 300rpx;
			height: 80rpx;
			line-height: 80rpx;
			background-color: #8b5a2b;
			color: #fff;
			font-size: 28rpx;
			border-radius: 40rpx;
		}
	}
	
	.reservation-list {
		margin-bottom: 30rpx;
		
		.reservation-item {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			.reservation-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid #f0f0f0;
				padding-bottom: 20rpx;
				margin-bottom: 20rpx;
				
				.reservation-date {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
				}
				
				.header-right {
					display: flex;
					align-items: center;
					
					.action-buttons {
						display: flex;
						
						.action-btn {
							margin-left: 10rpx;
							
							.btn-text {
								font-size: 24rpx;
								color: #8b5a2b;
							}
						}
					}
					
					.reservation-status {
						font-size: 24rpx;
						padding: 6rpx 16rpx;
						border-radius: 20rpx;
						
						&.status-expired {
							color: #999;
							background-color: #f5f5f5;
						}
						
						&.status-today {
							color: #fff;
							background-color: #67c23a;
						}
					}
				}
			}
			
			.reservation-content {
				margin-bottom: 20rpx;
				
				.info-row {
					display: flex;
					margin-bottom: 16rpx;
					
					.info-label {
						width: 180rpx;
						font-size: 28rpx;
						color: #666;
					}
					
					.info-value {
						flex: 1;
						font-size: 28rpx;
						color: #333;
					}
				}
			}
		}
	}
	
	.bottom-btn-container {
		position: fixed;
		bottom: 30rpx;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		padding: 0 30rpx;
		box-sizing: border-box;
		
		.add-more-btn {
			width: 80%;
			height: 80rpx;
			line-height: 80rpx;
			background-color: #8b5a2b;
			color: #fff;
			font-size: 28rpx;
			border-radius: 40rpx;
		}
	}
</style>