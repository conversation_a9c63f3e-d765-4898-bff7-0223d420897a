<template>
  <div class="kitchen-container" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 隐藏的全屏按钮，用于自动触发 -->
    <button ref="hiddenFullscreenBtn" @click="toggleFullscreen" style="display: none;">隐藏全屏按钮</button>

    <!-- 全屏控制按钮 -->
    <div class="fullscreen-control" v-if="!route.query.hideControls">
      <button @click="toggleFullscreen" class="fullscreen-btn">
        {{ isFullscreen ? '退出全屏' : '进入全屏' }}
      </button>
    </div>

    <div class="kitchen-board page-demo1">
      <div class="section escort-meals">
        <div class="head-title">
          <h2 class="h2-none">陪护餐预约</h2>
          <div class="filter-content">
            <div class="filter-item">
              <div class="label">预约日期:</div>
              <div class="content">
                {{ getTodayDate()  }}
              </div>
            </div>
            <div class="filter-item">
              <div class="label">用餐时段:</div>
              <div class="content">
                {{ getCurrentMealName() }}
              </div>
            </div>

          </div>
        </div>

        <div class="grid-container" ref="mealContainer">
          <!-- 有数据时显示餐食卡片 -->
          <div class="meal-card" :class="item.currentMeal" v-for="(item, index) in mealList" :key="index" v-if="mealList.length > 0">
            <div class="meal-header">
              <div class="room-number-large">{{ item.roomNumber }}</div>
              <div class="meal-right-info">
                <div class="meal-time-large">{{ item.currentMealName }}</div>
                <div class="quantity-display">×{{ item.quantity }}</div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div class="empty-state" v-if="mealList.length === 0">
            <div class="empty-icon">🍽️</div>
            <div class="empty-title">暂无陪护餐预约</div>
            <div class="empty-description">当前时段没有陪护餐预约信息</div>
          </div>
        </div>
        <div class="summary">
          <div class="summary-item">
            早餐:{{ breakfastTotal }}
          </div>
          <div class="summary-item">
            午餐:{{ lunchTotal }}
          </div>
          <div class="summary-item">
            晚餐:{{ dinnerTotal }}
          </div>
        </div>
      </div>

      <div class="section dietary-restrictions">
        <h2 style="min-height: 44px;">客户忌口信息</h2>
        <div class="restriction-list" ref="restrictionContainer">
          <!-- 有数据时显示忌口信息 -->
          <div class="restriction-row-full" v-for="(item, index) in userAllergyList" :key="index" v-if="userAllergyList.length > 0">
            <div class="room-number-large">{{ item.roomNumber }}房</div>
            <div class="restriction-info">{{ item.allergyDetail }}</div>
          </div>

          <!-- 空状态 -->
          <div class="empty-state restriction-empty" v-if="userAllergyList.length === 0">
            <div class="empty-icon">🚫</div>
            <div class="empty-title">暂无忌口信息</div>
            <div class="empty-description">当前没有客户忌口信息记录</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, getCurrentInstance, toRaw } from 'vue'
import { allListMealReservation, updateMealReservation } from "@/api/business/mealReservation";
import { listAllergy ,boardListAllergy} from "@/api/business/allergy";
import { useRoute } from 'vue-router'




// 获取路由
const route = useRoute()

// 全屏状态和隐藏按钮引用
const isFullscreen = ref(false)
const hiddenFullscreenBtn = ref(null)

// 当前页面索引 (0: demo1, 1: demo2)
const currentPage = ref(0)
let pageTimer = null
let refreshTimer = null
const mealList = reactive([])
// const breakfastMeals = reactive([])
// const lunchMeals = reactive([])
// const dinnerMeals = reactive([])
const breakfastTotal = ref(0)
const lunchTotal = ref(0)
const dinnerTotal = ref(0)
const userAllergyList = reactive([])
const currentTime = ref(0)

// 获取今天的日期，返回 YYYY-MM-DD 格式
const getTodayDate = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 判断当前时间应该显示哪个餐次
const getCurrentMealType = () => {
  const now = new Date()
  const hour = now.getHours()

  if (hour >= 8 && hour < 13) {
    // 8am-1pm 显示中餐
    return 'lunch'
  } else if (hour >= 13 && hour < 19) {
    // 1pm-7pm 显示晚餐
    return 'dinner'
  } else {
    // 7pm-8am 显示早餐
    return 'breakfast'
  }
}

// 获取当前餐次的中文名称
const getCurrentMealName = () => {
  const mealType = getCurrentMealType()
  const mealNames = {
    'breakfast': '早餐',
    'lunch': '中餐',
    'dinner': '晚餐'
  }
  return mealNames[mealType]
}

const queryParams = reactive({
  reservationDate: getTodayDate(),
  mealTime: ["breakfast", "lunch", "dinner"],
})


const { proxy } = getCurrentInstance();

const getMealReservation = () => {
  const queryData = { reservationDate: queryParams.reservationDate }

  mealList.length = 0
  // breakfastMeals.length = 0
  // lunchMeals.length = 0
  // dinnerMeals.length = 0

  allListMealReservation(queryData).then(response => {
    breakfastTotal.value = 0
    lunchTotal.value = 0
    dinnerTotal.value = 0

    for (let i = 0; i < response.length; i++) {
      const meals = response[i].mealTime.split(',')
      for (let j = 0; j < meals.length; j++) {
        const list = {
          ...response[i],
          currentMeal: meals[j],
          currentMealName: meals[j] === 'breakfast' ? '早' : meals[j] === 'lunch' ? '午' : '晚',
        }
        // 

        const currentMealTime = getCurrentMealType()

        
        if(list.currentMeal === currentMealTime){
          mealList.push(list)
        }

        switch (meals[j]) {
          case 'breakfast':
              breakfastTotal.value += list.quantity
            break;
          case 'lunch':
              lunchTotal.value += list.quantity
            break;
          case 'dinner':
              dinnerTotal.value += list.quantity
            break;
        }




      }

    }


    // mealReservationList.value = response.rows;
    // total.value = parseInt(response.total) || 0;
    // loading.value = false;
  });
}

const getUserAllergy = () => {
  boardListAllergy({}).then(response => {
    
    Object.assign(userAllergyList, response)
  });
}

const removeMealReservation = (rows) => {
  proxy.$modal.confirm('是否完成?').then(function () {
    const params = toRaw(rows)
    params.mealTime = params.mealTime.split(',').filter(item => item !== params.currentMeal).join(',');
    delete params.currentMeal
    delete params.currentMealName
    return updateMealReservation(params);
  }).then(() => {
    getMealReservation();
    proxy.$modal.msgSuccess("操作成功");
  }).catch(() => { });
}

const handleQuery = () => {
  getMealReservation();
}

// 页面切换逻辑
const switchPage = () => {
  if (currentPage === 1 && currentTime.value !== 4) {
    currentTime.value += 1
    return
  }
  if (currentTime.value === 4) currentTime.value += 0
  currentPage.value = (currentPage.value + 1) % 2
}

// 启动定时器
const startTimer = () => {
  pageTimer = setInterval(switchPage, 60000) // 每1分钟切换一次
}

// 停止定时器
const stopTimer = () => {
  if (pageTimer) {
    clearInterval(pageTimer)
    pageTimer = null
  }
}

// 启动定时刷新（10分钟）
const startRefreshTimer = () => {
  refreshTimer = setInterval(() => {
    getMealReservation()
    getUserAllergy()
  }, 10 * 60 * 1000) // 10分钟 = 660000毫秒
}

// 停止定时刷新
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 进入全屏
const enterFullscreen = () => {
  const element = document.documentElement
  if (element.requestFullscreen) {
    element.requestFullscreen()
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen()
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen()
  }
}

// 退出全屏
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen()
  }
}

// 切换全屏状态
const toggleFullscreen = () => {
  if (isFullscreen.value) {
    exitFullscreen()
  } else {
    enterFullscreen()
  }
}



// 滚动相关变量
const restrictionContainer = ref(null)
const mealContainer = ref(null)
const breakfastContainer = ref(null)
const lunchContainer = ref(null)
const dinnerContainer = ref(null)
let scrollTimer = null
let mealScrollTimer = null
let breakfastScrollTimer = null
let lunchScrollTimer = null
let dinnerScrollTimer = null
let currentScrollPosition = 0
let currentMealScrollPosition = 0
let currentBreakfastScrollPosition = 0
let currentLunchScrollPosition = 0
let currentDinnerScrollPosition = 0

// 启动滚动功能
const startScrolling = () => {
  if (!restrictionContainer.value) return

  scrollTimer = setInterval(() => {
    const container = restrictionContainer.value
    const containerHeight = container.clientHeight
    const scrollHeight = container.scrollHeight

    // 如果内容高度大于容器高度，才需要滚动
    if (scrollHeight > containerHeight) {
      currentScrollPosition += 1 // 每次滚动1像素

      // 如果滚动到底部，重置到顶部
      if (currentScrollPosition >= scrollHeight - containerHeight) {
        currentScrollPosition = 0
      }

      container.scrollTop = currentScrollPosition
    }
  }, 50) // 每50毫秒滚动一次，实现平滑滚动
}

// 停止滚动
const stopScrolling = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }
}

// 启动陪护餐预约模块滚动功能
const startMealScrolling = () => {
  if (!mealContainer.value) return

  mealScrollTimer = setInterval(() => {
    const container = mealContainer.value
    const containerHeight = container.clientHeight
    const scrollHeight = container.scrollHeight

    // 如果内容高度大于容器高度，才需要滚动
    if (scrollHeight > containerHeight) {
      currentMealScrollPosition += 1 // 每次滚动1像素

      // 如果滚动到底部，重置到顶部
      if (currentMealScrollPosition >= scrollHeight - containerHeight) {
        currentMealScrollPosition = 0
      }

      container.scrollTop = currentMealScrollPosition
    }
  }, 50) // 每50毫秒滚动一次，实现平滑滚动
}

// 停止陪护餐预约模块滚动
const stopMealScrolling = () => {
  if (mealScrollTimer) {
    clearInterval(mealScrollTimer)
    mealScrollTimer = null
  }
}

// 水平滚动函数
const startHorizontalScrolling = (mealType) => {
  const containerMap = {
    breakfast: breakfastContainer,
    lunch: lunchContainer,
    dinner: dinnerContainer
  }

  const container = containerMap[mealType]?.value
  if (!container) return

  // 清除现有定时器
  if (mealType === 'breakfast' && breakfastScrollTimer) {
    clearInterval(breakfastScrollTimer)
  } else if (mealType === 'lunch' && lunchScrollTimer) {
    clearInterval(lunchScrollTimer)
  } else if (mealType === 'dinner' && dinnerScrollTimer) {
    clearInterval(dinnerScrollTimer)
  }

  const scrollFunction = () => {
    const containerWidth = container.clientWidth
    const scrollWidth = container.scrollWidth

    // 如果内容宽度大于容器宽度，才需要滚动
    if (scrollWidth > containerWidth) {
      // 获取当前滚动位置
      let currentPos = 0
      if (mealType === 'breakfast') {
        currentPos = currentBreakfastScrollPosition
      } else if (mealType === 'lunch') {
        currentPos = currentLunchScrollPosition
      } else if (mealType === 'dinner') {
        currentPos = currentDinnerScrollPosition
      }

      currentPos += 1 // 每次滚动1像素

      // 如果滚动到右边界，重置到左边
      if (currentPos >= scrollWidth - containerWidth) {
        currentPos = 0
      }

      container.scrollLeft = currentPos

      // 更新位置变量
      if (mealType === 'breakfast') {
        currentBreakfastScrollPosition = currentPos
      } else if (mealType === 'lunch') {
        currentLunchScrollPosition = currentPos
      } else if (mealType === 'dinner') {
        currentDinnerScrollPosition = currentPos
      }
    }
  }

  // 设置定时器
  if (mealType === 'breakfast') {
    breakfastScrollTimer = setInterval(scrollFunction, 30)
  } else if (mealType === 'lunch') {
    lunchScrollTimer = setInterval(scrollFunction, 30)
  } else if (mealType === 'dinner') {
    dinnerScrollTimer = setInterval(scrollFunction, 30)
  }
}

// 停止水平滚动
const stopHorizontalScrolling = (mealType) => {
  if (mealType === 'breakfast' && breakfastScrollTimer) {
    clearInterval(breakfastScrollTimer)
    breakfastScrollTimer = null
  } else if (mealType === 'lunch' && lunchScrollTimer) {
    clearInterval(lunchScrollTimer)
    lunchScrollTimer = null
  } else if (mealType === 'dinner' && dinnerScrollTimer) {
    clearInterval(dinnerScrollTimer)
    dinnerScrollTimer = null
  }
}

// 启动所有水平滚动
const startAllHorizontalScrolling = () => {
  setTimeout(() => {
    startHorizontalScrolling('breakfast')
    startHorizontalScrolling('lunch')
    startHorizontalScrolling('dinner')
  }, 1000) // 延迟启动，确保DOM已渲染
}

// 停止所有水平滚动
const stopAllHorizontalScrolling = () => {
  stopHorizontalScrolling('breakfast')
  stopHorizontalScrolling('lunch')
  stopHorizontalScrolling('dinner')
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const isInFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  )

  if (!isInFullscreen && isFullscreen.value) {
    // 如果退出了API全屏但状态还是true，说明是CSS模拟的全屏
    return
  }

  isFullscreen.value = isInFullscreen
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'F11') {
    event.preventDefault()
    // F11被按下时，给用户提示
    console.log('检测到F11按键，建议使用浏览器原生F11功能获得最佳体验')
  } else if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen()
  }
}

// 处理URL参数
const handleUrlParams = () => {
  const params = route.query
  if (params.autoFullscreen === 'true') {
    // 延迟进入全屏，确保页面已完全加载
    setTimeout(() => {
      enterFullscreen()
    }, 1000)
  }
}


// 生命周期钩子
onMounted(() => {
  startTimer()
  startRefreshTimer() // 启动定时刷新
  getMealReservation()
  getUserAllergy()
  handleUrlParams() // 处理URL参数

  // 添加全屏状态监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 延迟启动滚动，确保DOM已渲染
  setTimeout(() => {
    startScrolling()
    startMealScrolling()
    startAllHorizontalScrolling()
  }, 1000)
})

onUnmounted(() => {
  stopTimer()
  stopRefreshTimer() // 停止定时刷新
  stopScrolling()
  stopMealScrolling()
  stopAllHorizontalScrolling()

  // 移除全屏状态监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.kitchen-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 5.3125rem); /* 默认高度，减去导航栏 */
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 全屏模式样式 */
.kitchen-container.fullscreen-mode {
  height: 100vh; /* 全屏时占满整个视口 */
}

/* 全屏控制按钮样式 */
.fullscreen-control {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.fullscreen-btn {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.fullscreen-btn:hover {
  background: rgba(0, 0, 0, 0.9);
}

.kitchen-board {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  transition: opacity 0.5s ease-in-out;
}

/* Demo1 样式 */
.page-demo1 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 2fr;
  gap: 1.25rem;
  padding: 1.25rem;
}

.section {
  background: white;
  border-radius: .625rem;
  padding: 1.25rem;
  box-shadow: 0 .125rem .625rem rgba(0, 0, 0, 0.1);
}

.escort-meals {
  grid-column: 1;
  grid-row: 1;
  overflow-y: auto;
  position: relative;
}

.dietary-restrictions {
  grid-column: 2;
  grid-row: 1;
  overflow-y: auto;
}

.tonic-schedule {
  grid-column: 1 / span 2;
  grid-row: 2;
  overflow-y: auto;
}

h2 {
  color: #1a1a1a;
  margin-bottom: 1.25rem;
  border-bottom: .125rem solid #e8e8e8;
  padding-bottom: .625rem;
}

.h2-none {
  margin-bottom: 0;
  border-bottom: 0;
  padding-bottom: 0;
}

.grid-container {
  /* display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, 1fr); */
  display: flex;
  flex-wrap: wrap;
  gap: .9375rem;
  padding: .625rem;
  height: calc(100% - 174px);
  max-height: calc(100% - 174px);
  overflow: hidden;
  scroll-behavior: smooth;
}

.meal-card {
  width: calc((100% - 2rem) / 3);
  height: calc((100% - 3rem) / 4);
}

/* 大屏适配 (2K及以上) */
@media screen and (min-width: 2560px) {
  .grid-container {
    height: calc(100% - 200px);
    max-height: calc(100% - 200px);
    gap: 1.2rem;
    padding: .8rem;
  }
}


/* 标准屏幕适配 (1366px-1599px) */
@media screen and (min-width: 1366px) and (max-width: 1599px) {
  .grid-container {
    height: calc(100% - 160px);
    max-height: calc(100% - 160px);
    gap: .7rem;
    padding: .5rem;
  }
}

/* 小屏适配 (1024px-1365px) */
@media screen and (min-width: 1024px) and (max-width: 1365px) {
  .grid-container {
    height: calc(100% - 150px);
    max-height: calc(100% - 150px);
    gap: .6rem;
    padding: .4rem;
  }
}

/* 平板适配 (768px-1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .grid-container {
    height: calc(100% - 140px);
    max-height: calc(100% - 140px);
    gap: .5rem;
    padding: .3rem;
  }
}

/* 小平板/大手机适配 (480px-767px) */
@media screen and (min-width: 480px) and (max-width: 767px) {
  .grid-container {
    height: calc(100% - 130px);
    max-height: calc(100% - 130px);
    gap: .4rem;
    padding: .2rem;
  }
}

/* 手机适配 (最小480px) */
@media screen and (max-width: 479px) {
  .grid-container {
    height: calc(100% - 120px);
    max-height: calc(100% - 120px);
    gap: .3rem;
    padding: .1rem;
  }
}

.restriction-list {
  display: flex;
  flex-direction: column;
  gap: .75rem;
  padding: .625rem;
  height: 90%;
  overflow: hidden;
  scroll-behavior: smooth;
}

.meal-card {
  border-radius: .5rem;
  padding: .9375rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
}

.breakfast {
  background-color: #C72C41;
}

.lunch {
  background-color: #E6AF2E;
}

.dinner {
  background-color: #3A6EA5;
}

.meal-header {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.room-number-large {
  color: white;
  font-weight: bold;
  font-size: 6em;
  text-align: left;
}


.meal-right-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

/* 空状态样式 */
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 300px;
  color: #999;
  text-align: center;
  padding: 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #666;
}

.empty-description {
  font-size: 1rem;
  color: #999;
  line-height: 1.5;
}

/* 忌口信息空状态特殊样式 */
.restriction-empty {
  min-height: 200px;
  grid-column: unset;
}

.meal-time-large {
  font-size: 3.5em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

.quantity-display {
  font-size: 3.5em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

.delete-button {
  position: absolute;
  top: -0.625rem;
  right: -0.625rem;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 3.125rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
}

.delete-button:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.restriction-row-full {
  background: #f8f9fa;
  border: .0625rem solid #e8e8e8;
  border-radius: .5rem;
  padding: 1.25rem;
  margin-bottom: .9375rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.restriction-row-full .room-number-large {
  color: #1890ff;
  font-weight: bold;
  font-size: 3em;
  text-align: left;
  margin-bottom: .9375rem;
}

.restriction-info {
  font-size: 2em;
  color: #666;
  text-align: left;
  line-height: 1.4;
}

.tonic-card {
  background: #1890ff;
  border-radius: .5rem;
  padding: .9375rem;
  height: 10rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
}

.tonic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: .625rem;
  font-size: 24px;
}

.tonic-card .room-number {
  color: white;
  font-weight: bold;
  font-size: 1.8em;
}

.tonic-name {
  font-size: 1.8em;
  font-weight: bold;
  color: white;
}

.tonic-time {
  font-size: 2.3em;
  color: #90EE90;
  font-weight: bold;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: .5rem;
  border-radius: .25rem;
}

/* Demo2 样式 */
.page-demo2 {
  padding: 1.25rem;
  box-sizing: border-box;
}

.meals-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 7.5rem - 6.5625rem);
}

.meal-section {
  /* background: white; */
  border-radius: .625rem;
  padding: 1.25rem;
  /* box-shadow: 0 .125rem .625rem rgba(0,0,0,0.1); */
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.meal-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 1.875rem;
  border-radius: .75rem;
  background: #f8f9fa;
  border: .125rem solid #e8e8e8;
  flex-shrink: 0;
}

.meal-summary.breakfast {
  border-left: .5rem solid #C72C41;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffeef1 100%);
}

.meal-summary.lunch {
  border-left: .5rem solid #E6AF2E;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff9e6 100%);
}

.meal-summary.dinner {
  border-left: .5rem solid #3A6EA5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
}

.meal-summary-content {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  font-size: 144px;
  margin-right: 1.25rem;
}

.meal-time-text {
  font-weight: bold;
  color: #333;
  text-shadow: .125rem .125rem .25rem rgba(0, 0, 0, 0.1);
}

.meal-count {
  font-weight: bold;
  color: #666;
  text-shadow: .125rem .125rem .25rem rgba(0, 0, 0, 0.1);
}

.meal-cards-container {
  display: flex;
  gap: .9375rem;
  flex: 1;
  overflow-x: hidden;
  /* 隐藏滚动条但保持滚动功能 */
  padding: .625rem 0;
  height: 100%;
  scroll-behavior: smooth;
  white-space: nowrap;
  /* 防止换行 */
  position: relative;
}

/* 隐藏滚动条但保持滚动功能 */
.meal-cards-container::-webkit-scrollbar {
  display: none;
}

.meal-cards-container {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.meal-card-demo2 {
  border-radius: .75rem;
  padding: 1.25rem;
  min-width: 15rem;
  /* 最小宽度 */
  height: 100%;
  display: flex;
  align-items: center;
  color: white;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, 0.15);
  white-space: normal;
  /* 允许卡片内容正常换行 */
}

.meal-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.meal-card-demo2 .room-number-large {
  color: white;
  font-weight: bold;
  font-size: 7em;
  text-align: left;
  text-shadow: .125rem .125rem .25rem rgba(0, 0, 0, 0.3);
}

.meal-card-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.meal-card-demo2 .meal-time-large {
  font-size: 4em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  margin-bottom: .5rem;
  text-shadow: .0625rem .0625rem .125rem rgba(0, 0, 0, 0.3);
}

.meal-card-demo2 .quantity-display {
  font-size: 4em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  text-shadow: .0625rem .0625rem .125rem rgba(0, 0, 0, 0.3);
}

.meal-card-demo2 .delete-button {
  position: absolute;
  top: -0.625rem;
  right: -0.625rem;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 2.8125rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 .125rem .375rem rgba(0, 0, 0, 0.2);
}

.meal-card-demo2 .delete-button:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.15);
  box-shadow: 0 .25rem .75rem rgba(255, 0, 0, 0.3);
}

.summary {
  width: 100%;
  font-size: 56px;
  color: #1890ff;
  background-color: #fff;
  font-weight: bold;

  display: flex;
  justify-content: flex-end;

  position: absolute;
  padding: 1.25rem;
  right: 0;
  bottom: 0;
}

.summary .summary-item {
  margin-left: 1.25rem;
}

.day-title {
  position: absolute;
  right: 30px;
  top: 20px;
  border-bottom: none;
}

.head-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  border-bottom: .125rem solid #e8e8e8;
  padding-bottom: .625rem;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.5rem;
  font-weight:500;

}

.filter-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.filter-item .label {
  margin-right: 8px;
}

.meal-section {
  height: calc(100% / 3);
}



/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .meal-card-demo2 {
    min-width: 14rem;
  }

  .meal-card-demo2 .room-number-large {
    font-size: 6em;
  }

  .meal-card-demo2 .meal-time-large,
  .meal-card-demo2 .quantity-display {
    font-size: 3.5em;
  }
}

@media screen and (max-width: 1600px) {
  .meal-card-demo2 {
    min-width: 12rem;
    padding: 1rem;
  }

  .meal-card-demo2 .room-number-large {
    font-size: 5em;
  }

  .meal-card-demo2 .meal-time-large,
  .meal-card-demo2 .quantity-display {
    font-size: 3em;
  }

  .meal-summary-content {
    font-size: 120px;
  }
}

@media screen and (max-width: 1366px) {
  .meal-card-demo2 {
    min-width: 10rem;
    padding: .8rem;
  }

  .meal-card-demo2 .room-number-large {
    font-size: 4em;
  }

  .meal-card-demo2 .meal-time-large,
  .meal-card-demo2 .quantity-display {
    font-size: 2.5em;
  }

  .meal-summary-content {
    font-size: 100px;
  }
}

@media screen and (max-width: 1024px) {
  .meal-card-demo2 {
    min-width: 8rem;
    padding: .6rem;
  }

  .meal-card-demo2 .room-number-large {
    font-size: 3em;
  }

  .meal-card-demo2 .meal-time-large,
  .meal-card-demo2 .quantity-display {
    font-size: 2em;
  }

  .meal-summary-content {
    font-size: 80px;
  }

  .meal-section {
    padding: .8rem;
  }

  
}


/* 大屏字体适配 (2K及以上) */
@media screen and (min-width: 2560px) {
  .room-number-large {
    font-size: 7em;
  }
  .meal-time-large {
    font-size: 4em;
  }
  .quantity-display {
    font-size: 4em;
  }
  .meal-card {
    padding: 1.2rem;
  }
}

/* 标准大屏字体适配 (1920px-2560px) */
@media screen and (min-width: 1920px) and (max-width: 2559px) {
  .grid-container {
    height: calc(100% - 180px);
    max-height: calc(100% - 180px);
    gap: 1rem;
    padding: .7rem;
  }

  

  .room-number-large {
    font-size: 7.5em;
  }

  .restriction-row-full .room-number-large{
    font-size: 6.5em;
  }
  .meal-time-large {
    font-size: 4.5em;
  }
  .quantity-display {
    font-size: 4.5em;
  }
  .meal-card,.restriction-row-full {
    padding: 1rem;
  }

  .restriction-info{
    font-size: 4em;
  }
}

/* 中等屏幕字体适配 (1600px-1919px) */
@media screen and (min-width: 1600px) and (max-width: 1919px) {
  .grid-container {
    height: calc(100% - 170px);
    max-height: calc(100% - 170px);
    gap: .8rem;
    padding: .6rem;
  }

  .room-number-large {
    font-size: 5em;
  }
  .restriction-row-full .room-number-large{
    font-size: 4em;
  }
  .meal-time-large {
    font-size: 3em;
  }
  .quantity-display {
    font-size: 3em;
  }
  .meal-card,.restriction-row-full {
    padding: .9rem;
  }

  .restriction-info{
    font-size: 2.5em;
  }
}

/* 标准屏幕字体适配 (1366px-1599px) */
@media screen and (min-width: 1366px) and (max-width: 1599px) {
  .room-number-large {
    font-size: 4.2em;
  }
  .meal-time-large {
    font-size: 2.5em;
  }
  .quantity-display {
    font-size: 2.5em;
  }
  .meal-card {
    padding: .8rem;
  }
}

/* 小屏字体适配 (1024px-1365px) */
@media screen and (min-width: 1024px) and (max-width: 1365px) {
  .room-number-large {
    font-size: 3.5em;
  }
  .meal-time-large {
    font-size: 2em;
  }
  .quantity-display {
    font-size: 2em;
  }
  .meal-card {
    padding: .7rem;
  }
}

/* 空状态响应式适配 */
@media screen and (min-width: 2560px) {
  .empty-icon {
    font-size: 6rem;
  }
  .empty-title {
    font-size: 2rem;
  }
  .empty-description {
    font-size: 1.3rem;
  }
}

@media screen and (min-width: 1920px) and (max-width: 2559px) {
  .empty-icon {
    font-size: 5rem;
  }
  .empty-title {
    font-size: 1.8rem;
  }
  .empty-description {
    font-size: 1.2rem;
  }
}

@media screen and (max-width: 1366px) {
  .empty-icon {
    font-size: 3rem;
  }
  .empty-title {
    font-size: 1.3rem;
  }
  .empty-description {
    font-size: 0.9rem;
  }
  .empty-state {
    min-height: 200px;
    padding: 1.5rem;
  }
}

@media screen and (max-width: 1024px) {
  .empty-icon {
    font-size: 2.5rem;
  }
  .empty-title {
    font-size: 1.1rem;
  }
  .empty-description {
    font-size: 0.8rem;
  }
  .empty-state {
    min-height: 150px;
    padding: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .empty-icon {
    font-size: 2rem;
  }
  .empty-title {
    font-size: 1rem;
  }
  .empty-description {
    font-size: 0.7rem;
  }
  .empty-state {
    min-height: 120px;
    padding: 0.8rem;
  }
}

</style>