package com.dfab.businessLog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.businessLog.entity.BusinessLog;

import java.util.List;

/**
 * 业务关键日志 Service 接口。
 */
public interface BusinessLogService extends IService<BusinessLog> {

    /**
     * 根据 openId 获取业务日志列表
     * @param openId 微信 openId
     * @return 业务日志列表
     */
    List<BusinessLog> getBusinessLogListByOpenId(String openId);

    /**
     * 根据 openId 和类型获取业务日志列表
     * @param openId 微信 openId
     * @param type 日志类型
     * @return 业务日志列表
     */
    List<BusinessLog> getBusinessLogListByOpenIdAndType(String openId, String type);

    /**
     * 根据会员入住ID和类型获取业务日志列表
     * @param memberCheckinId 会员入住ID
     * @param type 日志类型
     * @return 业务日志列表
     */
    List<BusinessLog> getBusinessLogListByMemberCheckinIdAndType(Long memberCheckinId, String type);

    /**
     * 创建业务日志（小程序端）
     * @param businessLog 业务日志信息
     * @return 创建的业务日志
     */
    BusinessLog create(BusinessLog businessLog);

    /**
     * 创建业务日志（后台管理端）
     * @param businessLog 业务日志信息
     * @return 创建的业务日志
     */
    BusinessLog createByAdmin(BusinessLog businessLog);

    /**
     * 根据 openId 和 ID 删除业务日志
     * @param openId 微信 openId
     * @param id 日志 ID
     * @return 是否删除成功
     */
    Boolean removeByOpenIdAndId(String openId, Long id);

    /**
     * 根据 openId 和 ID 更新业务日志
     * @param businessLog 业务日志信息
     * @return 更新后的业务日志
     */
    BusinessLog updateByOpenIdAndId(BusinessLog businessLog);

    /**
     * 分页查询业务日志
     * @param businessLog 查询条件
     * @return 分页结果
     */
    IPage<BusinessLog> page(BusinessLog businessLog);

    /**
     * 查询业务日志列表
     * @param businessLog 查询条件
     * @return 业务日志列表
     */
    List<BusinessLog> list(BusinessLog businessLog);
}
