package com.dfab.material.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.material.entity.Inventory;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.entity.MaterialInfo;
import com.dfab.material.mapper.InventoryMapper;
import com.dfab.material.mapper.InventoryRecordMapper;
import com.dfab.material.mapper.MaterialInfoMapper;
import com.dfab.material.service.InventoryService;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存信息Service实现类
 */
@Service
@Slf4j
public class InventoryServiceImpl extends ServiceImpl<InventoryMapper, Inventory> implements InventoryService {

    @Autowired
    private MaterialInfoMapper materialInfoMapper;
    
    @Autowired
    private InventoryRecordMapper inventoryRecordMapper;

    /**
     * 查询库存信息列表（包含物料信息）
     * @return 库存信息列表
     */
    @Override
    public List<Map<String, Object>> getInventoryList() {
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 查询所有库存信息
        List<Inventory> inventoryList = this.list();
        
        // 遍历库存信息，关联物料信息
        for (Inventory inventory : inventoryList) {
            MaterialInfo materialInfo = materialInfoMapper.selectById(inventory.getMaterialId());
            if (materialInfo != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", inventory.getId());
                map.put("materialId", materialInfo.getId());
                map.put("materialName", materialInfo.getMaterialName());
                map.put("specification", materialInfo.getSpecification());
                map.put("unit", materialInfo.getUnit());
                map.put("unitPrice", materialInfo.getUnitPrice());
                map.put("quantity", inventory.getQuantity());
                resultList.add(map);
            }
        }
        
        return resultList;
    }

    /**
     * 分页查询库存信息列表（包含物料信息）
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页库存信息列表
     */
    @Override
    public Page<Map<String, Object>> getInventoryPage(int pageNum, int pageSize) {
        log.info("库存简单分页查询: pageNum={}, pageSize={}", pageNum, pageSize);

        // 为了实现按物料名称排序，我们需要获取所有库存数据，然后在内存中排序和分页
        // 这是因为库存表和物料表是分离的，无法直接在数据库层面按物料名称排序

        // 优化：先检查数据量，决定使用哪种查询策略
        long totalCount = this.count();
        log.info("库存总数: {}", totalCount);

        // 如果数据量很大（>5000条），建议使用数据库分页，但会失去完整的排序
        // 这里为了保证排序的正确性，我们设置一个合理的阈值
        if (totalCount > 10000) {
            log.warn("库存数据量较大({}条)，排序可能影响性能", totalCount);
        }

        // 查询所有库存信息（为了保证排序正确性）
        List<Inventory> allInventoryList = this.list();

        // 构建包含物料信息的完整列表
        List<Map<String, Object>> allResultList = new ArrayList<>();

        // 批量查询物料信息，提高性能
        Set<Long> materialIds = allInventoryList.stream()
                .map(Inventory::getMaterialId)
                .filter(Objects::nonNull)  // 过滤空值
                .collect(Collectors.toSet());

        if (materialIds.isEmpty()) {
            // 如果没有有效的物料ID，返回空结果
            Page<Map<String, Object>> emptyPage = new Page<>(pageNum, pageSize);
            emptyPage.setTotal(0);
            emptyPage.setRecords(new ArrayList<>());
            log.info("没有找到有效的物料ID，返回空结果");
            return emptyPage;
        }

        List<MaterialInfo> materialInfoList = materialInfoMapper.selectBatchIds(materialIds);
        Map<Long, MaterialInfo> materialInfoMap = materialInfoList.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getId(), item), HashMap::putAll);

        // 遍历库存信息，关联物料信息
        for (Inventory inventory : allInventoryList) {
            MaterialInfo materialInfo = materialInfoMap.get(inventory.getMaterialId());
            if (materialInfo != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", inventory.getId());
                map.put("materialId", materialInfo.getId());
                map.put("materialName", materialInfo.getMaterialName());
                map.put("specification", materialInfo.getSpecification());
                map.put("unit", materialInfo.getUnit());
                map.put("unitPrice", materialInfo.getUnitPrice());
                map.put("quantity", inventory.getQuantity());
                allResultList.add(map);
            }
        }

        // 根据物料名称排序（升序）
        allResultList.sort((a, b) -> {
            String nameA = (String) a.get("materialName");
            String nameB = (String) b.get("materialName");
            if (nameA == null && nameB == null) return 0;
            if (nameA == null) return 1;
            if (nameB == null) return -1;
            return nameA.compareTo(nameB);
        });

        // 手动分页
        int total = allResultList.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<Map<String, Object>> pageResultList = new ArrayList<>();
        if (startIndex < total) {
            pageResultList = allResultList.subList(startIndex, endIndex);
        }

        // 构建分页结果
        Page<Map<String, Object>> resultPage = new Page<>(pageNum, pageSize);
        resultPage.setTotal(total);
        resultPage.setRecords(pageResultList);

        log.info("库存分页查询完成，总记录数: {}，当前页记录数: {}，已按物料名称排序", total, pageResultList.size());
        return resultPage;
    }

    /**
     * 预警列表
     * @return
     */
    @Override
    public List<MaterialInfo> warnList() {
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.exists("select id from material_info where material_info.id = inventory.material_id and material_info.warn_quantity > 0 and material_info.warn_quantity >= inventory.quantity");
        List<Inventory> list = this.list(queryWrapper);
        List<Long> materialIds = list.stream().map(Inventory::getMaterialId).toList();
        if(CollUtil.isEmpty(materialIds)){
            return List.of();
        }

        LambdaQueryWrapper<MaterialInfo> materialInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        materialInfoLambdaQueryWrapper.in(MaterialInfo::getId, materialIds);
        List<MaterialInfo> materialInfos = materialInfoMapper.selectList(materialInfoLambdaQueryWrapper);
        materialInfos.forEach(item -> {
            item.setQuantity(list.stream().filter(i -> i.getMaterialId().equals(item.getId())).findFirst().get().getQuantity());
        });

        return materialInfos;
    }

    @Override
    public Inventory getByMaterialId(Long materialId) {
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Inventory::getMaterialId, materialId);
        List<Inventory> list = this.list(queryWrapper);
        if(CollUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }


    /**
     * 分页查询库存信息列表（包含物料信息，支持查询条件）
     * @param materialInfo 查询条件
     * @return 分页库存信息列表
     */
    @Override
    public Page<Map<String, Object>> getInventoryPage(MaterialInfo materialInfo) {
        int pageNum = materialInfo.getPageNum() != null ? materialInfo.getPageNum() : 1;
        int pageSize = materialInfo.getPageSize() != null ? materialInfo.getPageSize() : 10;

        log.info("库存分页查询参数: pageNum={}, pageSize={}, materialName={}, specification={}",
                pageNum, pageSize, materialInfo.getMaterialName(), materialInfo.getSpecification());

        // 如果没有查询条件，使用原来的简单分页查询
        if ((materialInfo.getMaterialName() == null || materialInfo.getMaterialName().isEmpty()) &&
            (materialInfo.getSpecification() == null || materialInfo.getSpecification().isEmpty())) {
            return getInventoryPage(pageNum, pageSize);
        }

        // 有查询条件时，需要先查询符合条件的物料ID列表
        LambdaQueryWrapper<MaterialInfo> materialQueryWrapper = new LambdaQueryWrapper<>();

        // 物料名称模糊查询
        if (materialInfo.getMaterialName() != null && !materialInfo.getMaterialName().isEmpty()) {
            materialQueryWrapper.like(MaterialInfo::getMaterialName, materialInfo.getMaterialName());
        }

        // 规格型号模糊查询
        if (materialInfo.getSpecification() != null && !materialInfo.getSpecification().isEmpty()) {
            materialQueryWrapper.like(MaterialInfo::getSpecification, materialInfo.getSpecification());
        }

        // 按物料名称排序
        materialQueryWrapper.orderByAsc(MaterialInfo::getMaterialName);

        // 查询符合条件的物料信息
        List<MaterialInfo> materialInfoList = materialInfoMapper.selectList(materialQueryWrapper);

        if (materialInfoList.isEmpty()) {
            // 如果没有符合条件的物料，返回空结果
            Page<Map<String, Object>> emptyPage = new Page<>(pageNum, pageSize);
            emptyPage.setTotal(0);
            emptyPage.setRecords(new ArrayList<>());
            log.info("没有找到符合条件的物料，返回空结果");
            return emptyPage;
        }

        // 提取物料ID列表
        List<Long> materialIds = materialInfoList.stream()
                .map(MaterialInfo::getId)
                .toList();

        log.info("找到符合条件的物料数量: {}, 物料ID列表: {}", materialIds.size(), materialIds);

        // 构建库存查询条件，只查询这些物料的库存
        LambdaQueryWrapper<Inventory> inventoryQueryWrapper = new LambdaQueryWrapper<>();
        inventoryQueryWrapper.in(Inventory::getMaterialId, materialIds);

        // 执行分页查询
        Page<Inventory> page = new Page<>(pageNum, pageSize);
        Page<Inventory> inventoryPage = this.page(page, inventoryQueryWrapper);

        log.info("库存分页查询结果: total={}, records.size={}", inventoryPage.getTotal(), inventoryPage.getRecords().size());

        // 构建返回结果
        Page<Map<String, Object>> resultPage = new Page<>(pageNum, pageSize);
        resultPage.setTotal(inventoryPage.getTotal());
        resultPage.setPages(inventoryPage.getPages());

        List<Map<String, Object>> resultList = new ArrayList<>();

        // 创建物料信息映射，提高查询效率
        Map<Long, MaterialInfo> materialInfoMap = materialInfoList.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getId(), item), HashMap::putAll);

        // 遍历库存信息，关联物料信息
        for (Inventory inventory : inventoryPage.getRecords()) {
            MaterialInfo materialInfoEntity = materialInfoMap.get(inventory.getMaterialId());
            if (materialInfoEntity != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", inventory.getId());
                map.put("materialId", materialInfoEntity.getId());
                map.put("materialName", materialInfoEntity.getMaterialName());
                map.put("specification", materialInfoEntity.getSpecification());
                map.put("unit", materialInfoEntity.getUnit());
                map.put("unitPrice", materialInfoEntity.getUnitPrice());
                map.put("quantity", inventory.getQuantity());
                resultList.add(map);
            }
        }

        // 根据物料名称排序（升序），确保最终结果有序
        resultList.sort((a, b) -> {
            String nameA = (String) a.get("materialName");
            String nameB = (String) b.get("materialName");
            if (nameA == null && nameB == null) return 0;
            if (nameA == null) return 1;
            if (nameB == null) return -1;
            return nameA.compareTo(nameB);
        });

        resultPage.setRecords(resultList);
        log.info("最终返回结果数量: {}，已按物料名称排序", resultList.size());
        return resultPage;
    }

    /**
     * 入库操作
     * @param record 入库记录
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inbound(InventoryRecord record) {
        if (record.getMaterialId() == null || record.getQuantity() == null || record.getQuantity() <= 0) {
            throw new RuntimeException("参数错误");
        }
        
        // 设置记录类型为入库
        record.setRecordType(1);
        
        // 设置创建人信息
        try {
            record.setCreator(SecurityUtils.getUsername());
            record.setCreatorId(SecurityUtils.getUserId());
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败", e);
            throw new RuntimeException("用户未登录或会话已过期，请重新登录后再操作");
        }
        
        // 保存入库记录
        inventoryRecordMapper.insert(record);
        
        // 更新库存
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Inventory::getMaterialId, record.getMaterialId());
        Inventory inventory = this.getOne(queryWrapper);
        
        if (inventory == null) {
            // 如果库存不存在，创建新的库存记录
            inventory = new Inventory();
            inventory.setMaterialId(record.getMaterialId());
            inventory.setQuantity(record.getQuantity());
            return this.save(inventory);
        } else {
            // 如果库存已存在，更新库存数量
            inventory.setQuantity(inventory.getQuantity() + record.getQuantity());
            return this.updateById(inventory);
        }
    }

    /**
     * 出库操作
     * @param record 出库记录
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean outbound(InventoryRecord record) {
        if (record.getMaterialId() == null || record.getQuantity() == null || record.getQuantity() <= 0) {
            throw new RuntimeException("参数错误");
        }
        
        // 查询库存
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Inventory::getMaterialId, record.getMaterialId());
        Inventory inventory = this.getOne(queryWrapper);
        
        if (inventory == null || inventory.getQuantity() < record.getQuantity()) {
            throw new RuntimeException("库存不足");
        }
        
        // 设置记录类型为出库
        record.setRecordType(2);
        
        // 设置创建人信息
        try {
            record.setCreator(SecurityUtils.getUsername());
            record.setCreatorId(SecurityUtils.getUserId());
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败", e);
            throw new RuntimeException("用户未登录或会话已过期，请重新登录后再操作");
        }
        
        // 保存出库记录
        inventoryRecordMapper.insert(record);
        
        // 更新库存数量
        inventory.setQuantity(inventory.getQuantity() - record.getQuantity());
        return this.updateById(inventory);
    }
} 