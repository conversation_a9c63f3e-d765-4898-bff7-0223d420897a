<script>
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		// 全局分享给朋友配置
		onShareAppMessage: function(res) {
			console.log('全局分享给朋友', res);

			// 如果是从按钮分享，可以获取按钮的dataset
			if (res.from === 'button') {
				console.log('从按钮分享:', res.target);
			}

			return getShareAppMessageConfig({
				title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务',
				path: '/pages/index/index',
			});
		},
		// 全局分享到朋友圈配置
		onShareTimeline: function(res) {
			console.log('全局分享到朋友圈', res);

			return getShareTimelineConfig({
				title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
			});
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import "./static/fonts/iconfont.css";
</style>
