package com.dfab.universalReservation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.universalReservation.entity.UniversalReservation;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通用预约服务接口
 */
public interface UniversalReservationService extends IService<UniversalReservation> {
    
    /**
     * 根据openId获取预约列表
     * @param openId 用户openId
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByOpenId(String openId);
    
    /**
     * 根据openId和预约类型获取预约列表
     * @param openId 用户openId
     * @param reservationType 预约类型
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByOpenIdAndType(String openId, Integer reservationType);
    
    /**
     * 根据会员入住记录ID获取预约列表
     * @param memberCheckinId 会员入住记录ID
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByMemberCheckinId(Long memberCheckinId);
    
    /**
     * 根据预约类型获取预约列表
     * @param reservationType 预约类型
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByType(Integer reservationType);
    
    /**
     * 根据状态获取预约列表
     * @param status 预约状态
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByStatus(Integer status);
    
    /**
     * 根据预约类型和状态获取预约列表
     * @param reservationType 预约类型
     * @param status 预约状态
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByTypeAndStatus(Integer reservationType, Integer status);
    
    /**
     * 根据时间范围获取预约列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预约列表
     */
    List<UniversalReservation> getReservationsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 创建或更新预约（小程序用）
     * @param reservation 预约信息
     * @return 保存后的预约信息
     */
    UniversalReservation createOrUpdateReservation(UniversalReservation reservation);

    /**
     * 后台管理员创建预约
     * @param reservation 预约信息
     * @return 保存后的预约信息
     */
    UniversalReservation createReservationByAdmin(UniversalReservation reservation);
    
    /**
     * 根据ID和openId获取预约信息（小程序用）
     * @param id 预约ID
     * @param openId 用户openId
     * @return 预约信息
     */
    UniversalReservation getReservationByIdAndOpenId(Long id, String openId);
    
    /**
     * 根据ID和openId删除预约（小程序用）
     * @param id 预约ID
     * @param openId 用户openId
     * @return 删除结果
     */
    boolean deleteReservationByIdAndOpenId(Long id, String openId);
    
    /**
     * 分页查询预约列表（后台管理用）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<UniversalReservation> getReservationsPageByMybatisPlus(int pageNum, int pageSize, UniversalReservation queryParams);
    
    /**
     * 更新预约状态
     * @param id 预约ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateReservationStatus(Long id, Integer status);
    
    /**
     * 分配处理人员
     * @param id 预约ID
     * @param handlerName 处理人员姓名
     * @param handlerPhone 处理人员电话
     * @return 更新结果
     */
    boolean assignHandler(Long id, String handlerName, String handlerPhone);
    
    /**
     * 完成预约并记录实际时间
     * @param id 预约ID
     * @param actualStartTime 实际开始时间
     * @param actualEndTime 实际结束时间
     * @return 更新结果
     */
    boolean completeReservation(Long id, LocalDateTime actualStartTime, LocalDateTime actualEndTime);
    
    /**
     * 取消预约
     * @param id 预约ID
     * @param cancelReason 取消原因
     * @return 更新结果
     */
    boolean cancelReservation(Long id, String cancelReason);
    
    /**
     * 评价预约
     * @param id 预约ID
     * @param rating 评分
     * @param review 评价内容
     * @return 更新结果
     */
    boolean rateReservation(Long id, Integer rating, String review);
    
    /**
     * 获取今日预约统计
     * @param reservationType 预约类型（可选）
     * @return 统计数据
     */
    long getTodayReservationCount(Integer reservationType);
    
    /**
     * 获取待处理预约数量
     * @param reservationType 预约类型（可选）
     * @return 待处理数量
     */
    long getPendingReservationCount(Integer reservationType);
}
