package com.dfab.util;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.FieldDeclaration;
import com.github.javaparser.ast.comments.Comment;

import java.io.FileInputStream;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

public class FieldCommentReader {

    public static Map<String, String> getFieldComments(String classFilePath) throws Exception {
        Map<String, String> fieldCommentMap = new HashMap<>();

        try (FileInputStream in = new FileInputStream(classFilePath)) {
            JavaParser javaParser = new JavaParser();
            ParseResult<CompilationUnit> parseResult = javaParser.parse(in);

            if (parseResult.isSuccessful() && parseResult.getResult().isPresent()) {
                CompilationUnit cu = parseResult.getResult().get();

                // 遍历所有类型声明
                for (ClassOrInterfaceDeclaration type : cu.findAll(ClassOrInterfaceDeclaration.class)) {
                    // 遍历该类型下的所有字段声明
                    for (FieldDeclaration field : type.getFields()) {
                        for (com.github.javaparser.ast.body.VariableDeclarator variable : field.getVariables()) {
                            String fieldName = variable.getNameAsString();

                            // 获取字段上方的 Javadoc 或 行注释
                            String commentText = "";
                            if (field.getJavadocComment().isPresent()) {
                                commentText = field.getJavadocComment().get().getContent();
                            } else if (field.getComment().isPresent()) {
                                Comment comment = field.getComment().get();
                                commentText = comment.getContent().trim();
                            }

                            fieldCommentMap.put(fieldName, commentText);
                        }
                    }
                }
            } else {
                System.err.println("解析失败: " + parseResult.getProblems());
            }
        }

        return fieldCommentMap;
    }
}