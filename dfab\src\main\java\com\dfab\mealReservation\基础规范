    Controller
1、有两个Controller,一个用于小程序，例如MealReservationController，一个用于后台管理，例如MealReservationAdminController
2、小程序Controller需要有新增改查接口，所有接口必须有@Autowired
                                    private HttpServletRequest request;，用于openId参数获取，获取方式String openId = (String)request.getAttribute("openId");，基础访问路径为/api/加上实体类名如/api/mealReservation
3、后台管理Controller需要有查询接口及更新接口，更新接口必须传递id,查询接口需要有列表查询list及分页查询page，基础访问路径为/admin/加上实体类名如/admin/mealReservation
4、所有接口采用POST请求
5、@Tag(name = "MealReservationController", description = "小程序餐食预约接口，提供对餐食预约信息的增删改查功能")必须有，用于swagger的注释，name是类名，description为基础描述
6、新增接口加上必要的校验注解 @Valid
7、每个接口必须加上swagger描述如@Operation(summary = "根据 ID 获取餐食预约信息", description = "通过指定的餐食预约记录 ID 查询对应的预约信息")
8、小程序Controller查询接口必须要有String openId = (String)request.getAttribute("openId")用于service进行openId过滤
    如果需要接收参数，都以实体类去接收，如@RequestBody MealReservation reservation

    entity
1、必须继承BaseEntity实体类，这里有基础的创建人创建时间更新人更新时间等必要字段
2、id为Long，且必须有@TableId(type = IdType.ASSIGN)注解
3、@TableName("meal_reservation")该注解必须且用下划线命名
4、小程序的实体类必须加上openId，userId，userName，userPhone字段
5、对于需要校验字段需要加上@Schema注解如@Schema(description = "用户的微信 openId", example = "wx1234567890", required = true)
6、字段采用驼峰
7、所有注解必须有，如@Data
           @NoArgsConstructor
           @AllArgsConstructor
           @TableName("meal_reservation")
           @Schema(description = "餐食预约实体类")
           @Builder

    aervice、mapper
1、根据mybatisplus规范生成，mapper必须实现baseMapper,如public interface MealReservationMapper extends BaseMapper<MealReservation>
    service必须实现IService如public interface MealReservationService extends IService<MealReservation>

    serviceImpl
1、必须有基础的增删改查接口
2、小程序的接口需要带上openId进行新增或者查询
3、查询采用LambdaQueryWrapper
4、更新需要加上必要的校验，如id为空，throw new RuntimeException("id不能为空");
5、用于小程序的更新，删除，必须先根据openId进行查询，如果查出没有则返回，如
LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
                                                 queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
                                                 queryWrapper.eq(MealReservation::getId, reservation.getId());
                                                 MealReservation one = getOne(queryWrapper);
                                                 if (one != null) {
                                                     return this.removeById(one.getId());
                                                 }else {
                                                     log.error("不能删除别人数据");
                                                     return false;
                                                 }

所有方法必须加上注释