package com.dfab.taskCenter.controller;

import com.dfab.taskCenter.entity.TaskCenter;
import com.dfab.taskCenter.service.TaskCenterService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务中心Controller（小程序端）
 */
@RestController
@RequestMapping("/api/taskCenter")
@RequiredArgsConstructor
@Tag(name = "TaskCenterController", description = "小程序任务中心接口，提供对任务信息的查询功能")
public class TaskCenterController {

    private final TaskCenterService taskCenterService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 根据 openId 获取任务列表
     * @return 任务列表
     */
    @Operation(summary = "根据 openId 获取任务列表", description = "通过用户的微信 openId 查询对应的任务列表")
    @PostMapping("/list")
    @Log(title = "任务中心-获取任务列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<List<TaskCenter>> getTaskList() {
        try {
            String openId = (String) request.getAttribute("openId");
            List<TaskCenter> tasks = taskCenterService.getTasksByOpenId(openId);
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据任务ID获取任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "根据任务ID获取任务详情", description = "通过任务ID查询对应的任务详细信息")
    @PostMapping("/detail")
    @Log(title = "任务中心-获取任务详情", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<TaskCenter> getTaskDetail(
            @Parameter(description = "任务ID", required = true)
            @RequestParam Long taskId) {
        try {
            String openId = (String) request.getAttribute("openId");
            TaskCenter task = taskCenterService.getById(taskId);
            
            if (task == null) {
                return ResponseEntity.notFound().build();
            }
            
            // 验证任务是否属于当前用户
            if (!openId.equals(task.getOpenId())) {
                return ResponseEntity.badRequest().build();
            }
            
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据任务类型获取任务列表
     * @param taskType 任务类型
     * @return 任务列表
     */
    @Operation(summary = "根据任务类型获取任务列表", description = "通过任务类型查询对应的任务列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取任务列表",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TaskCenter.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PostMapping("/listByType")
    @Log(title = "任务中心-根据类型获取任务列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<List<TaskCenter>> getTaskListByType(
            @Parameter(description = "任务类型：1-车牌新增，2-车牌删除，7-预产期提醒，8-入院待产通知，9-出院手续通知", required = true)
            @RequestParam Integer taskType) {
        try {
            String openId = (String) request.getAttribute("openId");
            List<TaskCenter> allTasks = taskCenterService.getTasksByOpenId(openId);
            
            // 过滤指定类型的任务
            List<TaskCenter> filteredTasks = allTasks.stream()
                    .filter(task -> task.getTaskType().equals(taskType))
                    .collect(java.util.stream.Collectors.toList());
            
            return ResponseEntity.ok(filteredTasks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据任务状态获取任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    @Operation(summary = "根据任务状态获取任务列表", description = "通过任务状态查询对应的任务列表")
    @PostMapping("/listByStatus")
    @Log(title = "任务中心-根据状态获取任务列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<List<TaskCenter>> getTaskListByStatus(
            @Parameter(description = "任务状态：0-待处理，1-已处理", required = true)
            @RequestParam Integer status) {
        try {
            String openId = (String) request.getAttribute("openId");
            List<TaskCenter> allTasks = taskCenterService.getTasksByOpenId(openId);
            
            // 过滤指定状态的任务
            List<TaskCenter> filteredTasks = allTasks.stream()
                    .filter(task -> task.getStatus().equals(status))
                    .collect(java.util.stream.Collectors.toList());
            
            return ResponseEntity.ok(filteredTasks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据关键信息获取相关任务
     * @param keyInfo 关键信息
     * @return 任务列表
     */
    @Operation(summary = "根据关键信息获取相关任务", description = "通过关键信息查询相关的任务列表")
    @PostMapping("/listByKeyInfo")
    @Log(title = "任务中心-根据关键信息获取任务列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<List<TaskCenter>> getTaskListByKeyInfo(
            @Parameter(description = "关键信息", required = true)
            @RequestParam String keyInfo) {
        try {
            String openId = (String) request.getAttribute("openId");
            List<TaskCenter> allTasks = taskCenterService.getTasksByOpenId(openId);

            // 过滤指定关键信息的任务
            List<TaskCenter> filteredTasks = allTasks.stream()
                    .filter(task -> keyInfo.equals(task.getKeyInfo()))
                    .collect(java.util.stream.Collectors.toList());

            return ResponseEntity.ok(filteredTasks);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取任务统计信息
     * @return 统计信息
     */
    @Operation(summary = "获取任务统计信息", description = "获取当前用户的任务统计信息")

    @PostMapping("/statistics")
    @Log(title = "任务中心-获取任务统计", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public ResponseEntity<Object> getTaskStatistics() {
        try {
            String openId = (String) request.getAttribute("openId");
            List<TaskCenter> allTasks = taskCenterService.getTasksByOpenId(openId);
            
            long totalTasks = allTasks.size();
            long pendingTasks = allTasks.stream().filter(task -> task.getStatus() == 0).count();
            long processedTasks = allTasks.stream().filter(task -> task.getStatus() == 1).count();
            long carAddTasks = allTasks.stream().filter(task -> task.getTaskType() == 1).count();
            long carDeleteTasks = allTasks.stream().filter(task -> task.getTaskType() == 2).count();
            long leaveTasks = allTasks.stream().filter(task -> task.getTaskType() == 3).count();
            
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalTasks", totalTasks);
            statistics.put("pendingTasks", pendingTasks);
            statistics.put("processedTasks", processedTasks);
            statistics.put("carAddTasks", carAddTasks);
            statistics.put("carDeleteTasks", carDeleteTasks);
            statistics.put("leaveTasks", leaveTasks);
            
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
