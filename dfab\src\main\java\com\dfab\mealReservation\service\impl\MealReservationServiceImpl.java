package com.dfab.mealReservation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.mealReservation.entity.MealReservation;
import com.dfab.mealReservation.mapper.MealReservationMapper;
import com.dfab.mealReservation.service.MealReservationService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 餐食预约 Service 实现类。
 */
@Service
public class MealReservationServiceImpl extends ServiceImpl<MealReservationMapper, MealReservation> implements MealReservationService {


    @Resource
    private AppUserService appUserService;

    @Resource
    @Lazy
    private MemberCheckinService memberCheckinService;


    @Override
    public List<MealReservation> getAllReservationsByOpenId(String openId) {
        LambdaQueryWrapper<MealReservation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MealReservation::getOpenId, openId); // 使用 Lambda 表达式
        lambdaQueryWrapper.orderByDesc(MealReservation::getReservationDate); // 使用 Lambda 表达式
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<MealReservation> getReservationsByDate(LocalDate date) {
        LambdaQueryWrapper<MealReservation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MealReservation::getReservationDate, date); // 使用 Lambda 表达式
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public MealReservation add(MealReservation reservation) {
        AppUser appUser = appUserService.getByOpenId(reservation.getOpenId());
        if (appUser != null) {
            reservation.setUserId(appUser.getId());
            if (appUser.getName() != null && !appUser.getName().isEmpty() && (reservation.getUserName() == null || reservation.getUserName().isEmpty())) {
                reservation.setUserName(appUser.getName());
            }
            if(appUser.getPhoneNumber() != null && !appUser.getPhoneNumber().isEmpty() && (reservation.getUserPhone() == null || reservation.getUserPhone().isEmpty())){
                reservation.setUserPhone(appUser.getPhoneNumber());
            }
            List<MemberCheckin> allMemberCheckinsByOpenId = memberCheckinService.getAllMemberCheckinsByOpenId(appUser.getOpenId());
            if(CollUtil.isNotEmpty(allMemberCheckinsByOpenId)){
                if(StrUtil.isBlank(reservation.getRoomNumber())){
                    reservation.setRoomNumber(allMemberCheckinsByOpenId.get(0).getRoomNumber());
                }
                //会员id
                reservation.setMemberCheckinId(allMemberCheckinsByOpenId.get(0).getId());
            }

        }
        this.save(reservation);
        return reservation;
    }
    @Override
    public MealReservation addByAdmin(MealReservation reservation) {

        this.save(reservation);
        return reservation;
    }

    @Override
    public List<MealReservation> getReservationsByOpenIdAndDate(String openId, LocalDate date) {
        LambdaQueryWrapper<MealReservation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MealReservation::getOpenId, openId)
                          .eq(MealReservation::getReservationDate, date);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public IPage<MealReservation> page(MealReservation reservation) {

        int pageNum = reservation.getPageNum() == null ? 1 : reservation.getPageNum();
        int pageSize = reservation.getPageSize() == null ? 10 : reservation.getPageSize();
        Page<MealReservation> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        if (reservation.getOpenId() != null) {
            queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        }
        if (reservation.getReservationDate() != null) {
            queryWrapper.eq(MealReservation::getReservationDate, reservation.getReservationDate());
        }
        if (reservation.getReservationDateBegin() != null) {
            queryWrapper.ge(MealReservation::getReservationDate, reservation.getReservationDateBegin());
        }
        if (reservation.getReservationDateEnd() != null) {
            queryWrapper.le(MealReservation::getReservationDate, reservation.getReservationDateEnd());
        }
        if (reservation.getUserPhone() != null) {
            queryWrapper.like(MealReservation::getUserPhone, reservation.getUserPhone());
        }
        if (reservation.getRoomNumber() != null) {
            queryWrapper.like(MealReservation::getRoomNumber, reservation.getRoomNumber());
        }
        if (reservation.getUserName() != null) {
            queryWrapper.like(MealReservation::getUserName, reservation.getUserName());
        }

        if(null != reservation.getMemberStatus()){
            queryWrapper.exists("select 1 from member_checkin where delete_status = 0 and status = "+reservation.getMemberStatus() +" and id = meal_reservation.member_checkin_id");
        }


        String mealTimeStr = reservation.getMealTime();
        if (mealTimeStr != null && !mealTimeStr.isEmpty()) {
            String[] split = mealTimeStr.split(",");
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < split.length; i++) {
                    if (i == 0) {
                        wrapper.like(MealReservation::getMealTime, split[i].trim());
                    } else {
                        wrapper.or().like(MealReservation::getMealTime, split[i].trim());
                    }
                }
            });
        }
        queryWrapper.orderByDesc(MealReservation::getReservationDate);

        return this.page(page, queryWrapper);
    }

    /**
     * 获取查询条件下的用餐份数汇总
     * @param reservation
     * @return
     */
    @Override
    public Map<String,Integer> getSum(MealReservation reservation) {



        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        if (reservation.getOpenId() != null) {
            queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        }
        if (reservation.getReservationDate() != null) {
            queryWrapper.eq(MealReservation::getReservationDate, reservation.getReservationDate());
        }
        if (reservation.getReservationDateBegin() != null) {
            queryWrapper.ge(MealReservation::getReservationDate, reservation.getReservationDateBegin());
        }
        if (reservation.getReservationDateEnd() != null) {
            queryWrapper.le(MealReservation::getReservationDate, reservation.getReservationDateEnd());
        }
        if (reservation.getUserPhone() != null) {
            queryWrapper.like(MealReservation::getUserPhone, reservation.getUserPhone());
        }
        if (reservation.getRoomNumber() != null) {
            queryWrapper.like(MealReservation::getRoomNumber, reservation.getRoomNumber());
        }
        if (reservation.getUserName() != null) {
            queryWrapper.like(MealReservation::getUserName, reservation.getUserName());
        }

        if(null != reservation.getMemberStatus()){
            queryWrapper.exists("select 1 from member_checkin where delete_status = 0 and status = "+reservation.getMemberStatus() +" and id = meal_reservation.member_checkin_id");
        }


        Map<String,Integer> result = new HashMap<>();
        List<MealReservation> list = this.list(queryWrapper);

        //早餐数量
        int breakfastCount = list.stream().filter(l -> l.getMealTime().contains("breakfast")).mapToInt(MealReservation::getQuantity).sum();
        result.put("breakfast",breakfastCount);
        //午餐数量
        int lunchCount = list.stream().filter(l -> l.getMealTime().contains("lunch")).mapToInt(MealReservation::getQuantity).sum();
        result.put("lunch",lunchCount);
        //晚餐数量
        int dinnerCount = list.stream().filter(l -> l.getMealTime().contains("dinner")).mapToInt(MealReservation::getQuantity).sum();
        result.put("dinner",dinnerCount);
        //总计
        result.put("numTotal",breakfastCount+lunchCount+dinnerCount);
        return result;
    }

    @Override
    public List<MealReservation> list(MealReservation reservation) {

        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        if (reservation.getOpenId() != null) {
            queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        }
        if (reservation.getReservationDate() != null) {
            queryWrapper.eq(MealReservation::getReservationDate, reservation.getReservationDate());
        }
        if (reservation.getReservationDateBegin() != null) {
            queryWrapper.ge(MealReservation::getReservationDate, reservation.getReservationDateBegin());
        }
        if (reservation.getReservationDateEnd() != null) {
            queryWrapper.le(MealReservation::getReservationDate, reservation.getReservationDateEnd());
        }
        if (reservation.getUserPhone() != null) {
            queryWrapper.like(MealReservation::getUserPhone, reservation.getUserPhone());
        }
        if (reservation.getRoomNumber() != null) {
            queryWrapper.like(MealReservation::getRoomNumber, reservation.getRoomNumber());
        }
        if (reservation.getUserName() != null) {
            queryWrapper.like(MealReservation::getUserName, reservation.getUserName());
        }
        String mealTimeStr = reservation.getMealTime();
        if (mealTimeStr != null && !mealTimeStr.isEmpty()) {
            String[] split = mealTimeStr.split(",");
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < split.length; i++) {
                    if (i == 0) {
                        wrapper.like(MealReservation::getMealTime, split[i].trim());
                    } else {
                        wrapper.or().like(MealReservation::getMealTime, split[i].trim());
                    }
                }
            });
        }
        queryWrapper.orderByDesc(MealReservation::getReservationDate);
        return this.list(queryWrapper);
    }

    /**
     * 过滤已退住数据
     * @param reservation
     * @return
     */
    @Override
    public List<MealReservation> listOnlyIn(MealReservation reservation) {

        LambdaQueryWrapper<MemberCheckin> memberCheckinLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memberCheckinLambdaQueryWrapper.in(MemberCheckin::getStatus, Arrays.asList(0, 1));
        List<MemberCheckin> list = memberCheckinService.list(memberCheckinLambdaQueryWrapper);

        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        if(list != null && !list.isEmpty()){
            queryWrapper.and(
                    wrapper -> wrapper.eq(MealReservation::getMemberCheckinId, null)
                            .or().in(MealReservation::getMemberCheckinId, list.stream().map(MemberCheckin::getId).toList())
            );
        }

        if (reservation.getOpenId() != null) {
            queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        }
        if (reservation.getReservationDate() != null) {
            queryWrapper.eq(MealReservation::getReservationDate, reservation.getReservationDate());
        }
        if (reservation.getReservationDateBegin() != null) {
            queryWrapper.ge(MealReservation::getReservationDate, reservation.getReservationDateBegin());
        }
        if (reservation.getReservationDateEnd() != null) {
            queryWrapper.le(MealReservation::getReservationDate, reservation.getReservationDateEnd());
        }
        if (reservation.getUserPhone() != null) {
            queryWrapper.like(MealReservation::getUserPhone, reservation.getUserPhone());
        }
        if (reservation.getRoomNumber() != null) {
            queryWrapper.like(MealReservation::getRoomNumber, reservation.getRoomNumber());
        }
        if (reservation.getUserName() != null) {
            queryWrapper.like(MealReservation::getUserName, reservation.getUserName());
        }
        String mealTimeStr = reservation.getMealTime();
        if (mealTimeStr != null && !mealTimeStr.isEmpty()) {
            String[] split = mealTimeStr.split(",");
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < split.length; i++) {
                    if (i == 0) {
                        wrapper.like(MealReservation::getMealTime, split[i].trim());
                    } else {
                        wrapper.or().like(MealReservation::getMealTime, split[i].trim());
                    }
                }
            });
        }
        queryWrapper.orderByDesc(MealReservation::getReservationDate);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean updateByAdmin(MealReservation reservation) {
        if(null == reservation.getId()){
            throw new RuntimeException("id不能为空");
        }
        return this.updateById(reservation);
    }

    @Override
    public MealReservation get(MealReservation reservation) {
        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        queryWrapper.eq(MealReservation::getId, reservation.getId());
        return getOne(queryWrapper);
    }

    @Override
    public Boolean remove(MealReservation reservation) {
        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        queryWrapper.eq(MealReservation::getId, reservation.getId());
        MealReservation one = getOne(queryWrapper);
        if (one != null) {
            return this.removeById(one.getId());
        }else {
            log.error("不能删除别人数据");
            return false;
        }
    }

    @Override
    public Boolean update(MealReservation reservation) {
        LambdaQueryWrapper<MealReservation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MealReservation::getOpenId, reservation.getOpenId());
        queryWrapper.eq(MealReservation::getId, reservation.getId());
        MealReservation one = getOne(queryWrapper);
        if (one != null) {
            return this.updateById(reservation);
        }else {
            log.error("不能修改别人数据");
            return false;
        }
    }
}