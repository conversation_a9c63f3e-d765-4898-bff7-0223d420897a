package com.dfab.mealReservation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.mealReservation.entity.MealReservation;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 餐食预约 Service 接口。
 */
public interface MealReservationService extends IService<MealReservation> {

    MealReservation addByAdmin(MealReservation reservation);

    List<MealReservation> getReservationsByOpenIdAndDate(String openId, LocalDate date);

    List<MealReservation> getReservationsByDate(LocalDate date);

    MealReservation add(MealReservation reservation);

    List<MealReservation> getAllReservationsByOpenId(String openId);

    IPage<MealReservation> page(MealReservation reservation);

    Map<String,Integer> getSum(MealReservation reservation);

    List<MealReservation> list(MealReservation reservation);

    List<MealReservation> listOnlyIn(MealReservation reservation);

    Boolean updateByAdmin(MealReservation reservation);

    MealReservation get(MealReservation reservation);

    Boolean remove(MealReservation reservation);

    Boolean update(MealReservation reservation);
}