<template>
  <div class="app-container">
    <!-- 库存信息表 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>库存信息管理</span>
          <div>
            <el-button type="primary" @click="handleInbound">入库</el-button>
            <el-button type="warning" @click="handleOutbound">出库</el-button>
            <el-button type="success" @click="handleMemberOutbound">会员出库</el-button>
            <el-button type="primary" @click="handleBatchInbound" :disabled="inventorySelection.length === 0">批量入库</el-button>
            <el-button type="warning" @click="handleBatchOutbound" :disabled="inventorySelection.length === 0">批量出库</el-button>
            <el-button type="danger" @click="handleBatchDeleteInventory" :disabled="inventorySelection.length === 0">批量删除</el-button>
          </div>
        </div>
      </template>
      
      <!-- 库存信息筛选条件 -->
      <!-- <div class="search-tip">
        <el-text type="info" size="small">💡 输入查询条件后会自动搜索，无需点击搜索按钮</el-text>
      </div> -->
      <el-form :model="inventoryQueryParams" ref="inventoryQueryRef" :inline="true" class="search-form">
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="inventoryQueryParams.materialName"
            placeholder="请输入物料名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input
            v-model="inventoryQueryParams.specification"
            placeholder="请输入规格型号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleInventoryQuery">搜索</el-button>
          <el-button @click="resetInventoryQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        v-loading="loading"
        :data="inventoryList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        highlight-current-row
        ref="inventoryTableRef"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" width="50" />
        <el-table-column label="物料名称" prop="materialName" />
        <el-table-column label="规格型号" prop="specification" />
        <el-table-column label="单位" prop="unit" />
        <el-table-column label="单价" prop="unitPrice" />
        <el-table-column label="库存数量" prop="quantity" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button link type="danger" @click="handleDeleteInventory(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 库存信息分页 -->
      <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
        <el-pagination
          :current-page="inventoryQueryParams.pageNum"
          :page-size="inventoryQueryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="inventoryTotal"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleInventorySizeChange"
          @current-change="handleInventoryCurrentChange"
        />
      </div>
    </el-card>

    <!-- 出入库记录表 -->
    <el-card class="box-card" style="margin-top: 15px;">
      <template #header>
        <div class="card-header">
          <span>出入库记录</span>
          <div>
            <span v-if="currentMaterial" style="margin-right: 10px;">
              当前物料: {{ currentMaterial.materialName }} ({{ currentMaterial.specification }})
            </span>
            <el-button v-if="currentMaterial" link @click="showAllRecords">显示所有记录</el-button>
<!--            <el-button type="danger" @click="handleBatchDeleteRecord" :disabled="recordSelection.length === 0">批量删除</el-button>-->
          </div>
        </div>
      </template>
      
      <!-- 出入库记录筛选条件 -->
      <!-- <div class="search-tip">
        <el-text type="info" size="small">💡 输入查询条件后会自动搜索，无需点击搜索按钮</el-text>
      </div> -->
      <el-form :model="recordQueryParams" ref="recordQueryRef" :inline="true" class="search-form">
        <!-- 默认展示的筛选条件 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="recordQueryParams.materialName"
            placeholder="请输入物料名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input
            v-model="recordQueryParams.specification"
            placeholder="请输入规格型号"
            clearable
          />
        </el-form-item>
        <el-form-item label="出入库类型" prop="recordType" style="width: 300px;">
          <el-select v-model="recordQueryParams.recordType" placeholder="请选择类型" clearable>
            <el-option label="入库" :value="1" />
            <el-option label="出库" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input
            v-model="recordQueryParams.quantity"
            placeholder="请输入数量"
            clearable
          />
        </el-form-item>
        
        <!-- 更多筛选条件，默认隐藏 -->
        <template v-if="showMoreQuery">
          <el-form-item label="使用单位" prop="useUnit">
            <el-input
              v-model="recordQueryParams.useUnit"
              placeholder="请输入使用单位"
              clearable
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="recordQueryParams.remark"
              placeholder="请输入备注"
              clearable
            />
          </el-form-item>
          <el-form-item label="操作人" prop="creator">
            <el-input
              v-model="recordQueryParams.creator"
              placeholder="请输入操作人"
              clearable
            />
          </el-form-item>
          <el-form-item label="操作时间" prop="createTime">
            <el-date-picker
              v-model="recordQueryParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button type="primary" @click="handleRecordQuery">搜索</el-button>
          <el-button @click="resetRecordQuery">重置</el-button>
          <el-button link @click="toggleMoreQuery">
            {{ showMoreQuery ? '收回' : '更多' }}
            <i :class="showMoreQuery ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </el-form-item>
      </el-form>
      
      <el-table v-loading="recordLoading" :data="recordList" @selection-change="handleRecordSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" width="50" />
        <el-table-column label="物料名称" prop="materialName" />
        <el-table-column label="规格型号" prop="specification" />
        <el-table-column label="类型" prop="recordType">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.recordType === 1">入库</el-tag>
            <el-tag type="warning" v-else-if="scope.row.recordType === 2">出库</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="quantity" />
        <el-table-column label="使用单位" prop="useUnit" />
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="操作人" prop="creator" />
        <el-table-column label="操作时间" prop="createTime" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
<!--        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button link type="danger" @click="handleDeleteRecord(scope.row)">删除</el-button>
          </template>
        </el-table-column>-->
      </el-table>

      <!-- 出入库记录分页 -->
      <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
        <el-pagination
          :current-page="recordQueryParams.pageNum"
          :page-size="recordQueryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="recordTotal"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleRecordSizeChange"
          @current-change="handleRecordCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量入库对话框 -->
    <el-dialog title="批量入库" v-model="batchInboundOpen" width="700px" append-to-body>
      <el-form ref="batchInboundFormRef" :model="batchInboundForm" label-width="80px">
        <el-table :data="batchInboundForm.items" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column prop="materialName" label="物料名称" width="160" />
          <el-table-column prop="specification" label="规格型号" width="160" />
          <el-table-column prop="currentQuantity" label="当前库存" width="100" />
          <el-table-column label="入库数量">
            <template #default="{row}">
              <el-input-number v-model="row.quantity" :min="1" :step="1" style="width: 100%" />
            </template>
          </el-table-column>
        </el-table>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchInboundForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchInbound">确 定</el-button>
          <el-button @click="batchInboundOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量出库对话框 -->
    <el-dialog title="批量出库" v-model="batchOutboundOpen" width="700px" append-to-body>
      <el-form ref="batchOutboundFormRef" :model="batchOutboundForm" label-width="80px">
        <el-table :data="batchOutboundForm.items" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column prop="materialName" label="物料名称" width="180" />
          <el-table-column prop="specification" label="规格型号" width="180" />
          <el-table-column prop="currentQuantity" label="当前库存" width="100" />
          <el-table-column label="出库数量">
            <template #default="{row}">
              <el-input-number
                v-model="row.quantity"
                :min="1"
                :max="Math.max(1, row.currentQuantity)"
                :step="1"
                style="width: 100%"
              />
            </template>
          </el-table-column>
        </el-table>
        
        <el-form-item label="使用类型" prop="useType">
          <el-radio-group v-model="batchOutboundForm.useType" @change="handleBatchUseTypeChange">
            <el-radio :value="1">入住会员</el-radio>
            <el-radio :value="2">部门</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="batchOutboundForm.useType === 1" label="选择会员" prop="memberCheckinId">
          <el-select
            v-model="batchOutboundForm.memberCheckinId"
            placeholder="请选择入住会员"
            filterable
            style="width: 100%"
            @change="handleBatchMemberChange"
          >
            <el-option
              v-for="member in memberList"
              :key="member.id"
              :label="member.memberName + ' (' + member.phoneNumber + ') - ' + (member.roomNumber || '无房间号')"
              :value="member.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="batchOutboundForm.useType === 2" label="选择部门" prop="deptId">
          <el-select
            v-model="batchOutboundForm.deptId"
            placeholder="请选择部门"
            filterable
            style="width: 100%"
            @change="handleBatchDeptChange"
          >
            <el-option
              v-for="dept in deptList"
              :key="dept.deptId"
              :label="dept.deptName"
              :value="dept.deptId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchOutboundForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchOutbound">确 定</el-button>
          <el-button @click="batchOutboundOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 入库对话框 -->
    <el-dialog title="物料入库" v-model="inboundOpen" width="500px" append-to-body>
      <el-form ref="inboundFormRef" :model="inboundForm" :rules="inboundRules" label-width="80px">
        <el-form-item label="物料选择" prop="materialId">
          <el-select v-model="inboundForm.materialId" placeholder="请选择物料" filterable style="width: 100%" @change="handleMaterialChange">
            <el-option
              v-for="item in materialList"
              :key="item.id"
              :label="item.materialName + ' (' + item.specification + ')'"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="inboundForm.specification" placeholder="规格型号" readonly />
        </el-form-item>
        <el-form-item label="当前库存" prop="currentQuantity">
          <el-input v-model="inboundForm.currentQuantity" placeholder="当前库存" readonly />
        </el-form-item>
        <el-form-item label="入库数量" prop="quantity">
          <el-input-number v-model="inboundForm.quantity" :min="1" :step="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="inboundForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitInbound">确 定</el-button>
          <el-button @click="cancelInbound">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出库对话框 -->
    <el-dialog title="物料出库" v-model="outboundOpen" width="500px" append-to-body>
      <el-form ref="outboundFormRef" :model="outboundForm" :rules="outboundRules" label-width="80px">
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="outboundForm.materialName" readonly />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="outboundForm.specification" readonly />
        </el-form-item>
        <el-form-item label="当前库存" prop="currentQuantity">
          <el-input v-model="outboundForm.currentQuantity" readonly />
        </el-form-item>
        <el-form-item label="出库数量" prop="quantity">
          <el-input-number v-model="outboundForm.quantity" :min="1" :max="Math.max(1, outboundForm.currentQuantity)" :step="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="使用类型" prop="useType">
          <el-radio-group v-model="outboundForm.useType" @change="handleUseTypeChange">
            <el-radio :value="1">入住会员</el-radio>
            <el-radio :value="2">部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="outboundForm.useType === 1" label="选择会员" prop="memberCheckinId">
          <el-select v-model="outboundForm.memberCheckinId" placeholder="请选择入住会员" filterable style="width: 100%" @change="handleMemberChange">
            <el-option
              v-for="member in memberList"
              :key="member.id"
              :label="member.memberName + ' (' + member.phoneNumber + ') - ' + (member.roomNumber || '无房间号')"
              :value="member.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="outboundForm.useType === 1 && outboundForm.memberRoomNumber" label="房间号">
          <el-input v-model="outboundForm.memberRoomNumber" placeholder="房间号" readonly />
        </el-form-item>
        <el-form-item v-if="outboundForm.useType === 2" label="选择部门" prop="deptId">
          <el-select v-model="outboundForm.deptId" placeholder="请选择部门" filterable style="width: 100%" @change="handleDeptChange">
            <el-option
              v-for="dept in deptList"
              :key="dept.deptId"
              :label="dept.deptName"
              :value="dept.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="outboundForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOutbound">确 定</el-button>
          <el-button @click="cancelOutbound">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 会员出库对话框 -->
    <el-dialog title="会员出库" v-model="memberOutboundOpen" width="800px" append-to-body>
      <el-form ref="memberOutboundFormRef" :model="memberOutboundForm" :rules="memberOutboundRules" label-width="80px">
        <!-- 选择会员 -->
        <el-form-item label="选择会员" prop="memberCheckinId">
          <el-select
            v-model="memberOutboundForm.memberCheckinId"
            placeholder="请选择入住会员"
            filterable
            style="width: 100%"
            @change="handleMemberOutboundChange"
          >
            <el-option
              v-for="member in memberList"
              :key="member.id"
              :label="member.memberName + ' (' + member.phoneNumber + ') - ' + (member.roomNumber || '无房间号')"
              :value="member.id"
            />
          </el-select>
        </el-form-item>

        <!-- 会员套餐物料列表 -->
        <div v-if="memberOutboundForm.memberCheckinId && memberPackageList.length > 0">
          <el-divider content-position="left">会员套餐物料</el-divider>
          <el-table
            :data="memberPackageList"
            border
            style="width: 100%; margin-bottom: 20px;"
            v-loading="memberPackageLoading"
          >
            <el-table-column prop="materialName" label="物料名称" width="180" />
            <el-table-column prop="specification" label="规格型号" width="180" />
            <el-table-column prop="packageQuantity" label="套餐数量" width="100" />
            <el-table-column label="库存数量" width="100">
              <template #default="{row}">
                <span :class="{'text-red': (row.quantity || 0) <= 0}">
                  {{ row.quantity || 0 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="出库数量" width="150">
              <template #default="{row}">
                <el-input-number
                  v-model="row.outboundQuantity"
                  :min="0"
                  :max="row.quantity || 0"
                  :step="1"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="unitPrice" label="单价" width="100" />
          </el-table>
        </div>

        <!-- 没有套餐物料时的提示 -->
        <div v-if="memberOutboundForm.memberCheckinId && memberPackageList.length === 0 && !memberPackageLoading">
          <el-empty description="该会员暂无套餐物料" />
        </div>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="memberOutboundForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMemberOutbound" :disabled="!canSubmitMemberOutbound">确 定</el-button>
          <el-button @click="cancelMemberOutbound">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { pageInventory, inbound, outbound, deleteInventory, getInventoryByMaterialId } from '@/api/business/material'
import { listMaterial, pageRecord, deleteRecord } from '@/api/business/material'
import { getAllMemberCheckins, getMemberPackage } from '@/api/business/memberCheckin'
import { listDept } from '@/api/system/dept'
import { parseTime } from '@/utils/ruoyi'

const loading = ref(false)
const recordLoading = ref(false)
const inventoryList = ref([])
const materialList = ref([])
const memberList = ref([])
const deptList = ref([])
const recordList = ref([])
const allRecordList = ref([]) // 存储所有出入库记录
const inboundOpen = ref(false)
const outboundOpen = ref(false)
const batchInboundOpen = ref(false)
const batchOutboundOpen = ref(false)
const memberOutboundOpen = ref(false)
const multipleSelection = ref([])
const inventorySelection = ref([]) // 库存表格选中的行
const recordSelection = ref([]) // 记录表格选中的行
const currentMaterial = ref(null)
const inventoryTableRef = ref(null)
const showMoreQuery = ref(false)
const inventoryTotal = ref(0)
const recordTotal = ref(0)
const memberPackageList = ref([])
const memberPackageLoading = ref(false)

// 防抖定时器
let inventorySearchTimer = null
let recordSearchTimer = null

// 计算属性：是否可以提交会员出库
const canSubmitMemberOutbound = computed(() => {
  if (!memberOutboundForm.memberCheckinId) return false
  return memberPackageList.value.some(item => item.outboundQuantity > 0)
})

// 库存信息查询参数
const inventoryQueryParams = reactive({
  materialName: '',
  specification: '',
  pageNum: 1,
  pageSize: 10
})

// 出入库记录查询参数
const recordQueryParams = reactive({
  materialName: '',
  specification: '',
  recordType: undefined,
  quantity: '',
  useUnit: '',
  remark: '',
  creator: '',
  dateRange: [],
  pageNum: 1,
  pageSize: 10
})



const inboundForm = reactive({
  materialId: undefined,
  specification: '',
  currentQuantity: 0, // 当前库存数量
  quantity: 1,
  remark: ''
})

const outboundForm = reactive({
  materialId: undefined,
  materialName: '',
  specification: '',
  currentQuantity: 0,
  quantity: 1,
  useType: 1, // 默认选择入住会员
  memberCheckinId: undefined,
  memberName: '',
  memberPhone: '',
  memberRoomNumber: '',
  deptId: undefined,
  deptName: '',
  useUnit: '',
  remark: ''
})

const batchInboundForm = reactive({
  items: [],
  remark: ''
})

const batchOutboundForm = reactive({
  items: [],
  useType: 1,
  memberCheckinId: undefined,
  memberName: '',
  memberPhone: '',
  memberRoomNumber: '',
  deptId: undefined,
  deptName: '',
  useUnit: '',
  remark: ''
})

const memberOutboundForm = reactive({
  memberCheckinId: undefined,
  memberName: '',
  memberPhone: '',
  memberRoomNumber: '',
  remark: ''
})

const inboundRules = reactive({
  materialId: [{ required: true, message: '请选择物料', trigger: 'change' }],
  quantity: [{ required: true, message: '请输入入库数量', trigger: 'blur' }]
})

const outboundRules = reactive({
  quantity: [{ required: true, message: '请输入出库数量', trigger: 'blur' }],
  useType: [{ required: true, message: '请选择使用类型', trigger: 'change' }],
  memberCheckinId: [{
    required: true,
    message: '请选择入住会员',
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (outboundForm.useType === 1 && !value) {
        callback(new Error('请选择入住会员'))
      } else {
        callback()
      }
    }
  }],
  deptId: [{
    required: true,
    message: '请选择部门',
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (outboundForm.useType === 2 && !value) {
        callback(new Error('请选择部门'))
      } else {
        callback()
      }
    }
  }]
})

const memberOutboundRules = reactive({
  memberCheckinId: [{ required: true, message: '请选择会员', trigger: 'change' }]
})

const inboundFormRef = ref()
const outboundFormRef = ref()
const memberOutboundFormRef = ref()
const inventoryQueryRef = ref()
const recordQueryRef = ref()

/** 切换更多查询条件的显示/隐藏 */
function toggleMoreQuery() {
  showMoreQuery.value = !showMoreQuery.value
}

/** 防抖查询库存信息 */
function debouncedInventorySearch() {
  if (inventorySearchTimer) {
    clearTimeout(inventorySearchTimer)
  }
  inventorySearchTimer = setTimeout(() => {
    inventoryQueryParams.pageNum = 1
    getList()
  }, 500) // 500ms 防抖
}

/** 防抖查询出入库记录 */
function debouncedRecordSearch() {
  if (recordSearchTimer) {
    clearTimeout(recordSearchTimer)
  }
  recordSearchTimer = setTimeout(() => {
    // 保持当前的materialId过滤状态
    const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
    getRecordList(currentMaterialId, true) // 搜索时重置分页
  }, 500) // 500ms 防抖
}

/** 处理库存信息查询 */
function handleInventoryQuery() {
  inventoryQueryParams.pageNum = 1
  getList()
}

/** 重置库存信息查询条件 */
function resetInventoryQuery() {
  // 清除防抖定时器
  if (inventorySearchTimer) {
    clearTimeout(inventorySearchTimer)
  }

  inventoryQueryRef.value?.resetFields()
  inventoryQueryParams.materialName = ''
  inventoryQueryParams.specification = ''
  inventoryQueryParams.pageNum = 1

  // 清除当前选中的物料状态
  currentMaterial.value = null

  // 重置库存列表
  getList()

  // 重置出入库记录为显示所有记录
  getRecordList(null, true)
}

/** 处理库存分页大小变化 */
function handleInventorySizeChange(val) {
  inventoryQueryParams.pageSize = val
  inventoryQueryParams.pageNum = 1
  getList()
}

/** 处理库存当前页变化 */
function handleInventoryCurrentChange(val) {
  inventoryQueryParams.pageNum = val
  getList()
}

/** 处理出入库记录查询 */
function handleRecordQuery() {
  // 保持当前的materialId过滤状态
  const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
  getRecordList(currentMaterialId, true) // 查询时重置分页
}

/** 重置出入库记录查询条件 */
function resetRecordQuery() {
  // 清除防抖定时器
  if (recordSearchTimer) {
    clearTimeout(recordSearchTimer)
  }

  recordQueryRef.value?.resetFields()
  recordQueryParams.materialName = ''
  recordQueryParams.specification = ''
  recordQueryParams.recordType = undefined
  recordQueryParams.quantity = ''
  recordQueryParams.useUnit = ''
  recordQueryParams.remark = ''
  recordQueryParams.creator = ''
  recordQueryParams.dateRange = []
  // 保持当前的materialId过滤状态
  const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
  getRecordList(currentMaterialId, true) // 重置时重置分页
}

/** 处理记录分页大小变化 */
function handleRecordSizeChange(val) {
  recordQueryParams.pageSize = val
  // 保持当前的materialId过滤状态
  const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
  getRecordList(currentMaterialId, true) // 改变页面大小时重置分页
}

/** 处理记录当前页变化 */
function handleRecordCurrentChange(val) {
  recordQueryParams.pageNum = val
  // 保持当前的materialId过滤状态，不重置分页
  const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
  getRecordList(currentMaterialId, false) // 切换页码时不重置分页
}

/** 查询库存列表 */
function getList() {
  loading.value = true
  console.log('发送库存分页请求，参数:', inventoryQueryParams)
  pageInventory(inventoryQueryParams).then(response => {
    console.log('库存分页响应:', response)
    // 后端直接返回MyBatis-Plus的Page对象
    if (response && response.records !== undefined) {
      inventoryList.value = response.records || []
      inventoryTotal.value = parseInt(response.total) || 0
      console.log('设置库存数据:', inventoryList.value.length, '条，总数:', inventoryTotal.value)
    } else {
      inventoryList.value = []
      inventoryTotal.value = 0
      console.log('未找到records属性，设置为空数据')
    }
    loading.value = false
  }).catch(error => {
    console.error('获取库存列表失败:', error)
    inventoryList.value = []
    inventoryTotal.value = 0
    loading.value = false
  })
}

/** 查询物料列表 */
function getMaterialList() {
  listMaterial().then(response => {
    console.log('物料列表响应:', response);
    if (response && response.data) {
      materialList.value = response.data
    } else if (response) {
      materialList.value = Array.isArray(response) ? response : []
    } else {
      materialList.value = []
    }
  }).catch(error => {
    console.error('获取物料列表失败:', error)
    materialList.value = []
  })
}

/** 查询入住会员列表 */
function getMemberList() {
  getAllMemberCheckins().then(response => {
    console.log('入住会员列表响应:', response);
    if (response && response.data) {
      memberList.value = response.data
      console.log('会员列表加载成功，数量:', memberList.value.length)
    } else {
      memberList.value = []
      console.log('会员列表为空')
    }
  }).catch(error => {
    console.error('获取入住会员列表失败:', error)
    memberList.value = []
  })
}

/** 查询部门列表 */
function getDeptList() {
  listDept().then(response => {
    console.log('部门列表响应:', response);
    if (response && response.data) {
      deptList.value = response.data
      console.log('部门列表加载成功，数量:', deptList.value.length)
    } else {
      deptList.value = []
      console.log('部门列表为空')
    }
  }).catch(error => {
    console.error('获取部门列表失败:', error)
    deptList.value = []
  })
}

/** 查询出入库记录列表 */
function getRecordList(materialId, resetPage = false) {
  recordLoading.value = true

  // 设置物料ID过滤条件
  if (materialId !== undefined) {
    if (materialId) {
      recordQueryParams.materialId = materialId
    } else {
      recordQueryParams.materialId = null
    }
  }

  // 只有在需要重置分页时才重置到第一页
  if (resetPage) {
    recordQueryParams.pageNum = 1
  }

  console.log('发送出入库记录分页请求，参数:', recordQueryParams)
  pageRecord(recordQueryParams).then(response => {
    console.log('出入库记录分页响应:', response)
    // 后端直接返回MyBatis-Plus的Page对象
    if (response && response.records !== undefined) {
      allRecordList.value = response.records || []
      recordList.value = response.records || []
      recordTotal.value = parseInt(response.total) || 0
      console.log('设置记录数据:', allRecordList.value.length, '条，总数:', recordTotal.value)
    } else {
      allRecordList.value = []
      recordList.value = []
      recordTotal.value = 0
      console.log('未找到records属性，设置为空数据')
    }
    recordLoading.value = false
  }).catch(error => {
    console.error('获取出入库记录失败:', error)
    allRecordList.value = []
    recordList.value = []
    recordTotal.value = 0
    recordLoading.value = false
  })
}

/** 根据物料ID过滤出入库记录 */
function filterRecordsByMaterial(materialId) {
  if (materialId && allRecordList.value.length > 0) {
    recordList.value = allRecordList.value.filter(record => record.materialId === materialId)
    console.log(`过滤物料ID ${materialId} 的记录:`, recordList.value.length, '条')
  } else {
    recordList.value = allRecordList.value
    console.log('显示所有记录:', recordList.value.length, '条')
  }
}

/** 显示所有出入库记录 */
function showAllRecords() {
  currentMaterial.value = null

  // 清除物料ID过滤条件，查询所有记录，重置分页
  getRecordList(null, true)
  console.log('显示所有记录')
}

/** 删除单个库存记录 */
function handleDeleteInventory(row) {
  ElMessageBox.confirm(
    `确定要删除物料"${row.materialName}"的库存记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteInventory({ id: row.id }).then(response => {
      if (response && response.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('删除库存失败:', error)
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 批量删除库存记录 */
function handleBatchInbound() {
  // 检查是否有选中的数据
  if (inventorySelection.value.length === 0) {
    ElMessage.warning('请选择要入库的物料')
    return
  }

  // 创建选中数据的快照，避免在操作过程中丢失
  const selectedItems = [...inventorySelection.value]
  console.log('批量入库 - 创建数据快照:', selectedItems.length, '条')

  batchInboundForm.items = selectedItems.map(item => ({
    materialId: item.materialId,
    materialName: item.materialName,
    specification: item.specification,
    currentQuantity: Math.max(0, item.quantity || 0), // 确保库存数量不为负数
    quantity: 1
  }))
  batchInboundOpen.value = true
}

function handleBatchOutbound() {
  console.log('批量出库 - 选中的库存数据:', inventorySelection.value)

  // 检查是否有选中的数据
  if (inventorySelection.value.length === 0) {
    ElMessage.warning('请选择要出库的物料')
    return
  }

  // 检查是否有库存为0的物料
  const zeroStockItems = inventorySelection.value.filter(item => (item.quantity || 0) <= 0)
  if (zeroStockItems.length > 0) {
    const zeroStockNames = zeroStockItems.map(item => `${item.materialName}(${item.specification})`).join('、')
    ElMessageBox.alert(
      `以下物料库存为0，无法出库：\n${zeroStockNames}`,
      '库存不足提醒',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
    return
  }

  // 创建选中数据的快照，避免在操作过程中丢失
  const selectedItems = [...inventorySelection.value]
  console.log('批量出库 - 创建数据快照:', selectedItems.length, '条')

  batchOutboundForm.items = selectedItems.map(item => ({
    materialId: item.materialId,
    materialName: item.materialName,
    specification: item.specification,
    currentQuantity: Math.max(0, item.quantity || 0), // 确保库存数量不为负数
    quantity: 1
  }))
  console.log('批量出库 - 处理后的数据:', batchOutboundForm.items)
  batchOutboundOpen.value = true
}

async function submitBatchInbound() {
  try {
    loading.value = true
    for (const item of batchInboundForm.items) {
      await inbound({
        materialId: item.materialId,
        quantity: item.quantity,
        specification: item.specification,
        remark: batchInboundForm.remark
      })
    }
    ElMessage.success('批量入库成功')
    batchInboundOpen.value = false
    getList()
  } catch (error) {
    console.error('批量入库失败:', error)
    ElMessage.error('批量入库失败')
  } finally {
    loading.value = false
  }
}

async function submitBatchOutbound() {
  try {
    loading.value = true

    // 先校验所有物料的库存数量
    console.log('开始批量校验库存数量')
    const stockErrors = []

    for (const item of batchOutboundForm.items) {
      try {
        const inventoryResponse = await getInventoryByMaterialId(item.materialId)
        const currentInventory = inventoryResponse.data || inventoryResponse
        const currentStock = currentInventory ? (currentInventory.quantity || 0) : 0

        console.log(`物料${item.materialName}当前库存:${currentStock}, 出库数量:${item.quantity}`)

        if (currentStock < item.quantity) {
          stockErrors.push({
            materialName: item.materialName,
            specification: item.specification,
            currentStock: currentStock,
            outboundQuantity: item.quantity
          })
        }
      } catch (error) {
        console.error(`查询物料${item.materialName}库存失败:`, error)
        stockErrors.push({
          materialName: item.materialName,
          specification: item.specification,
          currentStock: 0,
          outboundQuantity: item.quantity,
          error: '查询库存失败'
        })
      }
    }

    // 如果有库存不足的物料，显示错误信息
    if (stockErrors.length > 0) {
      const errorMessages = stockErrors.map(error =>
        `${error.materialName}(${error.specification}): 当前库存${error.currentStock}，出库数量${error.outboundQuantity}${error.error ? ' - ' + error.error : ''}`
      ).join('\n')

      ElMessageBox.alert(
        `以下物料库存不足，无法出库：\n${errorMessages}`,
        '库存不足提醒',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
      return
    }

    // 所有校验通过，执行批量出库
    console.log('库存校验通过，开始执行批量出库')
    for (const item of batchOutboundForm.items) {
      const submitData = {
        materialId: item.materialId,
        specification: item.specification,
        quantity: item.quantity,
        useType: batchOutboundForm.useType,
        useUnit: batchOutboundForm.useUnit,
        remark: batchOutboundForm.remark,
        recordType: 2 // 出库类型
      }

      // 根据使用类型添加相应字段
      if (batchOutboundForm.useType === 1) {
        // 入住会员
        submitData.memberCheckinId = batchOutboundForm.memberCheckinId
        submitData.memberName = batchOutboundForm.memberName
        submitData.memberPhone = batchOutboundForm.memberPhone
        submitData.memberRoomNumber = batchOutboundForm.memberRoomNumber
      } else if (batchOutboundForm.useType === 2) {
        // 部门
        submitData.deptId = batchOutboundForm.deptId
        submitData.deptName = batchOutboundForm.deptName
      }

      await outbound(submitData)
    }

    ElMessage.success('批量出库成功')
    batchOutboundOpen.value = false
    getList()
    // 刷新出入库记录，保持当前的materialId过滤状态，不重置分页
    const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
    getRecordList(currentMaterialId, false)
  } catch (error) {
    console.error('批量出库失败:', error)
    ElMessage.error('批量出库失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

function handleBatchDeleteInventory() {
  if (inventorySelection.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${inventorySelection.value.length} 条库存记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const ids = inventorySelection.value.map(item => item.id)
    deleteInventory({ id: ids }).then(response => {
      if (response && response.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('批量删除库存失败:', error)
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 删除单个出入库记录 */
function handleDeleteRecord(row) {
  ElMessageBox.confirm(
    `确定要删除这条出入库记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteRecord({ id: row.id }).then(response => {
      if (response && response.code === 200) {
        ElMessage.success('删除成功')
        // 保持当前的materialId过滤状态，不重置分页
        const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
        getRecordList(currentMaterialId, false)
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('删除记录失败:', error)
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 批量删除出入库记录 */
function handleBatchDeleteRecord() {
  if (recordSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${recordSelection.value.length} 条出入库记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const ids = recordSelection.value.map(item => item.id)
    deleteRecord({ id: ids }).then(response => {
      if (response && response.code === 200) {
        ElMessage.success('删除成功')
        // 保持当前的materialId过滤状态，不重置分页
        const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
        getRecordList(currentMaterialId, false)
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('批量删除记录失败:', error)
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 表格多选框选中数据 */
function handleSelectionChange(selection) {
  console.log('选中状态变化:', selection.length, '条数据')
  multipleSelection.value = selection
  inventorySelection.value = selection
}

/** 出入库记录表格多选框选中数据 */
function handleRecordSelectionChange(selection) {
  recordSelection.value = selection
}

/** 表格行点击事件 */
function handleRowClick(row, column) {
  // 检查点击的是否是复选框列或操作列
  if (column && (column.type === 'selection' || column.fixed === 'right')) {
    // 如果点击的是复选框列或操作列，不做处理，让默认的逻辑生效
    return;
  }

  // 如果用户已经选中了多个项目，不要因为点击行而改变当前物料状态
  // 这样可以避免影响批量操作
  if (inventorySelection.value.length > 1) {
    console.log('用户已选中多个项目，跳过行点击处理')
    return;
  }

  // 更新当前物料（用于过滤出入库记录）
  currentMaterial.value = row;

  // 根据选中的物料查询该物料的出入库记录，重置分页
  console.log('选中物料:', row.materialName, '物料ID:', row.materialId)
  getRecordList(row.materialId, true)
}

/** 入库按钮操作 */
function handleInbound() {
  resetInboundForm()

  // 如果有选中的物料，自动填入物料信息
  if (multipleSelection.value.length === 1) {
    const selectedMaterial = multipleSelection.value[0]
    inboundForm.materialId = selectedMaterial.materialId
    inboundForm.specification = selectedMaterial.specification
    inboundForm.currentQuantity = selectedMaterial.quantity // 填入当前库存数量
    console.log('自动填入选中物料信息:', selectedMaterial.materialName, selectedMaterial.specification, '当前库存:', selectedMaterial.quantity)
  }

  inboundOpen.value = true
  getMaterialList()
}

/** 出库按钮操作 */
function handleOutbound() {
  if (multipleSelection.value.length !== 1) {
    ElMessage.warning('请选择一条库存记录')
    return
  }

  const row = multipleSelection.value[0]

  // 检查库存是否为0
  if ((row.quantity || 0) <= 0) {
    ElMessageBox.alert(
      `物料"${row.materialName}(${row.specification})"库存为0，无法出库`,
      '库存不足提醒',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
    return
  }

  resetOutboundForm()

  outboundForm.materialId = row.materialId
  outboundForm.materialName = row.materialName
  outboundForm.specification = row.specification
  outboundForm.currentQuantity = row.quantity
  outboundForm.quantity = 1

  // 获取会员和部门列表
  getMemberList()
  getDeptList()

  outboundOpen.value = true
}

/** 会员出库按钮操作 */
function handleMemberOutbound() {
  resetMemberOutboundForm()

  // 获取会员列表
  getMemberList()

  memberOutboundOpen.value = true
}

/** 会员选择变化事件（会员出库） */
function handleMemberOutboundChange(memberCheckinId) {
  if (!memberCheckinId) {
    memberPackageList.value = []
    return
  }

  const selectedMember = memberList.value.find(item => item.id === memberCheckinId)
  if (selectedMember) {
    memberOutboundForm.memberName = selectedMember.memberName
    memberOutboundForm.memberPhone = selectedMember.phoneNumber
    memberOutboundForm.memberRoomNumber = selectedMember.roomNumber || ''
    console.log('选择会员:', selectedMember.memberName, selectedMember.phoneNumber)

    // 获取会员套餐物料
    getMemberPackageList(memberCheckinId)
  }
}

/** 获取会员套餐物料列表 */
function getMemberPackageList(memberCheckinId) {
  memberPackageLoading.value = true
  memberPackageList.value = []

  getMemberPackage({ memberId: memberCheckinId }).then(response => {
    console.log('会员套餐物料响应:', response)
    if (response && response.data) {
      memberPackageList.value = response.data.map(item => ({
        ...item,
        outboundQuantity: 0 // 初始化出库数量为0
      }))
    } else if (response) {
      memberPackageList.value = (Array.isArray(response) ? response : []).map(item => ({
        ...item,
        outboundQuantity: 0
      }))
    } else {
      memberPackageList.value = []
    }
    memberPackageLoading.value = false
  }).catch(error => {
    console.error('获取会员套餐物料失败:', error)
    ElMessage.error('获取会员套餐物料失败')
    memberPackageList.value = []
    memberPackageLoading.value = false
  })
}

/** 重置会员出库表单 */
function resetMemberOutboundForm() {
  memberOutboundForm.memberCheckinId = undefined
  memberOutboundForm.memberName = ''
  memberOutboundForm.memberPhone = ''
  memberOutboundForm.memberRoomNumber = ''
  memberOutboundForm.remark = ''
  memberPackageList.value = []
  memberOutboundFormRef.value?.resetFields()
}

/** 取消会员出库 */
function cancelMemberOutbound() {
  memberOutboundOpen.value = false
  resetMemberOutboundForm()
}

/** 提交会员出库 */
async function submitMemberOutbound() {
  try {
    // 验证表单
    const valid = await memberOutboundFormRef.value?.validate()
    if (!valid) return

    // 获取需要出库的物料
    const outboundItems = memberPackageList.value.filter(item => item.outboundQuantity > 0)
    if (outboundItems.length === 0) {
      ElMessage.warning('请选择要出库的物料')
      return
    }

    loading.value = true

    // 先校验所有物料的库存数量
    console.log('开始校验会员出库物料库存数量')
    const insufficientItems = []

    for (const item of outboundItems) {
      try {
        const materialId = item.materialId || item.id
        console.log(`校验物料${item.materialName}(ID: ${materialId})库存数量`)

        const inventoryResponse = await getInventoryByMaterialId(materialId)
        const currentInventory = inventoryResponse.data || inventoryResponse
        const currentStock = currentInventory ? (currentInventory.quantity || 0) : 0

        console.log(`物料${item.materialName}当前库存: ${currentStock}, 出库数量: ${item.outboundQuantity}`)

        if (currentStock < item.outboundQuantity) {
          insufficientItems.push({
            materialName: item.materialName,
            specification: item.specification,
            currentStock: currentStock,
            requestQuantity: item.outboundQuantity
          })
        }
      } catch (error) {
        console.error(`校验物料${item.materialName}库存失败:`, error)
        insufficientItems.push({
          materialName: item.materialName,
          specification: item.specification,
          currentStock: 0,
          requestQuantity: item.outboundQuantity
        })
      }
    }

    // 如果有库存不足的物料，显示警告并阻止提交
    if (insufficientItems.length > 0) {
      const warningMessage = insufficientItems.map(item =>
        `物料"${item.materialName}(${item.specification})"库存不足，当前库存: ${item.currentStock}，出库数量: ${item.requestQuantity}`
      ).join('\n')

      await ElMessageBox.alert(
        warningMessage,
        '库存不足提醒',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
      loading.value = false
      return
    }

    // 所有校验通过，执行会员出库
    console.log('库存校验通过，开始执行会员出库')

    // 构建使用单位信息
    const roomInfo = memberOutboundForm.memberRoomNumber ? ` - ${memberOutboundForm.memberRoomNumber}` : ''
    const useUnit = `${memberOutboundForm.memberName}(${memberOutboundForm.memberPhone})${roomInfo}`

    // 逐个进行出库操作
    for (const item of outboundItems) {
      const submitData = {
        materialId: item.materialId || item.id,
        specification: item.specification,
        quantity: item.outboundQuantity,
        useType: 1, // 入住会员
        memberCheckinId: memberOutboundForm.memberCheckinId,
        memberName: memberOutboundForm.memberName,
        memberPhone: memberOutboundForm.memberPhone,
        memberRoomNumber: memberOutboundForm.memberRoomNumber,
        useUnit: useUnit,
        remark: memberOutboundForm.remark,
        recordType: 2 // 出库类型
      }

      console.log('会员出库提交数据:', submitData)
      await outbound(submitData)
    }

    ElMessage.success('会员出库成功')
    memberOutboundOpen.value = false
    getList()
    // 保持当前的materialId过滤状态，不重置分页
    const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
    getRecordList(currentMaterialId, false)
  } catch (error) {
    console.error('会员出库失败:', error)
    ElMessage.error('会员出库失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

/** 物料选择变化事件 */
function handleMaterialChange(materialId) {
  // 根据选中的物料ID找到对应的物料信息
  const selectedMaterial = materialList.value.find(item => item.id === materialId)
  if (selectedMaterial) {
    inboundForm.specification = selectedMaterial.specification

    // 查找对应的库存信息
    const inventoryItem = inventoryList.value.find(item => item.materialId === materialId)
    inboundForm.currentQuantity = inventoryItem ? inventoryItem.quantity : 0

    console.log('选择物料:', selectedMaterial.materialName, selectedMaterial.specification, '当前库存:', inboundForm.currentQuantity)
  }
}

/** 使用类型变化事件 */
function handleUseTypeChange(useType) {
  // 清空相关字段
  outboundForm.memberCheckinId = undefined
  outboundForm.memberName = ''
  outboundForm.memberPhone = ''
  outboundForm.memberRoomNumber = ''
  outboundForm.deptId = undefined
  outboundForm.deptName = ''
  outboundForm.useUnit = ''

  console.log('使用类型变化:', useType === 1 ? '入住会员' : '部门')
}

/** 会员选择变化事件 */
function handleMemberChange(memberCheckinId) {
  const selectedMember = memberList.value.find(item => item.id === memberCheckinId)
  if (selectedMember) {
    outboundForm.memberName = selectedMember.memberName
    outboundForm.memberPhone = selectedMember.phoneNumber
    outboundForm.memberRoomNumber = selectedMember.roomNumber || ''
    // 更新使用单位显示，包含房间号信息
    const roomInfo = selectedMember.roomNumber ? ` - ${selectedMember.roomNumber}` : ''
    outboundForm.useUnit = `${selectedMember.memberName}(${selectedMember.phoneNumber})${roomInfo}`
    console.log('选择会员:', selectedMember.memberName, selectedMember.phoneNumber, '房间号:', selectedMember.roomNumber)
  }
}

/** 部门选择变化事件 */
function handleDeptChange(deptId) {
  const selectedDept = deptList.value.find(item => item.deptId === deptId)
  if (selectedDept) {
    outboundForm.deptName = selectedDept.deptName
    outboundForm.useUnit = selectedDept.deptName
    console.log('选择部门:', selectedDept.deptName)
  }
}

/** 批量出库使用类型变化事件 */
function handleBatchUseTypeChange(useType) {
  // 清空相关字段
  batchOutboundForm.memberCheckinId = undefined
  batchOutboundForm.memberName = ''
  batchOutboundForm.memberPhone = ''
  batchOutboundForm.memberRoomNumber = ''
  batchOutboundForm.deptId = undefined
  batchOutboundForm.deptName = ''
  batchOutboundForm.useUnit = ''

  console.log('批量出库使用类型变化:', useType === 1 ? '入住会员' : '部门')
}

/** 批量出库会员选择变化事件 */
function handleBatchMemberChange(memberCheckinId) {
  if (!memberCheckinId) {
    batchOutboundForm.memberName = ''
    batchOutboundForm.memberPhone = ''
    batchOutboundForm.memberRoomNumber = ''
    batchOutboundForm.useUnit = ''
    return
  }

  const selectedMember = memberList.value.find(item => item.id === memberCheckinId)
  if (selectedMember) {
    batchOutboundForm.memberName = selectedMember.memberName
    batchOutboundForm.memberPhone = selectedMember.phoneNumber
    batchOutboundForm.memberRoomNumber = selectedMember.roomNumber || ''

    // 构建使用单位信息
    const roomInfo = selectedMember.roomNumber ? ` - ${selectedMember.roomNumber}` : ''
    batchOutboundForm.useUnit = `${selectedMember.memberName}(${selectedMember.phoneNumber})${roomInfo}`

    console.log('批量出库选择会员:', selectedMember.memberName, selectedMember.phoneNumber)
  }
}

/** 批量出库部门选择变化事件 */
function handleBatchDeptChange(deptId) {
  if (!deptId) {
    batchOutboundForm.deptName = ''
    batchOutboundForm.useUnit = ''
    return
  }

  const selectedDept = deptList.value.find(item => item.deptId === deptId)
  if (selectedDept) {
    batchOutboundForm.deptName = selectedDept.deptName
    batchOutboundForm.useUnit = selectedDept.deptName

    console.log('批量出库选择部门:', selectedDept.deptName)
  }
}

/** 重置入库表单 */
function resetInboundForm() {
  inboundForm.materialId = undefined
  inboundForm.specification = ''
  inboundForm.currentQuantity = 0
  inboundForm.quantity = 1
  inboundForm.remark = ''
  inboundFormRef.value?.resetFields()
}

/** 重置出库表单 */
function resetOutboundForm() {
  outboundForm.materialId = undefined
  outboundForm.materialName = ''
  outboundForm.specification = ''
  outboundForm.currentQuantity = 0
  outboundForm.quantity = 1
  outboundForm.useType = 1
  outboundForm.memberCheckinId = undefined
  outboundForm.memberName = ''
  outboundForm.memberPhone = ''
  outboundForm.memberRoomNumber = ''
  outboundForm.deptId = undefined
  outboundForm.deptName = ''
  outboundForm.useUnit = ''
  outboundForm.remark = ''
  outboundFormRef.value?.resetFields()
}

/** 取消入库 */
function cancelInbound() {
  inboundOpen.value = false
  resetInboundForm()
}

/** 取消出库 */
function cancelOutbound() {
  outboundOpen.value = false
  resetOutboundForm()
}

/** 提交入库 */
function submitInbound() {
  inboundFormRef.value?.validate(valid => {
    if (valid) {
      inbound(inboundForm).then(response => {
        console.log('入库操作响应:', response);
        if (response && (response.data === true || response === true)) {
          ElMessage.success('入库成功')
          inboundOpen.value = false
          getList()
          // 刷新出入库记录，保持当前的materialId过滤状态，不重置分页
          const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
          getRecordList(currentMaterialId, false)
        } else {
          ElMessage.error('入库失败')
        }
      }).catch(error => {
        console.error('入库操作失败:', error)
        ElMessage.error('入库失败: ' + error.message)
      })
    }
  })
}

/** 提交出库 */
function submitOutbound() {
  outboundFormRef.value?.validate(async valid => {
    if (valid) {
      try {
        // 先校验库存数量
        console.log('开始校验库存数量，物料ID:', outboundForm.materialId)
        const inventoryResponse = await getInventoryByMaterialId(outboundForm.materialId)
        const currentInventory = inventoryResponse.data || inventoryResponse

        console.log('当前库存信息:', currentInventory)

        if (!currentInventory || (currentInventory.quantity || 0) < outboundForm.quantity) {
          const currentStock = currentInventory ? (currentInventory.quantity || 0) : 0
          ElMessageBox.alert(
            `物料"${outboundForm.materialName}(${outboundForm.specification})"当前库存为${currentStock}，出库数量${outboundForm.quantity}超出库存，无法出库`,
            '库存不足提醒',
            {
              confirmButtonText: '确定',
              type: 'warning'
            }
          )
          return
        }

        // 构建提交数据
        const submitData = {
          materialId: outboundForm.materialId,
          specification: outboundForm.specification,
          quantity: outboundForm.quantity,
          useType: outboundForm.useType,
          useUnit: outboundForm.useUnit,
          remark: outboundForm.remark,
          recordType: 2 // 出库类型
        }

        // 根据使用类型添加相应字段
        if (outboundForm.useType === 1) {
          // 入住会员
          submitData.memberCheckinId = outboundForm.memberCheckinId
          submitData.memberName = outboundForm.memberName
          submitData.memberPhone = outboundForm.memberPhone
          submitData.memberRoomNumber = outboundForm.memberRoomNumber
        } else if (outboundForm.useType === 2) {
          // 部门
          submitData.deptId = outboundForm.deptId
          submitData.deptName = outboundForm.deptName
        }

        console.log('出库提交数据:', submitData);

        const response = await outbound(submitData)
        console.log('出库操作响应:', response);
        if (response && (response.data === true || response === true)) {
          ElMessage.success('出库成功')
          outboundOpen.value = false
          getList()
          // 刷新出入库记录，保持当前的materialId过滤状态，不重置分页
          const currentMaterialId = currentMaterial.value ? currentMaterial.value.materialId : null
          getRecordList(currentMaterialId, false)
        } else {
          ElMessage.error('出库失败')
        }
      } catch (error) {
        console.error('出库操作失败:', error)
        ElMessage.error('出库失败: ' + error.message)
      }
    }
  })
}

// 监听库存查询参数变化
watch(
  () => [inventoryQueryParams.materialName, inventoryQueryParams.specification],
  () => {
    debouncedInventorySearch()
  },
  { deep: true }
)

// 监听出入库记录查询参数变化
watch(
  () => [
    recordQueryParams.materialName,
    recordQueryParams.specification,
    recordQueryParams.recordType,
    recordQueryParams.quantity,
    recordQueryParams.useUnit,
    recordQueryParams.remark,
    recordQueryParams.creator,
    recordQueryParams.dateRange
  ],
  () => {
    debouncedRecordSearch()
  },
  { deep: true }
)

onMounted(() => {
  getList()
  // 初始化时查询所有出入库记录，重置分页
  getRecordList(null, true)
  // 预加载会员和部门列表
  getMemberList()
  getDeptList()
  // 加载物料列表
  getMaterialList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 15px;
}

.search-tip {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
  border-radius: 4px;
}

.text-red {
  color: #f56c6c;
  font-weight: bold;
}
</style>