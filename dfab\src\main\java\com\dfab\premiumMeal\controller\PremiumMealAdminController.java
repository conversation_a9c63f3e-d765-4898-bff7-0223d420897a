package com.dfab.premiumMeal.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dfab.premiumMeal.entity.PremiumMeal;
import com.dfab.premiumMeal.service.PremiumMealService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高档餐后台管理 Controller 类，提供高档餐信息的增删改查接口。
 */
@RestController
@RequestMapping("/admin/premiumMeal")
@Tag(name = "PremiumMealAdminController", description = "高档餐后台管理接口，提供对高档餐信息的增删改查功能")
public class PremiumMealAdminController {

    @Autowired
    private PremiumMealService premiumMealService;

    /**
     * 根据会员入住ID获取高档餐列表
     */
    @Operation(summary = "根据会员入住ID获取高档餐列表", description = "通过会员入住ID查询对应的高档餐信息列表")
//    @Log(title = "高档餐后台管理-根据会员入住ID获取高档餐列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    @PostMapping("/list")
    public List<PremiumMeal> list(@Parameter(description = "查询条件", required = true) @RequestBody PremiumMeal premiumMeal) {
        return premiumMealService.getByMemberCheckinId(premiumMeal.getMemberCheckinId());
    }

    /**
     * 分页查询高档餐信息
     */
    @Operation(summary = "分页查询高档餐信息", description = "分页查询高档餐信息列表")
//    @Log(title = "高档餐后台管理-分页查询高档餐信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    @PostMapping("/page")
    public Map<String, Object> page(@Parameter(description = "查询条件", required = true) @RequestBody PremiumMeal premiumMeal) {
        IPage<PremiumMeal> page = premiumMealService.page(premiumMeal);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        result.put("rows", page.getRecords());
        result.put("total", page.getTotal());
        return result;
    }

    /**
     * 添加高档餐信息
     */
    @Operation(summary = "添加高档餐信息", description = "添加新的高档餐记录")
    @Log(title = "高档餐后台管理-添加高档餐信息", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    @PostMapping("/add")
    public Map<String, Object> add(@Parameter(description = "高档餐信息", required = true) @RequestBody PremiumMeal premiumMeal) {
        premiumMealService.addByAdmin(premiumMeal);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }

    /**
     * 更新高档餐信息
     */
    @Operation(summary = "更新高档餐信息", description = "根据ID更新高档餐信息")
    @Log(title = "高档餐后台管理-更新高档餐信息", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    @PostMapping("/update")
    public Map<String, Object> update(@Parameter(description = "高档餐信息", required = true) @RequestBody PremiumMeal premiumMeal) {
        premiumMealService.updateByAdmin(premiumMeal);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }

    /**
     * 删除高档餐信息
     */
    @Operation(summary = "删除高档餐信息", description = "根据ID删除高档餐信息")
    @Log(title = "高档餐后台管理-删除高档餐信息", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    @PostMapping("/remove")
    public Map<String, Object> remove(@Parameter(description = "高档餐ID", required = true) @RequestBody Map<String, Long> params) {
        Long id = params.get("id");
        premiumMealService.removeByAdmin(id);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }
}
