import request from '@/utils/request'

// 通用请求配置，禁用防重复提交检查
const commonConfig = {
  headers: {
    'repeatSubmit': false
  }
};

// 根据会员入住ID查询高档餐列表
export function listPremiumMeal(data) {
  return request({
    url: '/admin/premiumMeal/list',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 分页查询高档餐信息
export function pagePremiumMeal(data) {
  return request({
    url: '/admin/premiumMeal/page',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 添加高档餐信息
export function addPremiumMeal(data) {
  return request({
    url: '/admin/premiumMeal/add',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 更新高档餐信息
export function updatePremiumMeal(data) {
  return request({
    url: '/admin/premiumMeal/update',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 删除高档餐信息
export function removePremiumMeal(id) {
  return request({
    url: '/admin/premiumMeal/remove',
    method: 'post',
    data: { id: id },
    ...commonConfig
  })
}
