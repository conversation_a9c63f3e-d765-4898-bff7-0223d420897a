package com.dfab.handicraftClass.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.dfab.handicraftClass.entity.HandicraftClassParticipant;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 手工课参与记录服务接口
 */
public interface HandicraftClassParticipantService extends IService<HandicraftClassParticipant> {
    
    /**
     * 根据openId获取参与的手工课列表（包含课程详情）
     * @param openId 用户openId
     * @return 参与的手工课列表
     */
    List<HandicraftClass> getParticipatedClassesByOpenId(String openId);
    
    /**
     * 根据openId和课程类型获取参与的手工课列表
     * @param openId 用户openId
     * @param classType 课程类型
     * @return 参与的手工课列表
     */
    List<HandicraftClass> getParticipatedClassesByOpenIdAndType(String openId, Integer classType);
    
    /**
     * 根据会员入住记录ID获取参与的手工课列表
     * @param memberCheckinId 会员入住记录ID
     * @return 参与的手工课列表
     */
    List<HandicraftClass> getParticipatedClassesByMemberCheckinId(Long memberCheckinId);
    
    /**
     * 根据课程ID获取参与记录列表
     * @param classId 课程ID
     * @return 参与记录列表
     */
    List<HandicraftClassParticipant> getParticipantsByClassId(Long classId);
    
    /**
     * 用户参与手工课
     * @param classId 课程ID
     * @param openId 用户openId
     * @return 参与记录
     */
    HandicraftClassParticipant joinClass(Long classId, String openId);
    
    /**
     * 用户退出手工课
     * @param classId 课程ID
     * @param openId 用户openId
     * @return 退出结果
     */
    boolean leaveClass(Long classId, String openId);
    
    /**
     * 检查用户是否已参与某个课程
     * @param classId 课程ID
     * @param openId 用户openId
     * @return 是否已参与
     */
    boolean hasJoined(Long classId, String openId);
    
    /**
     * 获取用户的参与记录
     * @param classId 课程ID
     * @param openId 用户openId
     * @return 参与记录
     */
    HandicraftClassParticipant getParticipantRecord(Long classId, String openId);
    
    /**
     * 更新参与状态
     * @param id 参与记录ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateParticipantStatus(Long id, Integer status);
    
    /**
     * 取消参与
     * @param id 参与记录ID
     * @param cancelReason 取消原因
     * @return 取消结果
     */
    boolean cancelParticipation(Long id, String cancelReason);
    
    /**
     * 评价课程
     * @param id 参与记录ID
     * @param rating 评分
     * @param review 评价内容
     * @return 评价结果
     */
    boolean rateClass(Long id, Integer rating, String review);
    
    /**
     * 获取用户参与统计
     * @param openId 用户openId
     * @return 统计信息
     */
    java.util.Map<String, Object> getUserParticipationStatistics(String openId);
    
    /**
     * 分页查询参与记录（后台管理用）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryParams 查询参数
     * @return 分页结果
     */
    PageInfo<HandicraftClassParticipant> getParticipantsPage(int pageNum, int pageSize, HandicraftClassParticipant queryParams);
}
