package com.dfab.fileInfo.controller;

import com.dfab.fileInfo.entity.FileInfo;
import com.dfab.fileInfo.service.FileService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

@RestController
@RequestMapping("/api/files")
@Api(value = "文件管理接口", tags = "文件管理")
public class FileController {

    @Autowired
    private FileService fileService;

    @Log(title = "文件管理-上传", businessType = BusinessType.QUERY)
    @PostMapping(path = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FileInfo uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("packageName") String packageName) throws IOException {
        return fileService.uploadFile(file, packageName);
    }

//    @Log(title = "文件管理-下载", businessType = BusinessType.DOWNLOAD)
    @GetMapping("/download/{id}")
    @ApiOperation(value = "下载文件", notes = "根据文件 ID 下载文件")
    public void downloadFile(@PathVariable String id, HttpServletResponse response) throws IOException {
        fileService.downloadFile(id, response);
    }

//    @Log(title = "文件管理-预览文件", businessType = BusinessType.QUERY)
    @GetMapping("/preview/{id}")
    @ApiOperation(value = "预览文件", notes = "根据文件 ID 预览文件")
    public void previewFile(@PathVariable String id, HttpServletResponse response) throws IOException {
        fileService.previewFile(id, response);
    }

//    @Log(title = "文件管理-预览不同尺寸的图片", businessType = BusinessType.QUERY)
    @GetMapping("/previewResized/{id}/{size}")
    @Operation(
        summary = "预览不同尺寸的图片",
        description = "根据文件 ID 和指定尺寸预览小、中、大、更大和超大尺寸的图片",
        responses = {
            @ApiResponse(responseCode = "200", description = "成功预览图片"),
            @ApiResponse(responseCode = "404", description = "文件未找到")
        }
    )

    public void previewResizedFile(
        @Parameter(description = "文件 ID", required = true) @PathVariable String id,
        @Parameter(description = "图片尺寸，可选值：small, middle, large, larger, morelarge", required = true) @PathVariable String size,
        HttpServletResponse response
    ) throws IOException {
        FileInfo fileInfo = fileService.getById(id);
        if (fileInfo == null) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        String fileName = fileInfo.getFileName();
        String packagePath = fileInfo.getFilePath().substring(0, fileInfo.getFilePath().lastIndexOf(File.separator));
        String resizedFilePath = packagePath + File.separator + id + "_" + size + "_" + fileName;

        File resizedFile = new File(resizedFilePath);
        if (!resizedFile.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到");
            return;
        }

        response.setContentType(fileInfo.getFileType());
        response.setContentLengthLong(resizedFile.length());

        try (InputStream inputStream = new FileInputStream(resizedFile)) {
            try (OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
        }
    }
}