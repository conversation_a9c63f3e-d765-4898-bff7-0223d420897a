package com.dfab.handicraftClass.enums;

/**
 * 手工课程类型枚举
 */
public enum ClassTypeEnum {
    
    KNITTING(1, "手工编织", "学习各种编织技巧，制作围巾、帽子等"),
    HANDICRAFT(2, "手工制作", "制作各种手工艺品，如折纸、剪纸等"),
    PAINTING(3, "绘画课程", "学习绘画技巧，包括水彩、素描等"),
    OTHER(4, "其他手工", "其他类型的手工课程");

    private final Integer code;
    private final String name;
    private final String description;

    ClassTypeEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 枚举值
     */
    public static ClassTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ClassTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     * @param code 代码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        ClassTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知类型";
    }
}
