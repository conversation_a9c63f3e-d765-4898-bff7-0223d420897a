package com.dfab.material.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.material.dto.MemberPackageDto;
import com.dfab.material.dto.MemberPackagePayResult;
import com.dfab.material.entity.MaterialInfo;

import java.util.List;

/**
 * 物料信息Service接口
 */
public interface MaterialInfoService extends IService<MaterialInfo> {
    Boolean updateMemberPackages(MemberPackageDto memberPackageDto);

    List<MaterialInfo> memberList(MaterialInfo materialInfo);

    MemberPackagePayResult memberListPay(MaterialInfo materialInfo);

    MaterialInfo getByMaterialId(Long materialId);
}
