package com.dfab.material.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 出入库记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("inventory_record")
@Schema(description = "出入库记录实体类")
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryRecord extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "记录ID", example = "1")
    private Long id;
    
    /**
     * 物料ID
     */
    @Schema(description = "物料ID", example = "1", required = true)
    private Long materialId;
    
    /**
     * 规格型号
     */
    @Schema(description = "规格型号", example = "XXXL", required = true)
    private String specification;
    
    /**
     * 出入库类型：1-入库，2-出库
     */
    @Schema(description = "出入库类型：1-入库，2-出库", example = "1", required = true)
    private Integer recordType;
    
    /**
     * 数量
     */
    @Schema(description = "数量", example = "10", required = true)
    private Integer quantity;
    
    /**
     * 使用单位
     */
    @Schema(description = "使用单位", example = "202房XXX")
    private String useUnit;

    /**
     * 使用类型：1-入住会员，2-部门
     */
    @Schema(description = "使用类型：1-入住会员，2-部门", example = "1")
    private Integer useType;

    /**
     * 入住会员ID
     */
    @Schema(description = "入住会员ID", example = "1")
    private Long memberCheckinId;

    /**
     * 会员姓名
     */
    @Schema(description = "会员姓名", example = "张三")
    private String memberName;

    /**
     * 会员手机号
     */
    @Schema(description = "会员手机号", example = "13800138000")
    private String memberPhone;

    /**
     * 会员房间号
     */
    @Schema(description = "会员房间号", example = "A101")
    private String memberRoomNumber;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", example = "1")
    private Long deptId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称", example = "财务部")
    private String deptName;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "房间物料不足")
    private String remark;
} 