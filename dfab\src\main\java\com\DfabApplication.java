package com;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
// 扫描Mapper接口
@MapperScan({"com.dfab.**.mapper"})
// 启用定时任务
@EnableScheduling
// 添加组件扫描
public class DfabApplication {

    public static void main(String[] args) {
        SpringApplication.run(DfabApplication.class, args);
    }


}