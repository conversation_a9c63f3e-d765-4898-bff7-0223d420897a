package com.dfab.taskCenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.taskCenter.dto.TaskCenterQueryDTO;
import com.dfab.taskCenter.entity.TaskCenter;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 任务中心Service接口
 */
public interface TaskCenterService extends IService<TaskCenter> {

    /**
     * 创建通用任务
     * @param taskType 任务类型
     * @param title 任务标题
     * @param description 任务描述
     * @param keyInfo 关键信息
     * @param businessId 关联业务ID
     * @param businessType 业务类型
     * @param customerName 客户姓名
     * @param customerPhone 客户电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param extendInfo 扩展信息
     * @param memberCheckinId 会员入住ID
     * @return 任务信息
     */
    TaskCenter createTask(Integer taskType, String title, String description, String keyInfo,
                         Long businessId, String businessType, String customerName, String customerPhone,
                         String openId, Long userId, String extendInfo, Long memberCheckinId);

    /**
     * 创建车牌新增任务
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @return 任务信息
     */
    TaskCenter createCarAddTask(Long carInfoId, String plateNumber, String ownerName,
                               String ownerPhone, String openId, Long userId);

    /**
     * 创建车牌新增任务（带会员信息）
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @return 创建的任务
     */
    TaskCenter createCarAddTaskWithMember(Long carInfoId, String plateNumber, String ownerName,
                                         String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName);

    /**
     * 创建车牌删除任务（带会员信息）
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @return 创建的任务
     */
    TaskCenter createCarDeleteTaskWithMember(Long carInfoId, String plateNumber, String ownerName,
                                           String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName);

    /**
     * 创建车牌删除任务（会员已删除场景）
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @return 创建的任务
     */
    TaskCenter createCarDeleteTaskForDeletedMember(Long carInfoId, String plateNumber, String ownerName,
                                                  String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName);

    /**
     * 创建车牌删除任务（用户主动删除场景，带会员信息）
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @return 创建的任务
     */
    TaskCenter createCarDeleteTaskByUser(Long carInfoId, String plateNumber, String ownerName,
                                        String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName);

    /**
     * 创建车牌删除任务
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @return 任务信息
     */
    TaskCenter createCarDeleteTask(Long carInfoId, String plateNumber, String ownerName,
                                  String ownerPhone, String openId, Long userId);

    /**
     * 创建离所任务
     * @param carInfoId 车辆信息ID
     * @param plateNumber 车牌号
     * @param ownerName 车主姓名
     * @param ownerPhone 车主电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @return 任务信息
     */
    TaskCenter createLeaveTask(Long carInfoId, String plateNumber, String ownerName,
                              String ownerPhone, String openId, Long userId);

    /**
     * 创建客户临近预产期提醒任务
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @param memberPhone 会员电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param expectedDeliveryDate 预产期
     * @return 任务信息
     */
    TaskCenter createExpectedDeliveryReminderTask(Long memberCheckinId, String memberName, String memberPhone,
                                                 String openId, Long userId, String expectedDeliveryDate);

    /**
     * 创建入院待产通知任务
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @param memberPhone 会员电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param hospitalAdmissionDate 入院待产日期
     * @return 任务信息
     */
    TaskCenter createHospitalAdmissionNotificationTask(Long memberCheckinId, String memberName, String memberPhone,
                                                      String openId, Long userId, String hospitalAdmissionDate);

    /**
     * 创建出院手续办理通知任务
     * @param memberCheckinId 会员入住ID
     * @param memberName 会员姓名
     * @param memberPhone 会员电话
     * @param openId 微信openId
     * @param userId 用户ID
     * @param dischargeDate 出院日期
     * @return 任务信息
     */
    TaskCenter createDischargeNotificationTask(Long memberCheckinId, String memberName, String memberPhone,
                                              String openId, Long userId, String dischargeDate);

    /**
     * 处理任务
     * @param taskId 任务ID
     * @param processRemark 处理备注
     * @return 处理结果
     */
    boolean processTask(Long taskId, String processRemark);

    /**
     * 根据任务类型查询任务列表
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<TaskCenter> getTasksByType(Integer taskType);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<TaskCenter> getTasksByStatus(Integer status);

    /**
     * 根据用户ID查询任务列表
     * @param userId 用户ID
     * @return 任务列表
     */
    List<TaskCenter> getTasksByUserId(Long userId);

    /**
     * 根据openId查询任务列表
     * @param openId 微信openId
     * @return 任务列表
     */
    List<TaskCenter> getTasksByOpenId(String openId);

    /**
     * 分页查询任务列表
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @param queryParams 查询参数实体
     * @return 分页任务列表
     */
    PageInfo<TaskCenter> getTasksPage(int pageNum, int pageSize, TaskCenter queryParams);

    /**
     * 查询待处理任务数量
     * @return 待处理任务数量
     */
    int getPendingTaskCount();

    /**
     * 查询今日新增任务数量
     * @return 今日新增任务数量
     */
    int getTodayTaskCount();

    /**
     * 根据关键信息查询相关任务
     * @param keyInfo 关键信息
     * @return 任务列表
     */
    List<TaskCenter> getTasksByKeyInfo(String keyInfo);

    /**
     * 根据业务类型查询任务
     * @param businessType 业务类型
     * @return 任务列表
     */
    List<TaskCenter> getTasksByBusinessType(String businessType);

    /**
     * 根据业务ID查询任务
     * @param businessId 业务ID
     * @return 任务列表
     */
    List<TaskCenter> getTasksByBusinessId(Long businessId);

    /**
     * 更新任务优先级
     * @param taskId 任务ID
     * @param priority 优先级
     * @return 更新结果
     */
    boolean updateTaskPriority(Long taskId, Integer priority);

    /**
     * 批量处理任务
     * @param taskIds 任务ID列表
     * @param processRemark 处理备注
     * @return 处理结果
     */
    boolean batchProcessTasks(List<Long> taskIds, String processRemark);

    /**
     * 更新任务基本信息（只更新业务相关字段）
     * @param taskId 任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param keyInfo 关键信息
     * @param priority 优先级
     * @return 更新结果
     */
    boolean updateTaskBasicInfo(Long taskId, String title, String description, String keyInfo, Integer priority);

    /**
     * 更新任务客户信息（只更新客户相关字段）
     * @param taskId 任务ID
     * @param customerName 客户姓名
     * @param customerPhone 客户电话
     * @return 更新结果
     */
    boolean updateTaskCustomerInfo(Long taskId, String customerName, String customerPhone);

    /**
     * 更新任务扩展信息（只更新扩展字段）
     * @param taskId 任务ID
     * @param extendInfo 扩展信息
     * @return 更新结果
     */
    boolean updateTaskExtendInfo(Long taskId, String extendInfo);
}
