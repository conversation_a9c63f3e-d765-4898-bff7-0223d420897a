package com.dfab.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权限管理实体类，用于存储系统中的权限信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("permission")
@Schema(description = "权限管理实体类")
public class Permission extends BaseEntity {
    // 权限 ID
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "权限 ID", example = "1")
    private Long id;

    // 权限名称
    @Schema(description = "权限名称", example = "用户管理")
    private String name;

    // 权限标识
    @Schema(description = "权限标识", example = "user:manage")
    private String code;

    // 权限描述
    @Schema(description = "权限描述", example = "可以进行用户管理操作")
    private String description;

    // 新增权限类型字段，使用枚举限定取值 BUTTON, MENU, API
    @Schema(description = "权限类型，取值：BUTTON, MENU, API", example = "MENU")
    private String type;
}