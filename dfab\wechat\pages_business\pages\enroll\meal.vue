<template>
	<view class="meal-container">
		<!-- 自定义导航栏 - 根据模式显示不同标题 -->
		<custom-nav-bar :title="isEdit ? '编辑陪护餐预约' : '陪护餐预约'" showBack></custom-nav-bar>

		<view class="form-container">
			<view class="form-item">
				<text class="item-label required">宝妈姓名</text>
				<input type="text" v-model="mealForm.userName" :disabled="mealForm.userNameDisabled || false" placeholder="请输入宝妈姓名"/>
				<text class="error-tip" v-if="errors.userName">{{ errors.userName }}</text>
			</view>

			<view class="form-item">
				<text class="item-label required">房间号</text>
				<input type="text" v-model="mealForm.roomNumber" :disabled="mealForm.userNameDisabled || false" placeholder="请输入房间号"/>
				<text class="error-tip" v-if="errors.roomNumber">{{ errors.roomNumber }}</text>
			</view>

			<view class="form-item">
				<text class="item-label required">预约日期</text>
				<view class="calendar-trigger" @click="showCalendar">
					<text class="trigger-text">{{ calendarTriggerText }}</text>
					<text class="trigger-icon">📅</text>
				</view>

				<!-- 已选择的日期列表 -->
				<view class="selected-dates-container" v-if="mealForm.reservationDates.length > 0">
					<view class="selected-dates-title">已选择日期 ({{ mealForm.reservationDates.length }}个):</view>
					<view class="selected-dates-list">
						<view
							class="date-tag"
							v-for="(date, index) in mealForm.reservationDates"
							:key="index"
						>
							<text class="date-text">{{ formatDisplayDate(date) }}</text>
							<text class="remove-date" @click="removeSingleDate(index)">×</text>
						</view>
					</view>
				</view>

				<text class="error-tip" v-if="errors.reservationDate">{{ errors.reservationDate }}</text>
			</view>

			<!-- 日历弹窗 -->
			<view class="calendar-modal" v-if="isCalendarVisible" @click="hideCalendar">
				<view class="calendar-content" @click.stop>
					<calendar-picker
						v-model="mealForm.reservationDates"
						:min-date="startDate"
						:max-date="endDate"
						@confirm="onCalendarConfirm"
					/>
				</view>
			</view>
			<view class="form-item">
				<text class="item-label required">用餐时段</text>
				<view class="checkbox-group">
					<view
						v-for="(item, index) in mealTimeOptions"
						:key="index"
						class="checkbox-item"
						:class="{ 'disabled': !isMealTimeAvailable(item) }"
					>
						<checkbox
							:value="item.id"
							:checked="mealForm.mealTime.includes(item.id)"
							:disabled="!isMealTimeAvailable(item)"
							@click="toggleMealTime(item)"
						/>
						<text>{{ item.name }}</text>
						<text v-if="!isMealTimeAvailable(item)" class="unavailable-text">（已截止预约）</text>
						<text v-else-if="shouldShowCountdown(item.id)" class="countdown-text">
							（还剩 {{ countdowns[item.id].hours.toString().padStart(2, '0') }}:{{ countdowns[item.id].minutes.toString().padStart(2, '0') }}:{{ countdowns[item.id].seconds.toString().padStart(2, '0') }} 截止）
						</text>
					</view>
				</view>
				<text class="error-tip" v-if="errors.mealTime">{{ errors.mealTime }}</text>
			</view>
			<view class="form-item">
				<text class="item-label required">用餐份数</text>
				<picker @change="personCountChange" :value="personCountIndex" :range="personCountOptions">
					<view class="picker-value">{{ personCountOptions[personCountIndex] || '请选择用餐份数' }}</view>
				</picker>
				<text class="error-tip" v-if="errors.quantity">{{ errors.quantity }}</text>
			</view>
			<!-- <view class="form-item">
				<text class="item-label">备注</text>
				<textarea v-model="mealForm.remarks" placeholder="请输入备注,如您的用餐忌口信息" />
			</view> -->

			<!-- 预约说明 -->
			<view class="notice-section">
				<view class="notice-title">预约须知</view>
				<view class="notice-item">1. 请尽量提前一天预约家属餐</view>
				<view class="notice-item">2. 早餐时间：7:00-8:30（当天7:30前可预约）</view>
				<view class="notice-item">3. 午餐时间：12:00-13:30（当天11:00前可预约）</view>
				<view class="notice-item">4. 晚餐时间：18:00-19:30（当天16:30前可预约）</view>
			</view>
			
			<!-- 查看我的预约按钮 -->
			<!-- <button class="view-btn" @click="viewMyReservations">查看我的预约</button> -->
			
			<button class="submit-btn" @click="submitMealForm">{{ isEdit ? '保存修改' : '提交预约' }}</button>
		</view>
	</view>
</template>

<script setup>
	import { reactive,ref,computed,onMounted, onBeforeUnmount,toRaw  } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { listMemberCheckin } from "@/config/api/member_check_in.js";
	import { addMealReservationInfo, getMealReservationInfoById, updateMealReservationInfo } from '@/config/api/meal_reservation.js'
	import CustomNavBar from '@/components/custom-nav-bar/custom-nav-bar.vue';
	import CalendarPicker from '@/components/calendar-picker/calendar-picker.vue';
	
	const isEdit = ref(false)
	const isCheckIn = ref(false)
	const isCalendarVisible = ref(false)
	
	// 从本地存储获取用户信息
	const getUserInfoFromStorage = () => {
		const userInfoStr = uni.getStorageSync('userInfo');
		if (userInfoStr) {
			try {
				const userInfo = JSON.parse(userInfoStr);
				return userInfo;
			} catch (error) {
				console.error('解析用户信息失败:', error);
				return null;
			}
		}
		return null;
	};
	
	// 获取用户姓名
	const getUserName = () => {
		const userInfo = getUserInfoFromStorage();
		if (userInfo) {
			return userInfo.name || userInfo.wxNickname || '';
		}
		return '';
	};
	
	const mealForm = reactive({
					userName: '',
					roomNumber: '',
					reservationDates: [], // 改为数组支持多选
					mealTime: [],
					quantity: '1份'
				});
	const mealTimeOptions = ref([
					{ id: 'breakfast', name: '早餐 (当天07：30截止)', cutoffHour: 7, cutoffMinute: 30 },
					{ id: 'lunch', name: '午餐 (当天11：00截止)', cutoffHour: 11, cutoffMinute: 0 },
					{ id: 'dinner', name: '晚餐 (当天16：30截止)', cutoffHour: 16, cutoffMinute: 30 }
				]);
	const countdownTimer = ref(null)
	const countdowns = reactive({
			breakfast: { hours: 0, minutes: 0, seconds: 0, isAvailable: true },
			lunch: { hours: 0, minutes: 0, seconds: 0, isAvailable: true },
			dinner: { hours: 0, minutes: 0, seconds: 0, isAvailable: true }
	})
	const personCountIndex = ref(0)
	const personCountOptions = ref(['1份', '2份', '3份', '4份', '5份'])
	const errors = reactive({})
	
	// 格式化日期为YYYY-MM-DD
	const formatDate = (date) => {
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
	const startDate = computed(() => {
	  const now = new Date();

	  // 检查当前时间下哪些餐次可用
	  const unavailableMeals = mealTimeOptions.value.filter(meal => {
	    const cutoffTime = new Date(now);
	    cutoffTime.setHours(meal.cutoffHour, meal.cutoffMinute, 0, 0);
	    return now > cutoffTime;
	  });

	  // 如果所有餐次都不可用，开始日期设为明天
	  if (unavailableMeals.length === mealTimeOptions.value.length) {
	    const tomorrow = new Date(now);
	    tomorrow.setDate(now.getDate() + 1);
	    return formatDate(tomorrow);
	  }

	  // 否则开始日期为今天
	  return formatDate(now);
	});
	
	const endDate = computed(() => {
	  const today = new Date();
	  // 创建一个新的日期对象，避免修改原始日期
	  const endDate = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
	  return formatDate(endDate);
	});

	// 日历触发按钮显示文本
	const calendarTriggerText = computed(() => {
		if (mealForm.reservationDates.length === 0) {
			return '点击选择预约日期';
		} else if (mealForm.reservationDates.length === 1) {
			return `已选择: ${mealForm.reservationDates[0]}`;
		} else {
			return `已选择 ${mealForm.reservationDates.length} 个日期`;
		}
	});

	// 显示日历
	const showCalendar = () => {
		isCalendarVisible.value = true;
	}

	// 隐藏日历
	const hideCalendar = () => {
		isCalendarVisible.value = false;
	}

	// 日历确认选择
	const onCalendarConfirm = (selectedDates) => {
		mealForm.reservationDates = [...selectedDates];
		isCalendarVisible.value = false;

		// 重新检查餐次可用性
		checkMealTimeAvailability();

		uni.showToast({
			title: `已选择 ${selectedDates.length} 个日期`,
			icon: 'success',
			duration: 1000
		});
	}

	// 格式化日期显示（将 2024-07-13 格式化为 7月13日）
	const formatDisplayDate = (dateStr) => {
		if (!dateStr) return '';
		const [year, month, day] = dateStr.split('-');
		return `${parseInt(month)}月${parseInt(day)}日`;
	}

	// 删除单个日期
	const removeSingleDate = (index) => {
		const removedDate = mealForm.reservationDates[index];
		mealForm.reservationDates.splice(index, 1);

		// 重新检查餐次可用性
		checkMealTimeAvailability();

		uni.showToast({
			title: `已移除 ${formatDisplayDate(removedDate)}`,
			icon: 'success',
			duration: 1000
		});
	}

	// 判断是否应该显示倒计时（剩余时间不足30分钟时显示）
	const shouldShowCountdown = mealId => {
		// 检查是否包含今天的预约
		if (mealForm.reservationDates.length === 0) return false;

		const now = new Date();
		const today = formatDate(now);

		// 检查选择的日期中是否包含今天
		const includesToday = mealForm.reservationDates.includes(today);

		if (!includesToday) return false;

		// 检查是否可用且剩余时间不足30分钟
		const countdown = countdowns[mealId];
		if (!countdown || !countdown.isAvailable) return false;

		// 计算总剩余分钟数
		const totalMinutes = countdown.hours * 60 + countdown.minutes;

		// 只有当剩余时间不足30分钟时才显示倒计时
		return totalMinutes < 30;
	}
	
	// 更新所有餐次的倒计时
	const updateCountdowns = () => {
		const now = new Date();
		const today = formatDate(now);

		// 检查选择的日期中是否包含今天
		const includesToday = mealForm.reservationDates.includes(today);

		// 遍历所有餐次，计算倒计时
		mealTimeOptions.value.forEach(meal => {
			// 如果选择的日期中不包含今天，不显示倒计时
			if (!includesToday) {
				countdowns[meal.id].isAvailable = true;
				countdowns[meal.id].hours = 0;
				countdowns[meal.id].minutes = 0;
				countdowns[meal.id].seconds = 0;
				return;
			}

			// 计算截止时间
			const cutoffTime = new Date(now);
			cutoffTime.setHours(meal.cutoffHour, meal.cutoffMinute, 0, 0);

			// 如果当前时间已经超过截止时间，标记为不可用
			if (now > cutoffTime) {
				countdowns[meal.id].isAvailable = false;
				countdowns[meal.id].hours = 0;
				countdowns[meal.id].minutes = 0;
				countdowns[meal.id].seconds = 0;
				return;
			}

			// 计算剩余时间（毫秒）
			const remainingTime = cutoffTime - now;

			// 转换为时分秒
			const hours = Math.floor(remainingTime / (1000 * 60 * 60));
			const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

			// 更新倒计时数据
			countdowns[meal.id].isAvailable = true;
			countdowns[meal.id].hours = hours;
			countdowns[meal.id].minutes = minutes;
			countdowns[meal.id].seconds = seconds;
		});
	}
	


	// 检查餐次可用性
	const checkMealTimeAvailability = () => {
		if (mealForm.mealTime.length > 0 && mealForm.reservationDates.length > 0) {
			const newMealTimes = [];
			const removedMealTimes = [];

			mealForm.mealTime.forEach(mealTimeId => {
				const mealTime = mealTimeOptions.value.find(option => option.id === mealTimeId);
				// 检查是否在所有选择的日期中都可用
				const isAvailableForAllDates = mealForm.reservationDates.every(date =>
					isMealTimeAvailableForDate(mealTime, date)
				);

				if (mealTime && isAvailableForAllDates) {
					newMealTimes.push(mealTimeId);
				} else if (mealTime) {
					removedMealTimes.push(mealTime.name);
				}
			});

			// 如果有餐次被移除，显示提示
			if (removedMealTimes.length > 0) {
				uni.showToast({
					title: `${removedMealTimes.join('、')}在某些日期已超过预约时间`,
					icon: 'none',
					duration: 2000
				});
				mealForm.mealTime = newMealTimes;
			}
		}
	}
	
	// 检查用餐时段是否可选（基于所有选择的日期）
	const isMealTimeAvailable = (mealTime) => {
		// 如果没有选择日期，所有时段都可选
		if (mealForm.reservationDates.length === 0) {
			return true;
		}

		// 检查是否在所有选择的日期中都可用
		return mealForm.reservationDates.every(date =>
			isMealTimeAvailableForDate(mealTime, date)
		);
	}

	// 检查用餐时段在特定日期是否可选
	const isMealTimeAvailableForDate = (mealTime, dateString) => {
		const now = new Date();
		const selectedDate = new Date(dateString);

		// 检查选择的日期是否是今天
		const isToday = selectedDate.getDate() === now.getDate() &&
						selectedDate.getMonth() === now.getMonth() &&
						selectedDate.getFullYear() === now.getFullYear();

		// 如果是未来日期（非当天），所有时段都可选
		if (!isToday) {
			return true;
		}

		// 当天的时间限制
		const currentHour = now.getHours();
		const currentMinute = now.getMinutes();

		// 检查是否超过截止时间
		if (currentHour > mealTime.cutoffHour ||
			(currentHour === mealTime.cutoffHour && currentMinute >= mealTime.cutoffMinute)) {
			return false;
		}

		return true;
	}
		
		// 切换用餐时段
		const toggleMealTime = (item) => {
				// 如果时段不可选，不执行任何操作
				if (!isMealTimeAvailable(item)) {
					uni.showToast({
						title: `${item.name}已截止预约`,
						icon: 'none'
					});
					return;
				}

				const index = mealForm.mealTime.indexOf(item.id);
				if (index === -1) {
					mealForm.mealTime.push(item.id);
				} else {
					mealForm.mealTime.splice(index, 1);
				}
		}
		
		// 用餐人数选择
		const personCountChange = (e) => {
			personCountIndex.value = e.detail.value;
			mealForm.quantity = personCountOptions.value[personCountIndex.value];
		}
		
		// 验证表单
		const validateForm = () => {
			Object.assign(errors, {});
			let isValid = true;
		
			// 添加调试信息
			console.log('表单验证 - 当前表单数据:', toRaw(mealForm));
			console.log('用餐份数值:', mealForm.quantity);
			console.log('用餐份数索引:', personCountIndex.value);
		
			if (!mealForm.userName) {
				errors.userName = '请输入宝妈姓名';
				isValid = false;
			}
		
			if (!mealForm.roomNumber) {
				errors.roomNumber = '请输入房间号';
				isValid = false;
			}
		
			if (mealForm.reservationDates.length === 0) {
				errors.reservationDate = '请至少选择一个预约日期';
				isValid = false;
			}
		
			if (mealForm.mealTime.length === 0) {
				errors.mealTime = '请选择用餐时段';
				isValid = false;
			} else {
				// 检查所选时段是否都可用
				const unavailableMeals = [];
				mealForm.mealTime.forEach(mealTimeId => {
					const mealTime = mealTimeOptions.value.find(option => option.id === mealTimeId);
					if (mealTime && !isMealTimeAvailable(mealTime)) {
						unavailableMeals.push(mealTime.name);
					}
				});
		
				if (unavailableMeals.length > 0) {
					errors.mealTime = `${unavailableMeals.join('、')}已截止预约`;
					isValid = false;
				}
			}
		
			if (!mealForm.quantity) {
				console.log('用餐份数验证失败 - 值为空:', mealForm.quantity);
				errors.quantity = '请选择用餐人数';
				isValid = false;
			}
		
			console.log('表单验证结果:', isValid);
			console.log('验证错误:', errors);
		
			return isValid;
		}
		
		// 提交表单
		const submitMealForm = async () => {
			
				if(!isCheckIn.value){
					return uni.showToast({
						title: '您还未入住，请先办理入住',
						icon: 'none'
					});
				}
			
				if (!validateForm()) {
					uni.showToast({
						title: '请完善必填信息',
						icon: 'none'
					});
					return;
				}
				
				try {
					const baseParams = toRaw(mealForm);
					baseParams.quantity = typeof baseParams.quantity == "number" ? baseParams.quantity : Number(baseParams.quantity.split('')[0]);
					baseParams.mealTime = baseParams.mealTime.join(',');

					let successCount = 0;
					let failCount = 0;
					const totalCount = baseParams.reservationDates.length;

					// 显示批量提交进度
					uni.showLoading({
						title: `正在提交 0/${totalCount}`,
						mask: true
					});

					// 循环为每个日期创建预约
					for (let i = 0; i < baseParams.reservationDates.length; i++) {
						const date = baseParams.reservationDates[i];

						// 为每个日期创建单独的参数对象
						const params = {
							...baseParams,
							reservationDate: date // 单个日期
						};
						delete params.reservationDates; // 删除数组字段

						try {
							// 更新进度
							uni.showLoading({
								title: `正在提交 ${i + 1}/${totalCount}`,
								mask: true
							});

							let response = {};
							if (isEdit.value) {
								response = await updateMealReservationInfo(params);
							} else {
								response = await addMealReservationInfo(params);
							}
							successCount++;
						} catch (error) {
							console.error(`日期 ${date} 提交失败:`, error);
							failCount++;
						}
					}

					uni.hideLoading();

					// 显示提交结果
					if (successCount === totalCount) {
						uni.showToast({
							title: `全部${totalCount}个日期${isEdit.value ? '修改' : '预约'}成功`,
							icon: 'success'
						});
					} else if (successCount > 0) {
						uni.showModal({
							title: '部分成功',
							content: `成功${successCount}个，失败${failCount}个`,
							showCancel: false
						});
					} else {
						uni.showToast({
							title: '全部提交失败，请稍后重试',
							icon: 'none'
						});
					}

					// 如果有成功的，跳转到列表页面
					if (successCount > 0) {
						setTimeout(() => {
							uni.navigateTo({
								url: `/pages_business/pages/enroll/meal_list?isCheckIn=${isCheckIn.value}`
							});
						}, 1500);
					}
				} catch (error) {
					uni.hideLoading();
					uni.showToast({
						title: '提交失败，请稍后重试',
						icon: 'none'
					});
					console.error('提交过程中发生错误:', error);
				}
			}
			
		
	
	onMounted(() => {
				
			  mealForm.quantity = personCountOptions.value[personCountIndex.value];
			  const now = new Date();
		
			  const unavailableMeals = mealTimeOptions.value.filter((meal) => !isMealTimeAvailable(meal));
			  const allMealsUnavailable = unavailableMeals.length === mealTimeOptions.value.length;
		
			  if (allMealsUnavailable) {
				const tomorrow = new Date(now);
				tomorrow.setDate(now.getDate() + 1);
				// 自动添加明天的日期到选择列表
				mealForm.reservationDates = [formatDate(tomorrow)];

				setTimeout(() => {
					uni.showModal({
						title: '温馨提示',
						content: '今日陪护餐预约已经结束，已为您自动添加明日预约日期',
						showCancel: false,
						confirmText: '我知道了',
						confirmColor: '#8b5a2b'
					});
				}, 500);
			} else {
				// 不自动添加今天的日期，让用户手动选择
				if (unavailableMeals.length > 0) {
					setTimeout(() => {
						uni.showToast({
							title: `${unavailableMeals.map((meal) => meal.name).join('、')}已截止预约`,
							icon: 'none',
							duration: 3000
						});
					}, 500);
				}
			}
			
			updateCountdowns();
			
			countdownTimer.value = setInterval(() => {
				updateCountdowns();
			  }, 1000);
		
			  mealForm.quantity = personCountOptions.value[personCountIndex.value];
			  console.log('mounted - 最终用餐份数:', mealForm.quantity);
			  console.log('mounted - 完整表单数据:', mealForm);
			});
	
	    onBeforeUnmount(() => {
	      if (countdownTimer.value) {
	        clearInterval(countdownTimer.value);
	        countdownTimer.value = null;
	      }
	    });
		
	// 查看我的预约
	const viewMyReservations = () => {
		try {
			console.log('跳转到陪护餐预约列表页面');
			uni.navigateTo({
				url: '/pages_business/pages/enroll/meal_list',
				fail: (err) => {
					console.error('跳转失败:', err);
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		} catch (error) {
			console.error('跳转过程中发生错误:', error);
			uni.showToast({
				title: '页面跳转失败',
				icon: 'none'
			});
		}
		
		
	};
	
	onLoad(async (options) => {
		if(options.edit){
			try {
				isEdit.value = options.edit
				const response = await getMealReservationInfoById({id:options.id})
				Object.assign(mealForm, response);

				// 将单个日期转换为数组格式
				if (response.reservationDate) {
					mealForm.reservationDates = [response.reservationDate];
				}

				mealForm.mealTime = mealForm.mealTime.split(',')
				personCountIndex.value = mealForm.quantity - 1
			  } catch (error) {
				console.error('获取数据失败:', error);
			  }
		}
		
		const response = await listMemberCheckin({}); 
		let memberInfo = {};
		
		if(typeof response === 'object'){
			uni.setStorageSync('memberInfo', JSON.stringify(response));
			memberInfo = response
		}
		if(Object.keys(memberInfo).length !== 0){
			isCheckIn.value = memberInfo.status !== 1
			
			if(memberInfo.memberName){
				mealForm.userName = memberInfo.memberName
				mealForm.userNameDisabled = true
			}
			
			if(memberInfo.roomNumber){
				mealForm.roomNumber = memberInfo.roomNumber
				mealForm.roomNumberDisabled = true
			}
		} 
	})
	
	
</script>

<style lang="scss" scoped>
	.meal-container {
		padding: 0 20rpx 20rpx;
		background-color: #f8f5f2;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.form-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		margin-top: 20rpx;
	}

	.form-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.form-item:first-child {
		padding-top: 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.required:after {
		content: '*';
		color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		margin-left: 5rpx;
	}

	.error-tip {
		font-size: 24rpx;
		color: #FF4D4F;
		margin-top: 8rpx;
	}

	input, textarea {
		border: 2rpx solid #ddd;
		padding: 20rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		width: 100%;
		height: 100rpx;
		line-height: normal;
		box-sizing: border-box;
		background-color: #fafafa;
		color: #333;
		vertical-align: top;
	}

	/* 确保输入框获得焦点时的样式 */
	input:focus, textarea:focus {
		border-color: #8b5a2b;
		background-color: #fff;
		outline: none;
		box-shadow: 0 0 0 2rpx rgba(139, 90, 43, 0.1);
	}

	/* 输入框占位符样式 */
	input::placeholder, textarea::placeholder {
		color: #aaa;
		font-size: 32rpx;
		opacity: 0.8;
	}

	textarea {
		height: 150rpx;
		resize: none;
		line-height: 1.5;
		padding-top: 20rpx;
	}

	/* 针对不同平台的输入框优化 */
	/* #ifdef MP-WEIXIN */
	input {
		height: 100rpx !important;
		line-height: normal !important;
		padding: 30rpx 20rpx !important;
		font-size: 32rpx !important;
	}
	/* #endif */

	/* 确保输入框内容正确显示 */
	.form-item input {
		display: block;
		width: 100%;
	}

	.picker-value {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #333;
		border: 1rpx solid #eee;
		padding: 0 20rpx;
		border-radius: 8rpx;
		background-color: #fff;
	}

	.calendar-trigger {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		color: #333;
		border: 1rpx solid #eee;
		padding: 0 20rpx;
		border-radius: 8rpx;
		background-color: #fff;
	}

	.trigger-text {
		flex: 1;
	}

	.trigger-icon {
		font-size: 32rpx;
		margin-left: 10rpx;
	}

	.calendar-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
	}

	.calendar-content {
		width: 100%;
		max-width: 700rpx;
		max-height: 80vh;
		overflow-y: auto;
	}

	.selected-dates-container {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: #f8f5f2;
		border-radius: 8rpx;
		border: 1rpx solid #e8e8e8;
	}

	.selected-dates-title {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 15rpx;
		font-weight: bold;
	}

	.selected-dates-list {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}

	.date-tag {
		display: flex;
		align-items: center;
		background-color: #8b5a2b;
		color: #fff;
		padding: 8rpx 15rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		box-shadow: 0 2rpx 4rpx rgba(139, 90, 43, 0.2);
	}

	.date-text {
		margin-right: 8rpx;
	}

	.remove-date {
		font-size: 28rpx;
		font-weight: bold;
		padding: 0 5rpx;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.3);
		line-height: 1;
		width: 30rpx;
		height: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: background-color 0.2s;
	}

	.remove-date:active {
		background-color: rgba(255, 255, 255, 0.5);
	}



	.checkbox-group {
		display: flex;
		flex-direction: column;
		margin: 10rpx 0;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.checkbox-item text {
		font-size: 26rpx;
		margin-left: 8rpx;
	}

	.checkbox-item.disabled {
		opacity: 0.6;
	}

	.unavailable-text {
		font-size: 24rpx;
		color: #FF4D4F;
		margin-left: 10rpx;
	}

	.countdown-text {
		font-size: 24rpx;
		color: #ff3300;
		margin-left: 10rpx;
		font-weight: bold;
		animation: blink 1s infinite;
	}

	@keyframes blink {
		0% { opacity: 1; }
		50% { opacity: 0.6; }
		100% { opacity: 1; }
	}

	.notice-section {
		margin: 30rpx 0;
		padding: 20rpx;
		background-color: #f8f5f2; /* 与首页背景色一致 */
		border-radius: 8rpx;
		border-left: 4rpx solid #8b5a2b; /* 深棕色，与首页标题颜色一致 */
	}

	.notice-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #8b5a2b; /* 与首页标题颜色一致 */
		margin-bottom: 15rpx;
	}

	.notice-item {
		font-size: 24rpx;
		color: #666;
		line-height: 1.8;
	}

	.submit-btn {
		background-color: #8b5a2b !important; /* 深棕色，与首页标题颜色一致 */
		color: #fff !important;
		border-radius: 50rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		font-weight: bold;
		border: none !important;
		outline: none !important;
	}

	/* 确保按钮在各种状态下都保持正确的颜色 */
	.submit-btn::after {
		border: none !important;
	}

	.submit-btn:hover {
		background-color: #7A4F26 !important;
		color: #fff !important;
	}

	.submit-btn:active {
		background-color: #6B4421 !important;
		color: #fff !important;
	}

	.view-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		background-color: #f0f0f0;
		color: #8b5a2b;
		border: 1rpx solid #8b5a2b;
		font-size: 32rpx;
		border-radius: 44rpx;
		margin-bottom: 30rpx;
	}
</style>
