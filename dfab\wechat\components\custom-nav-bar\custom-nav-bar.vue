<template>
	<view class="custom-nav-bar" :style="{ height: navBarHeight + 'px' }">
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		<view class="nav-content" :style="{ height: contentHeight + 'px' }">
			<view v-if="showBack" class="nav-back" @click="handleBack">
				<!-- <text class="back-icon">&#8592;</text> -->
				<up-icon name="arrow-left" color="#8b5a2b" size="20"></up-icon>
			</view>
			<view class="nav-title" :style="{ paddingTop: titlePaddingTop + 'px' }">{{ title }}</view>
		</view>
	</view>
	<!-- 占位元素，防止内容被导航栏遮挡 -->
	<view :style="{ height: navBarHeight + 'px' }"></view>
</template>

<script>
export default {
	name: 'CustomNavBar',
	props: {
		title: {
			type: String,
			default: '东方爱堡'
		},
		showBack: {
			type: Boolean,
			default: true
		},
		// 是否预留右侧胶囊按钮位置
		reserveRight: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			statusBarHeight: 20,
			contentHeight: 44,
			navBarHeight: 64,
			menuButtonInfo: null,
			titlePaddingTop: 0
		}
	},
	created() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 获取微信小程序胶囊按钮位置信息
		this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		
		// 计算导航栏内容区高度
		this.contentHeight = 50; // 设置固定高度
		
		// 计算整个导航栏高度
		this.navBarHeight = this.statusBarHeight + this.contentHeight;
		
		// 设置标题的位置，使用负值进一步上移
		this.titlePaddingTop = -10; // 使用负值使标题再往上移动
	},
	methods: {
		handleBack() {
			// 判断是否可以返回上一页
			const pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({
					delta: 1
				});
			} else {
				// 如果没有上一页，可以跳转到首页
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		}
	}
}
</script>

<style lang="scss">
.custom-nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 9999;
	background-color: #FAFAF5;
	box-shadow: none;
	
	.nav-content {
		display: flex;
		align-items: center;
		position: relative;
		padding: 0 15px;
		justify-content: center;
	}
	
	.nav-back {
		position: absolute;
		left: 5px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 36rpx;
		font-weight: 600;
		color: #8b5a2b;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
</style> 