package com.dfab.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;


import java.util.Date;

/**
 * 后台管理用户实体类，用于存储后台管理员的基本信息。
 * 该类包含了后台用户的账号、密码、邮箱等重要信息，
 * 同时还记录了用户的状态和最后登录时间。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("admin_user")
@Schema(description = "后台管理用户实体类")
public class AdminUser extends BaseEntity {
    /**
     * 后台用户的唯一标识，系统自动分配的 ID。
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "后台用户 ID", example = "1")
    private Long id;

    /**
     * 后台用户用于登录系统的用户名，该字段不能为空。
     */
    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名", example = "admin", required = true)
    private String username;

    /**
     * 后台用户用于登录系统的密码，该字段不能为空。
     */
    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码", example = "123456", required = true)
    private String password;

    /**
     * 后台用户的联系邮箱，可用于找回密码、接收系统通知等。
     */
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 后台用户的状态，0 表示禁用，1 表示启用。
     */
    @Schema(description = "用户状态，0：禁用、1：启用", example = "1")
    private Integer status;

    /**
     * 后台用户最后一次登录系统的时间。
     */
    @Schema(description = "最后登录时间", example = "2024-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastLoginTime;

    /**
     * 后台用户的联系电话。
     */
    @Schema(description = "用户电话", example = "13800138000")
    private String phoneNumber;

    /**
     * 标识该后台用户是否为超级管理员，1 表示是，0 表示不是。
     */
    @Schema(description = "是否管理员标识，2 表示是管理员，0 表示不是管理员", example = "0")
    private Integer isAdmin;
}