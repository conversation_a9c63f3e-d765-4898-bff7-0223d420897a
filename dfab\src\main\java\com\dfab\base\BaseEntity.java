package com.dfab.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseEntity implements Serializable {
    // 分页参数
    @TableField(exist = false)
    private Integer pageNum;
    @TableField(exist = false)
    private Integer pageSize;

    // 删除状态，0 表示未删除，1 表示已删除
    @TableLogic(value = "0", delval = "1")
    private Integer deleteStatus = 0;

    // 创建人
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    // 创建人 ID
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    // 添加创建时间字段及注解
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 修改人
    @TableField(fill = FieldFill.UPDATE)
    private String modifier;
    // 修改人 ID
    @TableField(fill = FieldFill.UPDATE)
    private Long modifierId;

    // 添加修改时间字段及注解
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
}