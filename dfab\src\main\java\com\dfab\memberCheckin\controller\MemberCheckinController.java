package com.dfab.memberCheckin.controller;

import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入住会员管理 Controller 类，提供小程序端入住会员信息的增删改查接口。
 */
@RestController
@RequestMapping("/api/memberCheckin")
@Tag(name = "MemberCheckinController", description = "小程序入住会员管理接口，提供对入住会员信息的增删改查功能")
public class MemberCheckinController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private MemberCheckinService memberCheckinService;

    /**
     * 创建新的入住会员信息
     * @param memberCheckin 入住会员信息实体
     * @return 创建成功的入住会员信息
     */
    @Operation(summary = "创建新的入住会员信息", description = "将新的入住会员信息保存到数据库中")
    @PostMapping("/save")
    public MemberCheckin createMemberCheckin(@Parameter(description = "入住会员信息实体", required = true) @Valid @RequestBody MemberCheckin memberCheckin) {
        String openId = (String)request.getAttribute("openId");
        memberCheckin.setOpenId(openId);
        memberCheckinService.add(memberCheckin);
        return memberCheckin;
    }

    /**
     * 根据 ID 获取入住会员信息
     * @param memberCheckin 包含ID的入住会员实体
     * @return 对应的入住会员信息
     */
    @Operation(summary = "根据 ID 获取入住会员信息", description = "通过指定的入住会员记录 ID 查询对应的入住信息")
    @PostMapping("/get")
    public MemberCheckin getMemberCheckinById(@Parameter(description = "包含ID的入住会员实体", required = true) @RequestBody MemberCheckin memberCheckin) {
        String openId = (String)request.getAttribute("openId");
        memberCheckin.setOpenId(openId);
        return memberCheckinService.get(memberCheckin);
    }

    /**
     * 根据 ID 修改入住会员信息
     * @param memberCheckin 入住会员信息实体
     * @return 修改结果
     */
    @Operation(summary = "根据 ID 修改入住会员信息", description = "通过指定的入住会员记录 ID 修改对应的入住信息")
    @PostMapping("/update")
    public Boolean updateMemberCheckin(@Parameter(description = "入住会员信息实体", required = true) @Valid @RequestBody MemberCheckin memberCheckin) {
        String openId = (String)request.getAttribute("openId");
        memberCheckin.setOpenId(openId);
        return memberCheckinService.update(memberCheckin);
    }

    /**
     * 删除入住会员信息
     */
    @Operation(summary = "删除入住会员信息", description = "根据指定的 ID 删除对应的入住会员信息")
    @PostMapping("/remove")
    public Boolean deleteMemberCheckin(@RequestBody MemberCheckin memberCheckin) {
        String openId = (String)request.getAttribute("openId");
        memberCheckin.setOpenId(openId);
        return memberCheckinService.remove(memberCheckin);
    }

    /**
     * 根据 openId 获取入住会员信息
     * @return 对应 openId 的入住会员信息列表
     */
    @Operation(summary = "根据 openId 获取入住会员信息", description = "通过用户的微信 openId 查询对应的入住会员信息")
    @PostMapping("/list")
    public List<MemberCheckin> getAllMemberCheckinsByOpenId() {
        String openId = (String)request.getAttribute("openId");
        return memberCheckinService.getAllMemberCheckinsByOpenId(openId);
    }

    /**
     * 根据 openId 获取当前入住的会员信息
     * @return 对应 openId 的当前入住会员信息列表
     */
    @Operation(summary = "根据 openId 获取当前入住的会员信息", description = "通过用户的微信 openId 查询当前入住的会员信息")
    @PostMapping("/current")
    public List<MemberCheckin> getCurrentMemberCheckins() {
        String openId = (String)request.getAttribute("openId");
        return memberCheckinService.getCurrentMemberCheckinsByOpenId(openId);
    }
}
