<template>
  <el-dialog :title="title" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="800px" append-to-body @close="handleClose">
    <el-form ref="handicraftClassRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程名称" prop="className">
            <el-input v-model="form.className" placeholder="请输入课程名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程类型" prop="classType">
            <el-select v-model="form.classType" placeholder="请选择课程类型" style="width: 100%">
              <el-option
                v-for="(name, code) in classTypes"
                :key="code"
                :label="name"
                :value="parseInt(code)"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程时长" prop="duration">
            <el-input-number v-model="form.duration" :min="1" :max="300" placeholder="分钟" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上课时间" prop="classTime">
            <el-date-picker
              v-model="form.classTime"
              type="datetime"
              placeholder="请选择上课时间"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上课地点" prop="location">
            <el-input v-model="form.location" placeholder="请输入上课地点" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择课程状态" style="width: 100%">
              <el-option label="待开始" :value="0" />
              <el-option label="进行中" :value="1" />
              <el-option label="已完成" :value="2" />
              <el-option label="已取消" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="课程描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入课程描述" />
      </el-form-item>



      <el-form-item label="备注" prop="remarks">
        <el-input v-model="form.remarks" type="textarea" :rows="2" placeholder="请输入备注" />
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="HandicraftClassForm">
import { ref, reactive, toRefs, watch, onMounted, getCurrentInstance } from 'vue';
import {
  getHandicraftClass,
  addHandicraftClass,
  updateHandicraftClass,
  getClassTypes
} from "@/api/business/handicraftClass";

const { proxy } = getCurrentInstance();

// 定义组件的 props 和 emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  classId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:visible', 'success']);

const handicraftClassRef = ref();
const classTypes = ref({});
const title = ref("");

const data = reactive({
  form: {
    id: null,
    className: '',
    classType: null,
    duration: 60,
    classTime: null,
    location: '',
    status: 0,
    description: '',
    remarks: ''
  },
  rules: {
    className: [
      { required: true, message: "课程名称不能为空", trigger: "blur" }
    ],
    classType: [
      { required: true, message: "课程类型不能为空", trigger: "change" }
    ],
    classTime: [
      { required: true, message: "上课时间不能为空", trigger: "change" }
    ],
    location: [
      { required: true, message: "上课地点不能为空", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 获取课程类型 */
function getClassTypesList() {
  getClassTypes().then(response => {
    classTypes.value = response.types || {};
  });
}

/** 重置表单 */
function reset() {
  form.value = {
    id: null,
    className: '',
    classType: null,
    duration: 60,
    classTime: null,
    location: '',
    status: 0,
    description: '',
    remarks: ''
  };
  handicraftClassRef.value?.resetFields();
}

/** 获取课程详情 */
function getClassDetail() {
  if (props.classId) {
    getHandicraftClass({ id: props.classId }).then(response => {
      form.value = response;
    });
  }
}

/** 提交表单 */
function submitForm() {
  handicraftClassRef.value.validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateHandicraftClass(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          handleClose();
          emit('success');
        });
      } else {
        addHandicraftClass(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          handleClose();
          emit('success');
        });
      }
    }
  });
}

/** 关闭弹窗 */
function handleClose() {
  reset();
  emit('update:visible', false);
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    reset();
    getClassTypesList();
    if (props.classId) {
      title.value = "修改手工课";
      getClassDetail();
    } else {
      title.value = "新增手工课";
    }
  }
});

onMounted(() => {
  getClassTypesList();
});
</script>
