package com.dfab.memberCheckin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.dfab.premiumMeal.entity.PremiumMeal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 入住会员管理实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("member_checkin")
@Schema(description = "入住会员管理实体类")
@Builder
public class MemberCheckin extends BaseEntity {
    /**
     * 入住记录的唯一标识，系统自动分配的 ID。
     * 使用JsonSerialize将Long转为String，避免前端精度丢失
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "入住记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户的微信 openId
     */
    @Schema(description = "用户的微信 openId", example = "wx1234567890")
    @Excel(name = "用户的微信 openId")
    private String openId;

    /**
     * 用户id userId
     */
    @Schema(description = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 用户电话
     */
    @Schema(description = "用户电话")
    @Excel(name = "用户电话")
    private String userPhone;

    /**
     * 会员姓名
     */
    @Schema(description = "会员姓名", example = "张三", required = true)
    @Excel(name = "会员姓名")
    private String memberName;

    /**
     * 会员手机号码
     */
    @Schema(description = "会员手机号码", example = "13800138000", required = true)
    @Excel(name = "会员手机号码")
    private String phoneNumber;

    /**
     * 入住日期
     */
    @Schema(description = "入住日期", example = "2024-01-01", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "入住日期")
    private LocalDate checkinDate;

    /**
     * 退住日期
     */
    @Schema(description = "退住日期", example = "2024-01-15")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "退住日期")
    private LocalDate checkoutDate;

    /**
     * 房间号
     */
    @Schema(description = "房间号", example = "A101")
    @Excel(name = "房间号")
    private String roomNumber;

    /**
     * 入住状态：0-已入住，1-已退住
     */
    @Schema(description = "入住状态：0-已入住，1-已退住", example = "0")
    @Excel(name = "入住状态")
    private Integer status;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "特殊需求说明")
    @Excel(name = "备注信息")
    private String remarks;

    /**
     * 紧急联系人姓名
     */
    @Schema(description = "紧急联系人姓名", example = "李四")
    @Excel(name = "紧急联系人姓名")
    private String emergencyContactName;

    /**
     * 紧急联系人电话
     */
    @Schema(description = "紧急联系人电话", example = "13900139000")
    @Excel(name = "紧急联系人电话")
    private String emergencyContactPhone;

    /**
     * 预产期
     */
    @Schema(description = "预产期", example = "2024-06-01")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "预产期")
    private LocalDate expectedDeliveryDate;

    /**
     * 入院待产日期
     */
    @Schema(description = "入院待产日期", example = "2024-05-25")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "入院待产日期")
    private LocalDate hospitalAdmissionDate;

    /**
     * 出院日期
     */
    @Schema(description = "出院日期", example = "2024-06-05")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "出院日期")
    private LocalDate dischargeDate;

    /**
     * 饮食禁忌（忌口信息），非数据库字段，用于展示
     */
    @Schema(description = "饮食禁忌", example = "不要香菜")
    @TableField(exist = false)
    @Excel(name = "饮食禁忌")
    private String allergyDetail;

    /**
     * 关联的车辆数量，非数据库字段，用于展示
     */
    @Schema(description = "关联的车辆数量", example = "2")
    @TableField(exist = false)
    @Excel(name = "关联的车辆数量")
    private Integer carCount;

    /**
     * 高档餐数量
     */
    @Schema(description = "高档餐数量", example = "2")
    @Excel(name = "高档餐数量")
    private Integer havePremiumMealNum;

    /**
     * 体重（单位：kg）
     */
    @Schema(description = "体重", example = "65.5")
    @Excel(name = "体重")
    private Double weight;

    /**
     * 产检医院
     */
    @Schema(description = "产检医院", example = "市人民医院")
    @Excel(name = "产检医院")
    private String prenatalHospital;

    /**
     * 推荐码
     */
    @Schema(description = "推荐码", example = "REF123456")
    @Excel(name = "推荐码")
    private String referralCode;

    /**
     * 中奖内容
     */
    @Schema(description = "中奖内容", example = "一等奖")
    @Excel(name = "中奖内容")
    private String prizeContent;

    /**
     * 高档餐数量
     */
    @Schema(description = "已使用高档餐数量", example = "2")
    @Excel(name = "已使用高档餐数量")
    @TableField(exist = false)
    private Integer haveUsePremiumMealNum;

    /**
     * 忌口投诉次数
     */
    @Schema(description = "忌口投诉次数", example = "2")
    @Excel(name = "忌口投诉次数")
    @TableField(exist = false)
    private Integer allergyComplaintCount;

    @TableField(exist = false)
    private List<PremiumMeal> haveUsePremiumMealList;
}
