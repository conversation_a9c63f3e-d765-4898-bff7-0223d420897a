package com.dfab.allergy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.allergy.entity.Allergy;
import com.dfab.allergy.mapper.AllergyMapper;
import com.dfab.allergy.service.AllergyService;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.businessLog.entity.BusinessLog;
import com.dfab.businessLog.service.BusinessLogService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.dfab.premiumMeal.service.PremiumMealService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户忌口 Service 实现类。
 */
@Service
public class AllergyServiceImpl extends ServiceImpl<AllergyMapper, Allergy> implements AllergyService {

    @Autowired
    private AppUserService appUserService;

    @Autowired
    private PremiumMealService premiumMealService;

    @Autowired
    @Lazy
    private MemberCheckinService memberCheckinService;

    @Autowired
    private BusinessLogService businessLogService;

    @Override
    public Allergy getAllergyByOpenId(String openId) {
        LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getOpenId, openId);
        lambdaQueryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(lambdaQueryWrapper);
        if(list != null && !list.isEmpty()){
            return list.get(0);
        }else{
            return null;
        }
    }

    @Override
    public List<Allergy> getAllergyListByOpenId(String openId) {
        LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getOpenId, openId);
        lambdaQueryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(lambdaQueryWrapper);
        return list;
    }

    @Override
    public Allergy create(Allergy allergy) {

        AppUser appUser = appUserService.getByOpenId(allergy.getOpenId());

       /* LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getOpenId, allergy.getOpenId());
        Allergy existingAllergy = baseMapper.selectOne(lambdaQueryWrapper);*/

//        allergy.setId(existingAllergy.getId());


//        if (existingAllergy != null) {
//            // 更新
//            allergy.setId(existingAllergy.getId());
//            allergy.setOpenId(null);
//            baseMapper.updateById(allergy);
//            return getById(existingAllergy.getId()); // 返回更新后的信息
//        } else {
        if (appUser != null) {
            allergy.setUserId(appUser.getId());
            if(StrUtil.isBlank(allergy.getUserName())){
                allergy.setUserName(appUser.getName());
            }
            if(StrUtil.isBlank(allergy.getUserPhone())){
                allergy.setUserPhone(appUser.getPhoneNumber());
            }
            List<MemberCheckin> allMemberCheckinsByOpenId = memberCheckinService.getAllMemberCheckinsByOpenId(appUser.getOpenId());
            if(CollUtil.isNotEmpty(allMemberCheckinsByOpenId)){
                if(StrUtil.isBlank(allergy.getRoomNumber())){
                    allergy.setRoomNumber(allMemberCheckinsByOpenId.get(0).getRoomNumber());
                }
                //会员id
                allergy.setMemberCheckinId(allMemberCheckinsByOpenId.get(0).getId());
            }
//                allergy.setUserName(appUser.getName());
//                allergy.setUserPhone(appUser.getPhoneNumber());
        }
        // 新增
        this.save(allergy);
        return allergy;

    }

    @Override
    public Allergy createByAdmin(Allergy allergy) {

       /* LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getUserId, allergy.getUserId());
        long count = this.count(lambdaQueryWrapper);
        if(count > 0) {
            throw new RuntimeException("该用户已存在忌口信息");
        }*/

        boolean save = this.save(allergy);
        return allergy;
    }

    @Override
    public Boolean removeByOpenId(String openId) {
        LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getOpenId, openId);
        Allergy existingAllergy = baseMapper.selectOne(lambdaQueryWrapper);
        if (existingAllergy != null) {
            return this.removeById(existingAllergy.getId());
        } else {
           throw new RuntimeException("忌口不存在");
        }
    }

    @Override
    public Allergy getAllergyByPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getUserPhone, phoneNumber);
        lambdaQueryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(lambdaQueryWrapper);
        if(list != null && !list.isEmpty()){
            return list.get(0);
        }else{
            return null;
        }
    }

    @Override
    public Allergy getAllergyByPhoneNumberOrOpenId(String phoneNumber, String openId) {
        // 优先通过openId查询
        if (openId != null && !openId.trim().isEmpty()) {
            Allergy allergy = getAllergyByOpenId(openId);
            if (allergy != null) {
                return allergy;
            }
        }

        // 如果通过openId没有找到，再通过手机号查询
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            return getAllergyByPhoneNumber(phoneNumber);
        }

        return null;
    }

    @Override
    public Allergy getAllergyByMemberId(Long memberId) {
        LambdaQueryWrapper<Allergy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Allergy::getMemberCheckinId, memberId);
        lambdaQueryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(lambdaQueryWrapper);
        if(list != null && !list.isEmpty()){
            return list.get(0);
        }else{
            return null;
        }


    }

    @Override
    public IPage<Allergy> page(Allergy allergy) {
        this.updateOut();
        int pageNum = allergy.getPageNum() == null ? 1 : allergy.getPageNum();
        int pageSize = allergy.getPageSize() == null ? 10 : allergy.getPageSize();
        Page<Allergy> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<Allergy> queryWrapper = new LambdaQueryWrapper<>();
        if (allergy.getOpenId() != null) {
            queryWrapper.eq(Allergy::getOpenId, allergy.getOpenId());
        }
        if (StrUtil.isNotBlank(allergy.getUserName())) {
            queryWrapper.like(Allergy::getUserName, allergy.getUserName());
        }
        if (StrUtil.isNotBlank(allergy.getRoomNumber())) {
            queryWrapper.like(Allergy::getRoomNumber, allergy.getRoomNumber());
        }
        if(null != allergy.getIsOut()){
            if(1 == allergy.getIsOut()){
                queryWrapper.eq(Allergy::getIsOut, allergy.getIsOut());
            }else{
                queryWrapper.and(wrapper -> wrapper.eq(Allergy::getIsOut, 0).or().isNull(Allergy::getIsOut));
            }

        }

        queryWrapper.exists("select 1 from member_checkin where delete_status = 0 and status in (0,3) and id = allergy.member_checkin_id");

        queryWrapper.orderByDesc(Allergy::getCreateTime);
        IPage<Allergy> result = this.page(page, queryWrapper);

        // 为每个忌口记录添加投诉次数统计
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            LambdaQueryWrapper<BusinessLog> businessLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
            businessLogLambdaQueryWrapper.eq(BusinessLog::getType, "allergyWrong");
            businessLogLambdaQueryWrapper.orderByDesc(BusinessLog::getCreateTime);
            // 查询投诉记录
            List<BusinessLog> list = businessLogService.list(businessLogLambdaQueryWrapper);


            for (Allergy allergyItem : result.getRecords()) {

                List<BusinessLog> complaintList = list.stream().filter(l -> l.getMemberCheckinId().equals(allergyItem.getMemberCheckinId())).toList();

                allergyItem.setComplaintCount(complaintList != null ? complaintList.size() : 0);
            }
        }

        return result;
    }


    //更新过期忌口
    public void updateOut() {
        LambdaQueryWrapper<Allergy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(queryWrapper);
        Map<Long, List<Allergy>> collect = list.stream().collect(Collectors.groupingBy(Allergy::getMemberCheckinId));
        for (Long key : collect.keySet()) {
            List<Allergy> allergyList = collect.get(key);
            if (null != key && allergyList.size() > 1) {
                List<Allergy> sortedList = allergyList.stream()
                        .sorted(Comparator.comparing(Allergy::getCreateTime).reversed())
                        .toList();

                for (int i = 1; i < sortedList.size(); i++) {
                    if(sortedList.get(i).getIsOut() != null && sortedList.get(i).getIsOut() == 1){
                        continue;
                    }
                    this.updateById(Allergy.builder().id(sortedList.get(i).getId()).isOut(1).build());
                }

            }
        }
    }

    @Override
    public List<Allergy> list(Allergy allergy) {
        LambdaQueryWrapper<Allergy> queryWrapper = new LambdaQueryWrapper<>();
        if (allergy.getOpenId() != null) {
            queryWrapper.eq(Allergy::getOpenId, allergy.getOpenId());
        }
        if (StrUtil.isNotBlank(allergy.getUserName())) {
            queryWrapper.like(Allergy::getUserName, allergy.getUserName());
        }
        if (StrUtil.isNotBlank(allergy.getRoomNumber())) {
            queryWrapper.like(Allergy::getRoomNumber, allergy.getRoomNumber());
        }


        if(null != allergy.getIsOut()){
            if(1 == allergy.getIsOut()){
                queryWrapper.eq(Allergy::getIsOut, allergy.getIsOut());
            }else{
                queryWrapper.and(wrapper -> wrapper.eq(Allergy::getIsOut, 0).or().isNull(Allergy::getIsOut));
            }

        }

        queryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(queryWrapper);

        // 为每个忌口记录添加投诉次数统计
        for (Allergy allergyItem : list) {
            if (allergyItem.getUserId() != null) {
                List<BusinessLog> complaintList = businessLogService.getBusinessLogListByMemberCheckinIdAndType(
                    allergyItem.getUserId(), "allergyWrong");
                allergyItem.setComplaintCount(complaintList != null ? complaintList.size() : 0);
            } else {
                allergyItem.setComplaintCount(0);
            }
        }

        return list;
    }

   /* @Override
    public List<Allergy> boardList() {
        this.updateOut();
        LambdaQueryWrapper<Allergy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq(Allergy::getIsOut, 0).or().isNull(Allergy::getIsOut));
        queryWrapper.orderByDesc(Allergy::getCreateTime);
        List<Allergy> list = this.list(queryWrapper);
        for (Allergy allergy : list) {
            if (null != allergy.getUserId()) {
                MemberCheckin memberCheckin = null;
                List<MemberCheckin> memberCheckinList = memberCheckinService.getByUserId(allergy.getUserId());
                if(memberCheckinList != null && !memberCheckinList.isEmpty()){
                    memberCheckin = memberCheckinList.get(0);
                }
                if (null == memberCheckin) {
                    List<MemberCheckin> memberCheckinList1 = memberCheckinService.getByRoomNumber(allergy.getRoomNumber());
                    if (memberCheckinList1 != null && !memberCheckinList1.isEmpty()) {
                        memberCheckin = memberCheckinList1.get(0);
                    }
                }
                if (null != memberCheckin) {
                    List<PremiumMeal> premiumMeals = premiumMealService.getByMemberCheckinId(memberCheckin.getId());
                    allergy.setHaveUsePremiumMealList(premiumMeals);
                    allergy.setHaveUsePremiumMealNum(premiumMeals.stream().mapToInt(PremiumMeal::getQuantity).sum());
                    allergy.setHavePremiumMealNum(memberCheckin.getHavePremiumMealNum());
                    if(CollUtil.isNotEmpty(premiumMeals)){
                        allergy.setConsumeTime(premiumMeals.get(0).getConsumeTime());
                    }
                }
            }
        }
        return list;
    }*/

    @Override
    public Boolean addComplaint(Long allergyId, String complaintContent) {
        if (allergyId == null) {
            throw new RuntimeException("忌口ID不能为空");
        }
        if (StrUtil.isBlank(complaintContent)) {
            throw new RuntimeException("投诉内容不能为空");
        }

        // 获取忌口信息
        Allergy allergy = this.getById(allergyId);
        if (allergy == null) {
            throw new RuntimeException("忌口信息不存在");
        }

        List<MemberCheckin> byRoomNumber = new ArrayList<>();
        if(null == allergy.getMemberCheckinId()){
            byRoomNumber = this.memberCheckinService.getByRoomNumber(allergy.getRoomNumber());
        }


        BusinessLog businessLog = BusinessLog.builder()
            .memberCheckinId(CollUtil.isNotEmpty(byRoomNumber) ? byRoomNumber.get(0).getId() : allergy.getMemberCheckinId())
            .openId(allergy.getOpenId())
            .detail(complaintContent)
            .businessId(allergyId)
            .type("allergyWrong")
            .build();

        return businessLogService.createByAdmin(businessLog) != null;
    }

    @Override
    public List<BusinessLog> getComplaintList(Long allergyId) {
        if (allergyId == null) {
            throw new RuntimeException("忌口ID不能为空");
        }

        // 获取忌口信息
        Allergy allergy = this.getById(allergyId);
        if (allergy == null) {
            throw new RuntimeException("忌口信息不存在");
        }
        List<Long> longsByAllergyId = getLongsByAllergyId(allergyId);
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusinessLog::getBusinessId, longsByAllergyId);
        queryWrapper.eq(BusinessLog::getType, "allergyWrong");
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        // 查询投诉记录
        return businessLogService.list(queryWrapper);
    }

    public List<Long> getLongsByAllergyId(Long allergyId) {
        Allergy allergy = this.getById(allergyId);
        LambdaQueryWrapper<Allergy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Allergy::getRoomNumber, allergy.getRoomNumber());
        List<Allergy> list = this.list(queryWrapper);
        if(list != null && !list.isEmpty()){
            return list.stream().map(Allergy::getId).collect(Collectors.toList());
        }else{
            return List.of();
        }
    }
}