package com.dfab.taskCenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 任务中心实体类
 * 通用任务管理，支持各种业务类型的任务
 */
@Data
@TableName("task_center")
@Schema(description = "任务中心实体类")
public class TaskCenter extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "任务 ID", example = "1")
    private Long id;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(min = 2, max = 100, message = "任务标题长度必须在2-100个字符之间")
    @Schema(description = "任务标题", example = "车牌处理", required = true)
    private String title;

    /**
     * 任务类型：1-车牌新增，2-车牌删除，3-离所处理，4-会员注册，5-预约处理，6-投诉处理等
     */
    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型", example = "1", required = true)
    private Integer taskType;

    /**
     * 任务状态：0-待处理，1-已处理
     */
    @Schema(description = "任务状态", example = "0")
    private Integer status = 0;

    /**
     * 任务描述（详细信息）
     */
    @Size(max = 1000, message = "任务描述长度不能超过1000个字符")
    @Schema(description = "任务描述", example = "会员张三新增车牌粤A12345，请前台及时处理")
    private String description;

    /**
     * 关键信息（核心业务数据，如车牌号、会员号、订单号等）
     */
    @Size(max = 100, message = "关键信息长度不能超过100个字符")
    @Schema(description = "关键信息", example = "粤A12345")
    private String keyInfo;

    /**
     * 关联业务ID（可以是车辆ID、会员ID、订单ID等）
     */
    @Schema(description = "关联业务ID", example = "1")
    private Long businessId;

    /**
     * 业务类型（car-车辆，member-会员，order-订单，complaint-投诉等）
     */
    @Size(max = 50, message = "业务类型长度不能超过50个字符")
    @Schema(description = "业务类型", example = "car")
    private String businessType;

    /**
     * 客户姓名
     */
    @Size(max = 50, message = "客户姓名长度不能超过50个字符")
    @Schema(description = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @Size(max = 20, message = "客户电话长度不能超过20个字符")
    @Schema(description = "客户电话", example = "***********")
    private String customerPhone;

    /**
     * 微信 openId
     */
    @Size(max = 100, message = "微信openId长度不能超过100个字符")
    @Schema(description = "微信 openId", example = "wx1234567890")
    private String openId;

    /**
     * 用户ID（用于数据隔离）
     */
    @Schema(description = "用户 ID", example = "1")
    private Long userId;

    /**
     * 用户ID（用于数据隔离）
     */
    @Schema(description = "会员 ID", example = "1")
    private Long memberCheckinId;

    /**
     * 扩展信息（JSON格式，存储额外的业务数据）
     */
    @Size(max = 2000, message = "扩展信息长度不能超过2000个字符")
    @Schema(description = "扩展信息", example = "{\"room_number\":\"201\",\"service_type\":\"月子护理\"}")
    private String extendInfo;

    /**
     * 处理人员
     */
    @Schema(description = "处理人员", example = "前台")
    private String processor;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processTime;

    /**
     * 处理备注
     */
    @Size(max = 500, message = "处理备注长度不能超过500个字符")
    @Schema(description = "处理备注", example = "已完成车牌登记")
    private String processRemark;

    /**
     * 优先级：1-低，2-中，3-高
     */
    @Schema(description = "优先级", example = "2")
    private Integer priority = 2;

    /**
     * 是否已推送：0-未推送，1-已推送
     */
    @Schema(description = "是否已推送", example = "0")
    private Integer isPushed = 0;

    /**
     * 推送次数
     */
    @Schema(description = "推送次数", example = "0")
    private Integer pushCount = 0;

    /**
     * 最后推送时间
     */
    @Schema(description = "最后推送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastPushTime;
}
