<template>
	<view class="calendar-picker">
		<!-- 日历头部 -->
		<view class="calendar-header">
			<view class="header-nav">
				<text class="nav-btn" @click="prevMonth">‹</text>
				<text class="current-month">{{ currentYear }}年{{ currentMonth }}月</text>
				<text class="nav-btn" @click="nextMonth">›</text>
			</view>
		</view>

		<!-- 星期标题 -->
		<view class="week-header">
			<text class="week-item" v-for="week in weekDays" :key="week">{{ week }}</text>
		</view>

		<!-- 日历主体 -->
		<view class="calendar-body">
			<view class="calendar-row" v-for="(week, weekIndex) in calendarDays" :key="weekIndex">
				<view 
					class="calendar-day" 
					v-for="(day, dayIndex) in week" 
					:key="dayIndex"
					:class="{
						'other-month': day.isOtherMonth,
						'disabled': day.isDisabled,
						'selected': day.isSelected,
						'today': day.isToday
					}"
					@click="selectDate(day)"
				>
					<text class="day-text">{{ day.day }}</text>
				</view>
			</view>
		</view>

		<!-- 已选择日期显示 -->
		<view class="selected-dates" v-if="selectedDates.length > 0">
			<view class="selected-title">已选择日期 ({{ selectedDates.length }}个):</view>
			<view class="selected-list">
				<view 
					class="selected-item" 
					v-for="(date, index) in selectedDates" 
					:key="index"
					@click="removeDate(date)"
				>
					<text class="date-text">{{ date }}</text>
					<text class="remove-btn">×</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="calendar-footer">
			<button class="clear-btn" @click="clearAll" v-if="selectedDates.length > 0">清空</button>
			<button class="confirm-btn" @click="confirm">确定</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

// Props
const props = defineProps({
	// 已选择的日期数组
	modelValue: {
		type: Array,
		default: () => []
	},
	// 最小可选日期
	minDate: {
		type: String,
		default: ''
	},
	// 最大可选日期
	maxDate: {
		type: String,
		default: ''
	}
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// 响应式数据
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth() + 1)
const selectedDates = ref([...props.modelValue])

// 星期标题
const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 格式化日期
const formatDate = (year, month, day) => {
	const m = month.toString().padStart(2, '0')
	const d = day.toString().padStart(2, '0')
	return `${year}-${m}-${d}`
}

// 解析日期字符串
const parseDate = (dateStr) => {
	const [year, month, day] = dateStr.split('-').map(Number)
	return new Date(year, month - 1, day)
}

// 检查日期是否被禁用
const isDateDisabled = (year, month, day) => {
	const dateStr = formatDate(year, month, day)
	const date = parseDate(dateStr)
	
	// 检查最小日期
	if (props.minDate) {
		const minDate = parseDate(props.minDate)
		if (date < minDate) return true
	}
	
	// 检查最大日期
	if (props.maxDate) {
		const maxDate = parseDate(props.maxDate)
		if (date > maxDate) return true
	}
	
	return false
}

// 检查是否是今天
const isToday = (year, month, day) => {
	const today = new Date()
	return year === today.getFullYear() && 
		   month === today.getMonth() + 1 && 
		   day === today.getDate()
}

// 生成日历数据
const calendarDays = computed(() => {
	const year = currentYear.value
	const month = currentMonth.value
	
	// 当月第一天
	const firstDay = new Date(year, month - 1, 1)
	// 当月最后一天
	const lastDay = new Date(year, month, 0)
	// 当月天数
	const daysInMonth = lastDay.getDate()
	// 第一天是星期几
	const firstDayWeek = firstDay.getDay()
	
	const days = []
	let currentWeek = []
	
	// 填充上个月的日期
	const prevMonth = month === 1 ? 12 : month - 1
	const prevYear = month === 1 ? year - 1 : year
	const prevMonthLastDay = new Date(prevYear, prevMonth, 0).getDate()
	
	for (let i = firstDayWeek - 1; i >= 0; i--) {
		const day = prevMonthLastDay - i
		currentWeek.push({
			day,
			fullDate: formatDate(prevYear, prevMonth, day),
			isOtherMonth: true,
			isDisabled: true,
			isSelected: false,
			isToday: false
		})
	}
	
	// 填充当月日期
	for (let day = 1; day <= daysInMonth; day++) {
		const fullDate = formatDate(year, month, day)
		const disabled = isDateDisabled(year, month, day)
		
		currentWeek.push({
			day,
			fullDate,
			isOtherMonth: false,
			isDisabled: disabled,
			isSelected: selectedDates.value.includes(fullDate),
			isToday: isToday(year, month, day)
		})
		
		// 每周7天，换行
		if (currentWeek.length === 7) {
			days.push(currentWeek)
			currentWeek = []
		}
	}
	
	// 填充下个月的日期
	const nextMonth = month === 12 ? 1 : month + 1
	const nextYear = month === 12 ? year + 1 : year
	let nextMonthDay = 1
	
	while (currentWeek.length < 7) {
		currentWeek.push({
			day: nextMonthDay,
			fullDate: formatDate(nextYear, nextMonth, nextMonthDay),
			isOtherMonth: true,
			isDisabled: true,
			isSelected: false,
			isToday: false
		})
		nextMonthDay++
	}
	
	if (currentWeek.length > 0) {
		days.push(currentWeek)
	}
	
	return days
})

// 选择日期
const selectDate = (day) => {
	if (day.isDisabled || day.isOtherMonth) return
	
	const dateStr = day.fullDate
	const index = selectedDates.value.indexOf(dateStr)
	
	if (index > -1) {
		// 取消选择
		selectedDates.value.splice(index, 1)
	} else {
		// 添加选择
		selectedDates.value.push(dateStr)
	}
	
	// 排序日期
	selectedDates.value.sort()
	
	// 触发更新
	emit('update:modelValue', selectedDates.value)
}

// 移除日期
const removeDate = (dateStr) => {
	const index = selectedDates.value.indexOf(dateStr)
	if (index > -1) {
		selectedDates.value.splice(index, 1)
		emit('update:modelValue', selectedDates.value)
	}
}

// 清空所有选择
const clearAll = () => {
	selectedDates.value = []
	emit('update:modelValue', selectedDates.value)
}

// 上一个月
const prevMonth = () => {
	if (currentMonth.value === 1) {
		currentYear.value--
		currentMonth.value = 12
	} else {
		currentMonth.value--
	}
}

// 下一个月
const nextMonth = () => {
	if (currentMonth.value === 12) {
		currentYear.value++
		currentMonth.value = 1
	} else {
		currentMonth.value++
	}
}

// 确定
const confirm = () => {
	emit('confirm', selectedDates.value)
}

// 监听外部传入的选择日期变化
watch(() => props.modelValue, (newVal) => {
	selectedDates.value = [...newVal]
}, { deep: true })

onMounted(() => {
	selectedDates.value = [...props.modelValue]
})
</script>

<style lang="scss" scoped>
.calendar-picker {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.calendar-header {
	margin-bottom: 20rpx;
}

.header-nav {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
}

.nav-btn {
	font-size: 36rpx;
	color: #8b5a2b;
	padding: 10rpx 20rpx;
	font-weight: bold;
}

.current-month {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.week-header {
	display: flex;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	margin-bottom: 10rpx;
}

.week-item {
	flex: 1;
	text-align: center;
	padding: 15rpx 0;
	font-size: 26rpx;
	color: #666;
	font-weight: bold;
}

.calendar-body {
	margin-bottom: 20rpx;
}

.calendar-row {
	display: flex;
}

.calendar-day {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 2rpx;
	border-radius: 8rpx;
	position: relative;
}

.calendar-day.other-month {
	opacity: 0.3;
}

.calendar-day.disabled {
	color: #ccc;
	background-color: #f9f9f9;
}

.calendar-day.selected {
	background-color: #8b5a2b;
	color: #fff;
}

.calendar-day.today {
	border: 2rpx solid #8b5a2b;
}

.calendar-day:not(.disabled):not(.other-month):active {
	background-color: #f0f0f0;
}

.day-text {
	font-size: 28rpx;
}

.selected-dates {
	margin-bottom: 20rpx;
	padding: 20rpx;
	background-color: #f8f5f2;
	border-radius: 8rpx;
}

.selected-title {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.selected-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.selected-item {
	display: flex;
	align-items: center;
	background-color: #8b5a2b;
	color: #fff;
	padding: 8rpx 15rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.date-text {
	margin-right: 8rpx;
}

.remove-btn {
	font-size: 28rpx;
	font-weight: bold;
	padding: 0 5rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.3);
	line-height: 1;
	width: 30rpx;
	height: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.calendar-footer {
	display: flex;
	gap: 20rpx;
}

.clear-btn, .confirm-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.clear-btn {
	background-color: #f0f0f0;
	color: #666;
}

.confirm-btn {
	background-color: #8b5a2b;
	color: #fff;
}
</style>
