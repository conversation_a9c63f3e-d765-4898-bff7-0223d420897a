/**
 * API配置文件
 * 用于管理不同环境的API地址
 */

// 获取当前环境
const ENV = process.env.NODE_ENV || 'development';

// 不同环境的基础URL
const BASE_URLS = {
  development: 'http://localhost:8089', // 本地开发环境
  test: 'http://localhost:8089',        // 测试环境
  production: 'http://localhost:8089'   // 生产环境
};

// 检查当前环境是否可以连接到API服务器
console.log(`当前环境: ${ENV}, API基础地址: ${BASE_URLS[ENV]}`);

// 当前环境的基础URL
const BASE_URL = BASE_URLS[ENV];

// 图片服务的固定地址（如果需要使用不同的服务器）
const IMAGE_SERVER_URL = 'https://xiayuxinzhu.cn';

// API路径
const API_PATHS = {
  cars: '/api/cars',
  users: '/api/users',
  files: '/api/files',
  appUser: '/api/appUser',
  allergy: '/api/allergy',
  mealReservation: '/api/mealReservation',
  universalReservation: '/api/universalReservation',
  handicraftClass: '/api/handicraftClass',
  customerSupplyRequest: '/api/customerSupplyRequest',
  images: '/api/files/previewResized'
};

// 导出完整的API URL
export const API = {
  // 车辆相关API
  cars: {
    list: `${BASE_URL}${API_PATHS.cars}`,
    add: `${BASE_URL}${API_PATHS.cars}`,
    createOrUpdate: `${BASE_URL}${API_PATHS.cars}/createOrUpdate`,
    getByOpenId: (openId) => `${BASE_URL}${API_PATHS.cars}/openId/${openId}`,
    getByIdAndOpenId: (id, openId) => `${BASE_URL}${API_PATHS.cars}/${id}/openId/${openId}`,
    update: (id) => `${BASE_URL}${API_PATHS.cars}/${id}`,
    delete: (id) => `${BASE_URL}${API_PATHS.cars}/${id}`,
    deleteByIdAndOpenId: (id, openId) => `${BASE_URL}${API_PATHS.cars}/${id}/openId/${openId}`
  },
  // 用户相关API
  users: {
    login: `${BASE_URL}${API_PATHS.users}/login`,
    info: `${BASE_URL}${API_PATHS.users}/info`
  },
  // AppUser相关API
  appUser: {
    getByOpenId: (openId) => `${BASE_URL}${API_PATHS.appUser}/${openId}`,
    addOrUpdate: `${BASE_URL}${API_PATHS.appUser}/addOrUpdate`,
    login: `${BASE_URL}${API_PATHS.appUser}/login`,
    info: `${BASE_URL}${API_PATHS.appUser}/info`
  },
  // 忌口信息相关API
  allergy: {
    getByOpenId: (openId) => `${BASE_URL}${API_PATHS.allergy}/${openId}`,
    createOrUpdate: `${BASE_URL}${API_PATHS.allergy}`,
    deleteByOpenId: (openId) => `${BASE_URL}${API_PATHS.allergy}/${openId}`
  },
  // 餐食预约相关API
  mealReservation: {
    create: `${BASE_URL}${API_PATHS.mealReservation}`,
    list: `${BASE_URL}${API_PATHS.mealReservation}/list`,
    get: `${BASE_URL}${API_PATHS.mealReservation}/get`,
    update: `${BASE_URL}${API_PATHS.mealReservation}/update`,
    remove: `${BASE_URL}${API_PATHS.mealReservation}/remove`,
    today: `${BASE_URL}${API_PATHS.mealReservation}/today`
  },
  // 通用预约相关API
  universalReservation: {
    list: `${BASE_URL}${API_PATHS.universalReservation}/list`,
    get: `${BASE_URL}${API_PATHS.universalReservation}/get`,
    save: `${BASE_URL}${API_PATHS.universalReservation}/save`,
    tasting: `${BASE_URL}${API_PATHS.universalReservation}/tasting`,
    roomViewing: `${BASE_URL}${API_PATHS.universalReservation}/room-viewing`,
    cancel: `${BASE_URL}${API_PATHS.universalReservation}/cancel`,
    delete: `${BASE_URL}${API_PATHS.universalReservation}/delete`,
    stats: `${BASE_URL}${API_PATHS.universalReservation}/stats`
  },
  // 手工课相关API
  handicraftClass: {
    list: `${BASE_URL}${API_PATHS.handicraftClass}/list`,
    get: `${BASE_URL}${API_PATHS.handicraftClass}/get`,
    join: `${BASE_URL}${API_PATHS.handicraftClass}/join`,
    cancel: `${BASE_URL}${API_PATHS.handicraftClass}/cancel`,
    myClasses: `${BASE_URL}${API_PATHS.handicraftClass}/my-classes`,
    rate: `${BASE_URL}${API_PATHS.handicraftClass}/rate`,
    stats: `${BASE_URL}${API_PATHS.handicraftClass}/stats`
  },
  // 客户用品补充需求相关API
  customerSupplyRequest: {
    list: `${BASE_URL}${API_PATHS.customerSupplyRequest}/list`,
    get: `${BASE_URL}${API_PATHS.customerSupplyRequest}/get`,
    add: `${BASE_URL}${API_PATHS.customerSupplyRequest}/add`,
    update: `${BASE_URL}${API_PATHS.customerSupplyRequest}/update`,
    delete: `${BASE_URL}${API_PATHS.customerSupplyRequest}/delete`,
    stats: `${BASE_URL}${API_PATHS.customerSupplyRequest}/stats`
  },
  // 图片相关API - 使用固定的图片服务器地址
  images: {
    baseUrl: `${IMAGE_SERVER_URL}${API_PATHS.images}`,
    getImageUrl: (imageId, size = 'medium') => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/${size}`,
    small: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/small`,
    medium: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/medium`,
    large: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/large`
  }
};

/**
 * 统一处理API请求
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export const request = (options) => {
  // 获取token
  const token = uni.getStorageSync('token') || '';

  // 默认请求头
  const header = {
    'Content-Type': 'application/json',
    ...options.header
  };

  // 如果有token，添加到请求头
  if (token) {
    header['Authorization'] = `Bearer ${token}`;
  }

  // 显示加载中
  if (options.showLoading !== false) {
    uni.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    });
  }

  // 打印请求信息，方便调试
  console.log(`API请求: ${options.method || 'GET'} ${options.url}`, options.data || '');

  return new Promise((resolve, reject) => {
    uni.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data,
      header,
      success: (res) => {
        // 隐藏加载中
        if (options.showLoading !== false) {
          uni.hideLoading();
        }

        // 打印响应信息，方便调试
        console.log(`API响应: ${options.url}`, res.statusCode, res.data);

        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        }
        // 未授权
        else if (res.statusCode === 401) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });

          // 清除登录信息
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');

          // 跳转到登录页
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);

          reject(new Error('未授权'));
        }
        // 其他错误
        else {
          let errorMsg = '请求失败';

          // 尝试从响应中获取错误信息
          if (typeof res.data === 'string') {
            errorMsg = res.data;
          } else if (res.data && res.data.message) {
            errorMsg = res.data.message;
          } else if (res.data && res.data.error) {
            errorMsg = res.data.error;
          }

          // 显示错误信息
          if (options.showError !== false) {
            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 3000
            });
          }

          reject(new Error(errorMsg));
        }
      },
      fail: (err) => {
        // 隐藏加载中
        if (options.showLoading !== false) {
          uni.hideLoading();
        }

        // 打印错误信息，方便调试
        console.error(`API请求失败: ${options.url}`, err);

        // 检查是否是网络连接问题
        const isNetworkError = err.errMsg && (
          err.errMsg.includes('timeout') ||
          err.errMsg.includes('fail') ||
          err.errMsg.includes('network')
        );

        // 显示错误信息
        if (options.showError !== false) {
          uni.showToast({
            title: isNetworkError ? '网络连接失败，请检查网络设置' : '网络异常，请稍后重试',
            icon: 'none',
            duration: 3000
          });
        }

        reject(err);
      },
      complete: () => {
        // 确保在complete回调中也隐藏loading，防止某些情况下loading不消失
        if (options.showLoading !== false) {
          uni.hideLoading();
        }
      }
    });
  });
};
