package com.dfab.taskCenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dfab.taskCenter.entity.PushRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 推送记录Mapper接口
 */
@Mapper
public interface PushRecordMapper extends BaseMapper<PushRecord> {

    /**
     * 根据任务ID查询推送记录
     * @param taskId 任务ID
     * @return 推送记录列表
     */
    List<PushRecord> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据推送状态查询推送记录
     * @param status 推送状态
     * @return 推送记录列表
     */
    List<PushRecord> selectByStatus(@Param("status") Integer status);

    /**
     * 根据接收者openId查询推送记录
     * @param receiverOpenId 接收者openId
     * @return 推送记录列表
     */
    List<PushRecord> selectByReceiverOpenId(@Param("receiverOpenId") String receiverOpenId);

    /**
     * 查询推送失败的记录
     * @return 推送失败的记录列表
     */
    List<PushRecord> selectFailedRecords();

    /**
     * 查询需要重试的推送记录
     * @param maxRetryCount 最大重试次数
     * @return 需要重试的推送记录列表
     */
    List<PushRecord> selectRetryRecords(@Param("maxRetryCount") Integer maxRetryCount);

    /**
     * 统计今日推送数量
     * @return 今日推送数量
     */
    int countTodayPushes();

    /**
     * 统计推送成功率
     * @return 推送成功率
     */
    Double calculateSuccessRate();

    /**
     * 根据推送类型统计数量
     * @param pushType 推送类型
     * @return 推送数量
     */
    int countByPushType(@Param("pushType") Integer pushType);
}
