package com.dfab.universalReservation.enums;

/**
 * 预约类型枚举
 */
public enum ReservationTypeEnum {

    CAR(1, "用车预约", "用于预约接送车辆服务"),
    TASTING(2, "试餐预约", "用于预约试吃体验"),
    ROOM_VIEWING(3, "看房预约", "用于预约参观房间"),
    OTHER(4, "其他预约", "其他类型的预约服务");

    private final Integer code;
    private final String name;
    private final String description;

    ReservationTypeEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 枚举值
     */
    public static ReservationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReservationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     * @param code 代码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        ReservationTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知类型";
    }
}
