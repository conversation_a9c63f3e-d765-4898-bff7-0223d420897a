<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          @input="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格型号" prop="specification">
        <el-input
          v-model="queryParams.specification"
          placeholder="请输入规格型号"
          clearable
          @input="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>

        <el-button
            type="warning"
            plain
            icon="Warning"
            size="small"
            @click="getList"
        >刷新预警</el-button>
      </el-form-item>
    </el-form>



    <el-table v-loading="loading" :data="currentPageData">
      <el-table-column type="index" width="50" />
      <el-table-column label="物料名称" prop="materialName" />
      <el-table-column label="规格型号" prop="specification" />
      <el-table-column label="单位" prop="unit" />
      <el-table-column label="单价" prop="unitPrice">
        <template #default="scope">
          ¥{{ scope.row.unitPrice }}
        </template>
      </el-table-column>
      <el-table-column label="当前库存" prop="quantity">
        <template #default="scope">
          <el-tag :type="getQuantityTagType(scope.row.quantity)" effect="dark">
            {{ scope.row.quantity || 0 }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提醒数量" prop="warnQuantity">
        <template #default="scope">
          {{ scope.row.warnQuantity || '未设置' }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180" />
<!--      <el-table-column label="预警级别" width="100">
        <template #default="scope">
          <el-tag :type="getWarnLevelType(scope.row.quantity, scope.row.warnQuantity)" effect="dark">
            {{ getWarnLevelText(scope.row.quantity, scope.row.warnQuantity) }}
          </el-tag>
        </template>
      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { warnList } from '@/api/business/material'

const loading = ref(false)
const showSearch = ref(true)
const total = ref(0)
const warnListData = ref([])
const filteredData = ref([])

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  materialName: '',
  specification: ''
})

// 计算当前页显示的数据
const currentPageData = computed(() => {
  const start = (queryParams.pageNum - 1) * queryParams.pageSize
  const end = start + queryParams.pageSize
  return filteredData.value.slice(start, end)
})

/** 查询物料预警列表 */
function getList() {
  loading.value = true
  warnList().then(response => {
    console.log('预警列表响应:', response)
    
    let data = []
    if (response && response.data) {
      data = response.data
    } else if (Array.isArray(response)) {
      data = response
    }
    
    warnListData.value = data
    applyFilter()
    loading.value = false
  }).catch(error => {
    console.error('获取预警列表失败:', error)
    ElMessage.error('获取预警列表失败')
    warnListData.value = []
    filteredData.value = []
    total.value = 0
    loading.value = false
  })
}

/** 应用筛选条件 */
function applyFilter() {
  let data = [...warnListData.value]
  
  // 根据物料名称筛选
  if (queryParams.materialName) {
    data = data.filter(item => 
      item.materialName && item.materialName.includes(queryParams.materialName)
    )
  }
  
  // 根据规格型号筛选
  if (queryParams.specification) {
    data = data.filter(item => 
      item.specification && item.specification.includes(queryParams.specification)
    )
  }
  
  filteredData.value = data
  total.value = data.length
  
  // 如果当前页超出范围，重置到第一页
  const maxPage = Math.ceil(total.value / queryParams.pageSize)
  if (queryParams.pageNum > maxPage && maxPage > 0) {
    queryParams.pageNum = 1
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  applyFilter()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.materialName = ''
  queryParams.specification = ''
  queryParams.pageNum = 1
  applyFilter()
}

/** 分页处理 */
function handlePagination() {
  // 当分页参数改变时，重新应用筛选
  applyFilter()
}

/** 获取库存数量标签类型 */
function getQuantityTagType(quantity) {
  const qty = quantity || 0
  if (qty === 0) return 'danger'
  if (qty <= 5) return 'warning'
  if (qty <= 10) return 'info'
  return 'success'
}

/** 获取预警级别类型 */
function getWarnLevelType(quantity, warnQuantity) {
  const qty = quantity || 0
  const warn = warnQuantity || 0

  if (qty === 0) return 'danger'
  if (warn > 0 && qty <= warn) return 'warning'
  return 'success'
}

/** 获取预警级别文本 */
function getWarnLevelText(quantity, warnQuantity) {
  const qty = quantity || 0
  const warn = warnQuantity || 0

  if (qty === 0) return '缺货'
  if (warn > 0 && qty <= warn) return '预警'
  return '正常'
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
