package com.dfab.businessLog.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.dfab.businessLog.entity.BusinessLog;
import com.dfab.businessLog.service.BusinessLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务关键日志 Controller 类，提供业务日志信息的增删改查接口。
 */
@RestController
@RequestMapping("/api/businessLog")
@Tag(name = "BusinessLogController", description = "小程序业务日志接口，提供对业务日志信息的增删改查功能")
public class BusinessLogController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private BusinessLogService businessLogService;

    /**
     * 根据 openId 获取业务日志列表
     * @return 业务日志列表
     */
    @Operation(summary = "获取用户业务日志列表", description = "通过用户的微信 openId 获取业务日志列表")
    @PostMapping("/list")
    @Log(title = "业务日志-获取用户业务日志列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<BusinessLog> getBusinessLogList() {
        String openId = (String) request.getAttribute("openId");
        return businessLogService.getBusinessLogListByOpenId(openId);
    }

    /**
     * 根据 openId 和类型获取业务日志列表
     * @param businessLog 查询条件，包含类型信息
     * @return 业务日志列表
     */
    @Operation(summary = "根据类型获取用户业务日志列表", description = "通过用户的微信 openId 和日志类型获取业务日志列表")
    @PostMapping("/listByType")
    @Log(title = "业务日志-根据类型获取用户业务日志列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<BusinessLog> getBusinessLogListByType(@Parameter(description = "查询条件", required = true) @RequestBody BusinessLog businessLog) {
        String openId = (String) request.getAttribute("openId");
        return businessLogService.getBusinessLogListByOpenIdAndType(openId, businessLog.getType());
    }

    /**
     * 根据 ID 获取业务日志信息
     * @param businessLog 包含 ID 的业务日志对象
     * @return 业务日志信息
     */
    @Operation(summary = "根据 ID 获取业务日志信息", description = "通过指定的业务日志记录 ID 查询对应的日志信息")
    @PostMapping("/get")
    @Log(title = "业务日志-根据 ID 获取业务日志信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public BusinessLog getBusinessLogById(@Parameter(description = "包含 ID 的业务日志对象", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.getById(businessLog.getId());
    }

    /**
     * 创建业务日志
     * @param businessLog 业务日志信息
     * @return 创建的业务日志
     */
    @Operation(summary = "创建业务日志", description = "创建新的业务日志记录")
    @PostMapping("/create")
    @Log(title = "业务日志-创建业务日志", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public BusinessLog createBusinessLog(@Parameter(description = "业务日志信息", required = true) @Valid @RequestBody BusinessLog businessLog) {
        String openId = (String) request.getAttribute("openId");
        businessLog.setOpenId(openId);
        return businessLogService.create(businessLog);
    }

    /**
     * 更新业务日志
     * @param businessLog 业务日志信息
     * @return 更新后的业务日志
     */
    @Operation(summary = "更新业务日志", description = "更新现有的业务日志记录")
    @PostMapping("/update")
    @Log(title = "业务日志-更新业务日志", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public BusinessLog updateBusinessLog(@Parameter(description = "业务日志信息", required = true) @Valid @RequestBody BusinessLog businessLog) {
        String openId = (String) request.getAttribute("openId");
        businessLog.setOpenId(openId);
        return businessLogService.updateByOpenIdAndId(businessLog);
    }

    /**
     * 删除业务日志
     * @param businessLog 包含 ID 的业务日志对象
     * @return 是否删除成功
     */
    @Operation(summary = "删除业务日志", description = "删除指定的业务日志记录")
    @PostMapping("/delete")
    @Log(title = "业务日志-删除业务日志", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
    public Boolean deleteBusinessLog(@Parameter(description = "包含 ID 的业务日志对象", required = true) @RequestBody BusinessLog businessLog) {
        String openId = (String) request.getAttribute("openId");
        return businessLogService.removeByOpenIdAndId(openId, businessLog.getId());
    }
}
