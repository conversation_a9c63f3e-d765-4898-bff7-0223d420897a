user  www www;
worker_processes auto;
error_log  /www/wwwlogs/nginx_error.log  crit;
pid        /www/server/nginx/logs/nginx.pid;
worker_rlimit_nofile 51200;

stream {
    log_format tcp_format '$time_local|$remote_addr|$protocol|$status|$bytes_sent|$bytes_received|$session_time|$upstream_addr|$upstream_bytes_sent|$upstream_bytes_received|$upstream_connect_time';

    access_log /www/wwwlogs/tcp-access.log tcp_format;
    error_log /www/wwwlogs/tcp-error.log;
    include /www/server/panel/vhost/nginx/tcp/*.conf;
}

events {
    use epoll;
    worker_connections 51200;
    multi_accept on;
}

http {
    include       mime.types;
    #include luawaf.conf;
    include proxy.conf;
    lua_package_path "/www/server/nginx/lib/lua/?.lua;;";

    default_type  application/octet-stream;

    server_names_hash_bucket_size 512;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 50m;

    sendfile   on;
    tcp_nopush on;

    keepalive_timeout 60;

    tcp_nodelay on;

    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_read_timeout 300;
    fastcgi_buffer_size 64k;
    fastcgi_buffers 4 64k;
    fastcgi_busy_buffers_size 128k;
    fastcgi_temp_file_write_size 256k;
    fastcgi_intercept_errors on;

    gzip on;
    gzip_min_length  1k;
    gzip_buffers     4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 2;
    gzip_types     text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/json image/jpeg image/gif image/png font/ttf font/otf image/svg+xml application/xml+rss text/x-js;
    gzip_vary on;
    gzip_proxied   expired no-cache no-store private auth;
    gzip_disable   "MSIE [1-6]\.";

    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    server_tokens off;
    access_log off;

    # HTTPS Server for xiayuxinzhu.cn
    server {
        listen 443 ssl;
        server_name xiayuxinzhu.cn;

        ssl_certificate /www/server/nginx/conf/ssl/xiayuxinzhu.cn_bundle.pem;
        ssl_certificate_key /www/server/nginx/conf/ssl/xiayuxinzhu.cn.key;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5:!3DES;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        client_max_body_size 8000m;

        location / {
            root /www/wwwroot/ruoyi/dist;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /prod-api {
            rewrite ^/prod-api/(.*)$ /$1 break;
            proxy_pass http://localhost:8089;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
        }

         # API 路径反向代理
        location /api/ {
            proxy_pass http://localhost:8089/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            proxy_buffering off;
        }

    }

    # HTTP to HTTPS Redirect for xiayuxinzhu.cn
    server {
        listen 80;
        server_name xiayuxinzhu.cn;

        return 301 https://xiayuxinzhu.cn$request_uri;
    }

    # Existing servers
    server {
        listen       8082;
        server_name  test;
        client_max_body_size 8000m;

        location / {
            root  /www/wwwroot/ruoyi/dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /prod-api {
            rewrite  ^/prod-api/(.*)$ /$1 break;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            proxy_pass http://localhost:8089;
        }
    }

    server {
        listen       80;
        server_name  test2;
        client_max_body_size 8000m;

        location / {
            root  /www/wwwroot/ruoyi/dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /prod-api {
            rewrite  ^/prod-api/(.*)$ /$1 break;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            proxy_pass http://localhost:8089;
        }
    }

    include /www/server/panel/vhost/nginx/*.conf;
}