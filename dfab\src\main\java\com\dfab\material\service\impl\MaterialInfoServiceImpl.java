package com.dfab.material.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.material.dto.MemberPackageDto;
import com.dfab.material.dto.MemberPackagePayResult;
import com.dfab.material.entity.Inventory;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.entity.MaterialInfo;
import com.dfab.material.mapper.MaterialInfoMapper;
import com.dfab.material.service.InventoryRecordService;
import com.dfab.material.service.InventoryService;
import com.dfab.material.service.MaterialInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料信息Service实现类
 */
@Service
@Slf4j
public class MaterialInfoServiceImpl extends ServiceImpl<MaterialInfoMapper, MaterialInfo> implements MaterialInfoService {

    @Autowired
    private InventoryRecordService inventoryRecordService;

    @Autowired
    private InventoryService inventoryService;

    @Override
    public Boolean updateMemberPackages(MemberPackageDto memberPackageDto) {

        //更新数量
        memberPackageDto.getPackages().stream().filter(p -> null != p.getMaterialId()).forEach(pa -> {
            this.updateById(MaterialInfo.builder().id(pa.getId()).packageQuantity(pa.getPackageQuantity()).build());
        });


        memberPackageDto.getPackages().stream().filter(p -> null == p.getMaterialId()).forEach(packageDto -> {
            packageDto.setMaterialId(packageDto.getId());
            packageDto.setMemberId(memberPackageDto.getMemberId());
            packageDto.setId(null);
            packageDto.setModifier(null);
            packageDto.setModifyTime(null);
            packageDto.setCreateTime(null);
            packageDto.setCreator(null);
            this.save(packageDto);

        });
        return true;
    }

    @Override
    public List<MaterialInfo> memberList(MaterialInfo materialInfo) {
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialInfo::getMemberId,materialInfo.getMemberId());
        List<MaterialInfo> list = this.list(queryWrapper);

        if(CollUtil.isEmpty(list)){
            return list;
        }

        // 提取 物料id 成新的 List
        List<Long> idList = list.stream()
                .map(MaterialInfo::getMaterialId)
                .toList();

        LambdaQueryWrapper<Inventory> inventoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        inventoryLambdaQueryWrapper.in(Inventory::getMaterialId,idList);
        List<Inventory> inventoryList = inventoryService.list(inventoryLambdaQueryWrapper);

        LambdaQueryWrapper<InventoryRecord> inventoryRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        inventoryRecordLambdaQueryWrapper.in(InventoryRecord::getMaterialId,idList);
        inventoryRecordLambdaQueryWrapper.eq(InventoryRecord::getMemberCheckinId,materialInfo.getMemberId());
        List<InventoryRecord> inventoryRecords = inventoryRecordService.list(inventoryRecordLambdaQueryWrapper);
        Map<Long, List<InventoryRecord>> groupedByMaterialId = inventoryRecords.stream()
                .collect(Collectors.groupingBy(
                        InventoryRecord::getMaterialId,
                        Collectors.toList()
                ));
        // 第五步：遍历原始 materialInfoList，并在每项中获取对应的 inventoryRecords
        for (MaterialInfo materialInfo1 : list) {
            Long materialId = materialInfo1.getMaterialId();

            inventoryList.stream().filter(i -> i.getMaterialId().equals(materialId)).findFirst().ifPresent(inventory -> {
                materialInfo1.setQuantity(inventory.getQuantity());
            });

            // 从分组后的 map 中取出对应 materialId 的记录
            List<InventoryRecord> records = groupedByMaterialId.get(materialId);
            // 初始库存为 0
            Long totalStock = 0L;
            if (records != null && !records.isEmpty()) {
                if (records != null && !records.isEmpty()) {
                    for (InventoryRecord record : records) {
                        if (record.getRecordType() == 1) {
                            totalStock -= record.getQuantity(); // 入库：减
                        } else if (record.getRecordType() == 2) {
                            totalStock += record.getQuantity(); // 出库：加
                        }
                    }
                }
            }

            materialInfo1.setMemberHaveUseNum(totalStock);
            //需要补钱
            if(totalStock.intValue() > materialInfo1.getPackageQuantity()){
                materialInfo1.setMemberNeedPay(new BigDecimal(totalStock.intValue() - materialInfo1.getPackageQuantity()).multiply(new BigDecimal(materialInfo1.getUnitPrice())).setScale(2, RoundingMode.HALF_UP));
            }
        }


        return list;
    }

    @Override
    public MemberPackagePayResult memberListPay(MaterialInfo materialInfo) {
        final BigDecimal[] needPay = {BigDecimal.ZERO};
        final BigDecimal[] allPay = {BigDecimal.ZERO};
        final BigDecimal[] packagePay = {BigDecimal.ZERO};
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialInfo::getMemberId,materialInfo.getMemberId());
        List<MaterialInfo> list = this.list(queryWrapper);



        // 提取 物料id 成新的 List
        List<Long> idList = list.stream()
                .map(MaterialInfo::getMaterialId)
                .toList();
        LambdaQueryWrapper<InventoryRecord> inventoryRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        inventoryRecordLambdaQueryWrapper.in(InventoryRecord::getMaterialId,idList);
        inventoryRecordLambdaQueryWrapper.eq(InventoryRecord::getMemberCheckinId,materialInfo.getMemberId());
        List<InventoryRecord> inventoryRecords = inventoryRecordService.list(inventoryRecordLambdaQueryWrapper);
        Map<Long, List<InventoryRecord>> groupedByMaterialId = inventoryRecords.stream()
                .collect(Collectors.groupingBy(
                        InventoryRecord::getMaterialId,
                        Collectors.toList()
                ));
        // 第五步：遍历原始 materialInfoList，并在每项中获取对应的 inventoryRecords
        for (MaterialInfo materialInfo1 : list) {
            Long materialId = materialInfo1.getMaterialId();

            // 从分组后的 map 中取出对应 materialId 的记录
            List<InventoryRecord> records = groupedByMaterialId.get(materialId);
            // 初始库存为 0
            Long totalStock = 0L;
            if (records != null && !records.isEmpty()) {
                if (records != null && !records.isEmpty()) {
                    for (InventoryRecord record : records) {
                        if (record.getRecordType() == 1) {
                            totalStock -= record.getQuantity(); // 入库：减
                        } else if (record.getRecordType() == 2) {
                            totalStock += record.getQuantity(); // 出库：加
                        }
                    }
                }
            }

            materialInfo1.setMemberHaveUseNum(totalStock);
            //需要补钱
            if(totalStock.intValue() > materialInfo1.getPackageQuantity()){
                BigDecimal bigDecimal = new BigDecimal(totalStock.intValue() - materialInfo1.getPackageQuantity()).multiply(new BigDecimal(materialInfo1.getUnitPrice())).setScale(2, RoundingMode.HALF_UP);
                materialInfo1.setMemberNeedPay(bigDecimal);
                needPay[0] = needPay[0].add(bigDecimal);

            }
            //套餐内金额
            if(totalStock > 0){
                BigDecimal bigDecimal = new BigDecimal(totalStock).multiply(new BigDecimal(materialInfo1.getUnitPrice())).setScale(2, RoundingMode.HALF_UP);
                //套餐内金额
                BigDecimal packageBigDecimal = new BigDecimal(materialInfo1.getPackageQuantity() ).multiply(new BigDecimal(materialInfo1.getUnitPrice())).setScale(2, RoundingMode.HALF_UP);
                materialInfo1.setPackagePay(totalStock - materialInfo1.getPackageQuantity() < 0 ? packageBigDecimal : packageBigDecimal);
                materialInfo1.setTruePay(bigDecimal);
                packagePay[0] = packagePay[0].add(totalStock - materialInfo1.getPackageQuantity() < 0 ? packageBigDecimal : packageBigDecimal);
                allPay[0] = allPay[0].add(bigDecimal);
            }
        }

        groupedByMaterialId.forEach((materialId, inventoryRecordList) -> {
            if(!idList.contains(materialId)){
                MaterialInfo unInPack = this.getById(materialId);

                if (null != unInPack) {
                    unInPack.setPackageQuantity(0);
                    // 从分组后的 map 中取出对应 materialId 的记录
                    List<InventoryRecord> records = groupedByMaterialId.get(materialId);
                    // 初始库存为 0
                    Long totalStock = 0L;
                    if (records != null && !records.isEmpty()) {
                        if (records != null && !records.isEmpty()) {
                            for (InventoryRecord record : records) {
                                if (record.getRecordType() == 1) {
                                    totalStock -= record.getQuantity(); // 入库：减
                                } else if (record.getRecordType() == 2) {
                                    totalStock += record.getQuantity(); // 出库：加
                                }
                            }
                        }
                    }
                    if(totalStock.intValue() > 0){
                        BigDecimal bigDecimal = new BigDecimal(totalStock).multiply(new BigDecimal(unInPack.getUnitPrice())).setScale(2, RoundingMode.HALF_UP);
                        unInPack.setMemberNeedPay(bigDecimal);
                        unInPack.setMemberHaveUseNum(totalStock);
                        needPay[0] = needPay[0].add(bigDecimal);
                        allPay[0] = allPay[0].add(bigDecimal);
                    }
                    list.add(unInPack);
                }

            }
        });
        return MemberPackagePayResult.builder().packages(list).needPay(needPay[0]).packagePay(packagePay[0]).truePay(allPay[0]).build();

    }

    @Override
    public MaterialInfo getByMaterialId(Long materialId) {
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialInfo::getMemberId,materialId);
        List<MaterialInfo> list = this.list(queryWrapper);
        if(CollUtil.isNotEmpty(list)){
            return list.get(0);
        }else{
            return null;
        }
    }
}