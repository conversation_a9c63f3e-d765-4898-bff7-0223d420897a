package com.dfab.appUser.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.mapper.AppUserMapper;
import com.dfab.appUser.service.AppUserService;
import org.springframework.stereotype.Service;

@Service
public class AppUserServiceImpl extends ServiceImpl<AppUserMapper, AppUser> implements AppUserService {

    /*@Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private WechatUtil wechatUtil;*/

    @Override
    public AppUser getByOpenId(String openId) {
        if (openId == null) {
            throw new RuntimeException("openId is null");
        }
        LambdaQueryWrapper<AppUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AppUser::getOpenId, openId);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public AppUser createOrUpdateAppUser(AppUser appUser) {
        LambdaQueryWrapper<AppUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AppUser::getOpenId, appUser.getOpenId());
        AppUser existingUser = baseMapper.selectOne(lambdaQueryWrapper);

        if (existingUser != null) {
            // 更新用户信息
            appUser.setId(existingUser.getId());
            baseMapper.updateById(appUser);
            return getById(existingUser.getId()); // 返回更新后的用户信息
        } else {
            // 新增用户
            appUser.setType("user");
            baseMapper.insert(appUser);
            return appUser;
        }
    }

    @Override
    public AppUser getByPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<AppUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AppUser::getPhoneNumber, phoneNumber);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

   /* @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 1. 处理手机号密码登录
        if (StringUtils.hasText(loginRequest.getPhoneNumber()) && StringUtils.hasText(loginRequest.getPassword())) {
            return handlePhoneLogin(loginRequest);
        }

        // 2. 处理微信登录
        if (StringUtils.hasText(loginRequest.getCode())) {
            return handleWechatLogin(loginRequest);
        }

        // 3. 如果都不满足，抛出异常
        throw new RuntimeException("登录参数不正确，请提供手机号和密码或微信登录code");
    }

    *//**
     * 处理手机号密码登录
     * @param loginRequest 登录请求
     * @return 登录响应
     *//*
    private LoginResponse handlePhoneLogin(LoginRequest loginRequest) {
        // 根据手机号查询用户
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getPhoneNumber, loginRequest.getPhoneNumber());
        AppUser user = baseMapper.selectOne(queryWrapper);

        // 如果用户不存在，抛出异常
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // TODO: 实际项目中应该对密码进行加密处理和验证
        // 这里简化处理，假设密码是"123456"
        if (!"123456".equals(loginRequest.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 更新最后访问时间
        user.setLastAccessTime(new Date());
        baseMapper.updateById(user);

        // 生成token
        String token = jwtUtil.generateToken(user.getOpenId());

        // 返回登录响应
        return LoginResponse.of(token, user);
    }

    *//**
     * 处理微信登录
     * @param loginRequest 登录请求
     * @return 登录响应
     *//*
    private LoginResponse handleWechatLogin(LoginRequest loginRequest) {
        try {
            // 获取微信登录凭证
            Map<String, String> sessionData = wechatUtil.getSessionKeyAndOpenId(loginRequest.getCode());
            String openId = sessionData.get("openid");
            String sessionKey = sessionData.get("session_key");

            if (openId == null || sessionKey == null) {
                throw new RuntimeException("获取微信登录凭证失败");
            }

            // 获取手机号
            String phoneNumber = null;
            if (StringUtils.hasText(loginRequest.getEncryptedData()) && StringUtils.hasText(loginRequest.getIv())) {
                // 解密获取手机号
                String decryptedData = wechatUtil.decryptData(loginRequest.getEncryptedData(), sessionKey, loginRequest.getIv());
                phoneNumber = wechatUtil.getPhoneNumberFromDecryptedData(decryptedData);
            } else if (StringUtils.hasText(loginRequest.getPhoneNumber())) {
                // 如果前端直接传了手机号（开发环境）
                phoneNumber = loginRequest.getPhoneNumber();
            }

            // 查询用户是否存在
            AppUser user = getByOpenId(openId);

            // 如果用户不存在，创建新用户
            if (user == null) {
                user = new AppUser();
                user.setOpenId(openId);
                user.setWxNickname(loginRequest.getWxNickname());
                user.setWxAvatarUrl(loginRequest.getWxAvatarUrl());
                user.setWxGender(loginRequest.getWxGender());
                user.setWxCountry(loginRequest.getWxCountry());
                user.setWxProvince(loginRequest.getWxProvince());
                user.setWxCity(loginRequest.getWxCity());
                if (phoneNumber != null) {
                    user.setPhoneNumber(phoneNumber);
                }
                user.setLastAccessTime(new Date());
                user.setType("user");
                baseMapper.insert(user);
            } else {
                // 如果用户存在，更新用户信息
                if (StringUtils.hasText(loginRequest.getWxNickname())) {
                    user.setWxNickname(loginRequest.getWxNickname());
                }
                if (StringUtils.hasText(loginRequest.getWxAvatarUrl())) {
                    user.setWxAvatarUrl(loginRequest.getWxAvatarUrl());
                }
                if (loginRequest.getWxGender() != null) {
                    user.setWxGender(loginRequest.getWxGender());
                }
                if (StringUtils.hasText(loginRequest.getWxCountry())) {
                    user.setWxCountry(loginRequest.getWxCountry());
                }
                if (StringUtils.hasText(loginRequest.getWxProvince())) {
                    user.setWxProvince(loginRequest.getWxProvince());
                }
                if (StringUtils.hasText(loginRequest.getWxCity())) {
                    user.setWxCity(loginRequest.getWxCity());
                }
                if (phoneNumber != null && (user.getPhoneNumber() == null || user.getPhoneNumber().isEmpty())) {
                    user.setPhoneNumber(phoneNumber);
                }
                user.setLastAccessTime(new Date());
                baseMapper.updateById(user);
            }

            // 生成token
            String token = jwtUtil.generateToken(openId);

            // 返回登录响应
            return LoginResponse.of(token, user);
        } catch (Exception e) {
            throw new RuntimeException("微信登录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public AppUser getUserInfoByToken(String token) {
        if (!StringUtils.hasText(token)) {
            throw new RuntimeException("token不能为空");
        }

        // 从token中获取openId
        String openId;
        try {
            openId = jwtUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            throw new RuntimeException("无效的token");
        }

        // 根据openId查询用户
        AppUser user = getByOpenId(openId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return user;
    }*/
}