<template>
	<view class="settings-container">
		<view class="settings-header">
			<text class="header-title">设置</text>
		</view>
		
		<view class="settings-list">
			<view class="settings-item">
				<text class="item-label">账号与安全</text>
				<text class="item-arrow">></text>
			</view>
			
			<view class="settings-item">
				<text class="item-label">隐私设置</text>
				<text class="item-arrow">></text>
			</view>
			
			<view class="settings-item">
				<text class="item-label">通知设置</text>
				<text class="item-arrow">></text>
			</view>
			
			<view class="settings-item">
				<text class="item-label">关于我们</text>
				<text class="item-arrow">></text>
			</view>
			
			<view class="settings-item">
				<text class="item-label">意见反馈</text>
				<text class="item-arrow">></text>
			</view>
			
			<view class="settings-item">
				<text class="item-label">清除缓存</text>
				<text class="item-arrow">></text>
			</view>
		</view>
		
		<view class="version-info">
			<text>月子中心 v1.0.0</text>
		</view>
	</view>
</template>

<script>
	/**
	 * 设置页面
	 * 用于用户设置和管理个人偏好
	 */
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			/**
			 * 清除缓存
			 * 清除应用本地缓存数据
			 */
			clearCache() {
				uni.showModal({
					title: '提示',
					content: '确定要清除缓存吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除缓存，但保留登录信息
							const token = uni.getStorageSync('token');
							const userInfo = uni.getStorageSync('userInfo');
							
							// 清除所有缓存
							uni.clearStorageSync();
							
							// 恢复登录信息
							if (token && userInfo) {
								uni.setStorageSync('token', token);
								uni.setStorageSync('userInfo', userInfo);
							}
							
							uni.showToast({
								title: '缓存已清除',
								icon: 'success'
							});
						}
					}
				});
			}
		},
		// 分享给朋友
		onShareAppMessage() {
			return getShareAppMessageConfig({
				title: '东方爱堡月子会所 - 专业月子护理服务',
				path: '/pages/index/index' // 跳转到首页
			});
		},
		// 分享到朋友圈
		onShareTimeline() {
			return getShareTimelineConfig({
				title: '东方爱堡月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护'
			});
		}
	}
</script>

<style>
	.settings-container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.settings-header {
		margin-bottom: 40rpx;
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.settings-list {
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}
	
	.settings-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.settings-item:last-child {
		border-bottom: none;
	}
	
	.item-label {
		font-size: 28rpx;
		color: #333;
	}
	
	.item-arrow {
		color: #ccc;
		font-size: 24rpx;
	}
	
	.version-info {
		text-align: center;
		color: #999;
		font-size: 24rpx;
		margin-top: 60rpx;
	}
</style>