package com.dfab.taskCenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 推送记录实体类
 * 用于记录微信小程序推送历史
 */
@Data
@TableName("push_record")
@Schema(description = "推送记录实体类")
public class PushRecord extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "推送记录 ID", example = "1")
    private Long id;

    /**
     * 关联的任务ID
     */
    @NotNull(message = "任务ID不能为空")
    @Schema(description = "关联的任务ID", example = "1")
    private Long taskId;

    /**
     * 推送类型：1-任务创建通知，2-任务完成通知，3-任务提醒
     */
    @NotNull(message = "推送类型不能为空")
    @Schema(description = "推送类型", example = "1", required = true)
    private Integer pushType;

    /**
     * 推送标题
     */
    @NotBlank(message = "推送标题不能为空")
    @Size(min = 1, max = 100, message = "推送标题长度必须在1-100个字符之间")
    @Schema(description = "推送标题", example = "车牌处理任务", required = true)
    private String title;

    /**
     * 推送内容
     */
    @NotBlank(message = "推送内容不能为空")
    @Size(min = 1, max = 500, message = "推送内容长度必须在1-500个字符之间")
    @Schema(description = "推送内容", example = "您有新的车牌处理任务，请及时处理", required = true)
    private String content;

    /**
     * 接收者微信 openId
     */
    @Schema(description = "接收者微信 openId", example = "wx1234567890")
    private String receiverOpenId;

    /**
     * 推送状态：0-推送失败，1-推送成功
     */
    @Schema(description = "推送状态", example = "1")
    private Integer status = 0;

    /**
     * 推送时间
     */
    @Schema(description = "推送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;

    /**
     * 推送响应信息
     */
    @Size(max = 1000, message = "推送响应信息长度不能超过1000个字符")
    @Schema(description = "推送响应信息", example = "推送成功")
    private String response;

    /**
     * 错误信息
     */
    @Size(max = 1000, message = "错误信息长度不能超过1000个字符")
    @Schema(description = "错误信息", example = "网络超时")
    private String errorMsg;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount = 0;

    /**
     * 模板ID（微信小程序推送模板）
     */
    @Schema(description = "模板ID", example = "template_001")
    private String templateId;

    /**
     * 跳转页面路径
     */
    @Schema(description = "跳转页面路径", example = "/pages/task/detail")
    private String pagePath;

    /**
     * 推送数据（JSON格式）
     */
    @Size(max = 2000, message = "推送数据长度不能超过2000个字符")
    @Schema(description = "推送数据", example = "{\"task_id\":\"1\",\"plate_number\":\"粤A12345\"}")
    private String pushData;
}
