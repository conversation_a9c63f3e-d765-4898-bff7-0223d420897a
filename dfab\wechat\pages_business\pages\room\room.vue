<template>
	<view class="room-container">
		<custom-nav-bar title="房型展示"></custom-nav-bar>
		<view class="room-content">
			<!-- 房型介绍 -->
			<view class="room-intro">
				<view class="intro-text">
					<view class="intro-title">精品房型设计</view>
					<view class="intro-desc">
						我们精心设计了多种房型，从温馨舒适的标准间到奢华典雅的套房，每一间都配备完善的母婴护理设施，为您提供家一般的温暖体验。
					</view>
				</view>
			</view>

			<!-- 房型图片展示 -->
			<view class="room-section">
				<view class="room-grid">
					<view
						class="room-item"
						v-for="(item, index) in roomList"
						:key="index"
						@click="previewImage(item.mediumImage, item.largeImage)"
					>
						<image
							class="room-image"
							:src="item.mediumImage"
							mode="aspectFill"
							:lazy-load="true"
						></image>
						<view class="room-name">{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import imageApi from '@/config/api/image.js';
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 房型展示页面
	 * 展示月子中心的各种房型图片，从后端读取
	 */

	// 后端图片ID列表
	// const imageIds = [
	// 	'1927003818722185218',
	// 	'1927003920849293313',
	// 	'1927003969482248194',
	// 	'1927004020803751937',
	// 	'1927004071022153730',
	// 	'1927004119797714945',
	// 	'1927004176294989826',
	// 	'1927004221660581890',
	// 	'1927004272445214722'
	// ];
	const imageIds = [
		'1942244233926725633',
		'1942244864146067458',
		'1942244954868862978',
		'1942245040025817089',
		'1942245125807722498',
		'1942245225233698818',
		'1942245301813301250',
		'1942245371958841346'
	];

	// 房型名称列表
	const roomNames = [
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' '
	];

	// 房型图片列表 - 使用封装的API生成
	const roomList = ref(imageApi.generateImageList(imageIds, roomNames));

	// 图片预览功能 - 使用封装的API
	const previewImage = (currentImage, largeImage) => {
		imageApi.previewImage(currentImage, largeImage);
	};

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 豪华房型展示',
			path: '/pages_business/pages/room/room',
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所豪华房型，为您提供舒适的月子环境',
		});
	}

	onMounted(() => {
		console.log('房型展示页面加载完成');
		console.log('房型图片列表:', roomList.value);
	});
</script>

<style>
	.room-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.room-header {
		background-color: #8b5a2b;
		padding: 30rpx;
		color: #fff;
		text-align: center;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.room-content {
		padding: 20rpx;
	}

	.room-intro {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.intro-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.intro-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		text-align: center;
	}

	.room-section {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.room-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.room-item {
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		cursor: pointer;
	}

	.room-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	}

	.room-image {
		width: 100%;
		height: 400rpx;
		object-fit: cover;
	}

	.room-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #8b5a2b;
		text-align: center;
		padding: 20rpx 15rpx 10rpx;
	}
</style>