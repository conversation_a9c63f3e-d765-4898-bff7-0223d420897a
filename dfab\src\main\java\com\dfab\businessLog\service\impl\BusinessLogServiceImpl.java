package com.dfab.businessLog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.businessLog.entity.BusinessLog;
import com.dfab.businessLog.mapper.BusinessLogMapper;
import com.dfab.businessLog.service.BusinessLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 业务关键日志 Service 实现类。
 */
@Slf4j
@Service
public class BusinessLogServiceImpl extends ServiceImpl<BusinessLogMapper, BusinessLog> implements BusinessLogService {

    @Override
    public List<BusinessLog> getBusinessLogListByOpenId(String openId) {
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLog::getOpenId, openId);
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<BusinessLog> getBusinessLogListByOpenIdAndType(String openId, String type) {
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLog::getOpenId, openId);
        if (StringUtils.hasText(type)) {
            queryWrapper.eq(BusinessLog::getType, type);
        }
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<BusinessLog> getBusinessLogListByMemberCheckinIdAndType(Long memberCheckinId, String type) {
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLog::getMemberCheckinId, memberCheckinId);
        if (StringUtils.hasText(type)) {
            queryWrapper.eq(BusinessLog::getType, type);
        }
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public BusinessLog create(BusinessLog businessLog) {
        if (!StringUtils.hasText(businessLog.getOpenId())) {
            throw new RuntimeException("openId不能为空");
        }
        if (!StringUtils.hasText(businessLog.getDetail())) {
            throw new RuntimeException("日志详情不能为空");
        }
        if (!StringUtils.hasText(businessLog.getType())) {
            throw new RuntimeException("日志类型不能为空");
        }

        boolean save = this.save(businessLog);
        if (save) {
            return businessLog;
        } else {
            throw new RuntimeException("创建业务日志失败");
        }
    }

    @Override
    public BusinessLog createByAdmin(BusinessLog businessLog) {
        if (!StringUtils.hasText(businessLog.getDetail())) {
            throw new RuntimeException("日志详情不能为空");
        }
        if (!StringUtils.hasText(businessLog.getType())) {
            throw new RuntimeException("日志类型不能为空");
        }

        boolean save = this.save(businessLog);
        if (save) {
            return businessLog;
        } else {
            throw new RuntimeException("创建业务日志失败");
        }
    }

    @Override
    public Boolean removeByOpenIdAndId(String openId, Long id) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        if (!StringUtils.hasText(openId)) {
            throw new RuntimeException("openId不能为空");
        }

        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLog::getOpenId, openId);
        queryWrapper.eq(BusinessLog::getId, id);
        BusinessLog existingLog = getOne(queryWrapper);
        
        if (existingLog != null) {
            return this.removeById(existingLog.getId());
        } else {
            log.error("不能删除别人的数据，openId: {}, id: {}", openId, id);
            return false;
        }
    }

    @Override
    public BusinessLog updateByOpenIdAndId(BusinessLog businessLog) {
        if (businessLog.getId() == null) {
            throw new RuntimeException("id不能为空");
        }
        if (!StringUtils.hasText(businessLog.getOpenId())) {
            throw new RuntimeException("openId不能为空");
        }

        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLog::getOpenId, businessLog.getOpenId());
        queryWrapper.eq(BusinessLog::getId, businessLog.getId());
        BusinessLog existingLog = getOne(queryWrapper);
        
        if (existingLog != null) {
            boolean update = this.updateById(businessLog);
            if (update) {
                return this.getById(businessLog.getId());
            } else {
                throw new RuntimeException("更新业务日志失败");
            }
        } else {
            log.error("不能修改别人的数据，openId: {}, id: {}", businessLog.getOpenId(), businessLog.getId());
            throw new RuntimeException("不能修改别人的数据");
        }
    }

    @Override
    public IPage<BusinessLog> page(BusinessLog businessLog) {
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(businessLog.getOpenId())) {
            queryWrapper.eq(BusinessLog::getOpenId, businessLog.getOpenId());
        }
        if (businessLog.getMemberCheckinId() != null) {
            queryWrapper.eq(BusinessLog::getMemberCheckinId, businessLog.getMemberCheckinId());
        }
        if (StringUtils.hasText(businessLog.getType())) {
            queryWrapper.eq(BusinessLog::getType, businessLog.getType());
        }
        if (StringUtils.hasText(businessLog.getDetail())) {
            queryWrapper.like(BusinessLog::getDetail, businessLog.getDetail());
        }
        
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        
        Page<BusinessLog> page = new Page<>(businessLog.getPageNum() != null ? businessLog.getPageNum() : 1,
                businessLog.getPageSize() != null ? businessLog.getPageSize() : 10);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public List<BusinessLog> list(BusinessLog businessLog) {
        LambdaQueryWrapper<BusinessLog> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(businessLog.getOpenId())) {
            queryWrapper.eq(BusinessLog::getOpenId, businessLog.getOpenId());
        }
        if (businessLog.getMemberCheckinId() != null) {
            queryWrapper.eq(BusinessLog::getMemberCheckinId, businessLog.getMemberCheckinId());
        }
        if (StringUtils.hasText(businessLog.getType())) {
            queryWrapper.eq(BusinessLog::getType, businessLog.getType());
        }
        if (StringUtils.hasText(businessLog.getDetail())) {
            queryWrapper.like(BusinessLog::getDetail, businessLog.getDetail());
        }
        
        queryWrapper.orderByDesc(BusinessLog::getCreateTime);
        
        return this.list(queryWrapper);
    }
}
