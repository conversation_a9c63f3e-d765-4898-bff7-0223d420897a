<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="预约日期" prop="reservationDate">
        <el-date-picker
          v-model="queryParams.reservationDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择日期"
          clearable
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期范围" prop="reservationDateRange">
        <el-date-picker
          v-model="queryParams.reservationDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          clearable
          style="width: 240px"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用餐时段" prop="mealTime">
        <el-select v-model="queryParams.mealTime" ref="mealTimeFilter" placeholder="请选择用餐时段"  clearable multiple style="width: 200px" @change="handleQuery('mealTime')">
          <el-option label="早餐" value="breakfast" />
          <el-option label="午餐" value="lunch" />
          <el-option label="晚餐" value="dinner" />
        </el-select>
      </el-form-item>-->


      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          style="width: 160px"
          @keyup.enter="handleQuery"
          @input="handleInputChange"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="queryParams.roomNumber"
          placeholder="请输入房间号"
          clearable
          style="width: 160px"
          @keyup.enter="handleQuery"
          @input="handleInputChange"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员状态" prop="memberStatus">
        <el-select
          v-model="queryParams.memberStatus"
          placeholder="请选择会员状态"
          clearable
          style="width: 160px"
          @change="handleQuery"
        >
          <el-option label="已入住" value="0" />
          <el-option label="待入住" value="3" />
          <el-option label="已退住" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="订餐总数" style="margin-right: 10px;">
        <span style="color: #409eff; font-weight: bold;">{{ statisticsData.numTotal }}</span>
      </el-form-item>
      <el-form-item label="早餐" style="margin-right: 10px;">
        <span style="color: #f56c6c; font-weight: bold;">{{ statisticsData.breakfast }}</span>
      </el-form-item>
      <el-form-item label="午餐" style="margin-right: 10px;">
        <span style="color: #e6a23c; font-weight: bold;">{{ statisticsData.lunch }}</span>
      </el-form-item>
      <el-form-item label="晚餐" style="margin-right: 10px;">
        <span style="color: #409eff; font-weight: bold;">{{ statisticsData.dinner }}</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:mealReservation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:mealReservation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:mealReservation:remove']"
        >删除</el-button>
      </el-col>
      <!-- 导出按钮已隐藏 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:mealReservation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mealReservationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column label="预约日期" align="center" prop="reservationDate" width="120" />
      <el-table-column label="用餐时段" align="center" prop="mealTime" width="180">
        <template #default="scope">
          <div style="display: flex; flex-wrap: wrap; gap: 4px; justify-content: center;">
            <span
              v-for="mealTag in getMealTimeTags(scope.row.mealTime)"
              :key="mealTag.text"
              :style="{
                backgroundColor: mealTag.color,
                color: '#fff',
                padding: '2px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: '500',
                display: 'inline-block',
                margin: '1px'
              }"
            >
              {{ mealTag.text }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="用餐份数" align="center" prop="quantity" width="80" />
      <el-table-column label="用户姓名" align="center" prop="userName" width="120" />
      <el-table-column label="联系电话" align="center" prop="userPhone" width="120" />
      <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:mealReservation:edit']"
          >修改</el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:mealReservation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
      <el-pagination
        v-show="total > 0"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加或修改餐食预约对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="mealReservationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="预约日期" prop="reservationDate">
          <el-date-picker
            v-model="form.reservationDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="用餐时段" prop="mealTime">
          <el-select v-model="form.mealTime" ref="mealTimeSelect" placeholder="请选择用餐时段" multiple style="width: 100%" @change="$refs.mealTimeSelect.blur()">
            <el-option label="早餐" value="breakfast" />
            <el-option label="午餐" value="lunch" />
            <el-option label="晚餐" value="dinner" />
          </el-select>
        </el-form-item>
        <el-form-item label="用餐份数" prop="quantity">
          <el-input-number v-model="form.quantity" :min="1" :max="10" style="width: 100%" />
        </el-form-item>
        <el-form-item label="选择会员" prop="selectedMember">
          <el-select
            v-model="form.selectedMember"
            placeholder="请选择入住会员"
            style="width: 100%"
            @change="handleMemberChange"
            filterable
          >
            <el-option
              v-for="member in memberList"
              :key="member.id"
              :label="`${member.memberName} - ${member.phoneNumber} - ${member.roomNumber}`"
              :value="member.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="联系电话" prop="userPhone">
          <el-input v-model="form.userPhone" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="form.roomNumber" placeholder="自动填充" readonly />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { listMealReservation, getMealReservation, addMealReservation, updateMealReservation, delMealReservation, exportMealReservation } from "@/api/business/mealReservation";
import { getAllMemberCheckins } from "@/api/business/memberCheckin";

const { proxy } = getCurrentInstance();

// 防抖定时器
let debounceTimer = null;

const mealReservationList = ref([]);
const memberList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 统计数据
const statisticsData = ref({
  numTotal: 0,
  breakfast: 0,
  lunch: 0,
  dinner: 0
});

// 获取默认预约日期字符串 (YYYY-MM-DD格式)
function getDefaultReservationDate() {
  const now = new Date()
  const currentHour = now.getHours()

  // 如果过了7pm，预约日期为第二天
  if (currentHour >= 19) {
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const year = tomorrow.getFullYear()
    const month = (tomorrow.getMonth() + 1).toString().padStart(2, '0')
    const day = tomorrow.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  } else {
    // 7pm前，预约日期为当天
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }
}

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    reservationDate: undefined,
    reservationDateRange: undefined,
    reservationDateBegin: undefined,
    reservationDateEnd: undefined,
    mealTime: undefined,
    userName: undefined,
    roomNumber: undefined,
    memberStatus: "0"
  },
  rules: {
    mealTime: [
      { required: true, message: "用餐时段不能为空", trigger: "change" }
    ],
    quantity: [
      { required: true, message: "用餐份数不能为空", trigger: "blur" }
    ],
    selectedMember: [
      { required: true, message: "请选择入住会员", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询餐食预约列表 */
function getList() {
  loading.value = true;

  // 创建查询参数副本，移除reservationDateRange字段
  const queryData = { ...queryParams.value };
  delete queryData.reservationDateRange;

  // 处理多选的用餐时段，转换为逗号分隔的字符串
  if (queryData.mealTime && Array.isArray(queryData.mealTime)) {
    queryData.mealTime = queryData.mealTime.join(',');
  }

  listMealReservation(queryData).then(response => {
    mealReservationList.value = response.rows;
    total.value = parseInt(response.total) || 0;

    // 更新统计数据
    statisticsData.value.numTotal = response.numTotal || 0;
    statisticsData.value.breakfast = response.breakfast || 0;
    statisticsData.value.lunch = response.lunch || 0;
    statisticsData.value.dinner = response.dinner || 0;

    loading.value = false;
  });
}

/** 处理页面大小变化 */
function handleSizeChange(val) {
  queryParams.value.pageSize = val
  queryParams.value.pageNum = 1
  getList()
}

/** 处理当前页变化 */
function handleCurrentChange(val) {
  queryParams.value.pageNum = val
  getList()
}

// 用餐时段文本映射
function getMealTimeText(mealTime) {
  if (!mealTime) return '';

  const mealTimeMap = {
    'Breakfast': '早餐',
    'Lunch': '午餐',
    'Dinner': '晚餐',
    'breakfast': '早餐',
    'lunch': '午餐',
    'dinner': '晚餐',
    'BREAKFAST': '早餐',
    'LUNCH': '午餐',
    'DINNER': '晚餐',
    '早餐': '早餐',
    '午餐': '午餐',
    '晚餐': '晚餐'
  };

  // 如果包含逗号，说明是多个用餐时段
  if (mealTime.includes(',')) {
    return mealTime.split(',')
      .map(time => time.trim())
      .map(time => mealTimeMap[time] || time)
      .join(' ');
  }

  return mealTimeMap[mealTime] || mealTime;
}

// 用餐时段标签映射（带颜色）
function getMealTimeTags(mealTime) {
  if (!mealTime) return [];

  const mealTimeMap = {
    'Breakfast': { text: '早餐', color: '#f56c6c' },
    'Lunch': { text: '午餐', color: '#e6a23c' },
    'Dinner': { text: '晚餐', color: '#409eff' },
    'breakfast': { text: '早餐', color: '#f56c6c' },
    'lunch': { text: '午餐', color: '#e6a23c' },
    'dinner': { text: '晚餐', color: '#409eff' },
    'BREAKFAST': { text: '早餐', color: '#f56c6c' },
    'LUNCH': { text: '午餐', color: '#e6a23c' },
    'DINNER': { text: '晚餐', color: '#409eff' },
    '早餐': { text: '早餐', color: '#f56c6c' },
    '午餐': { text: '午餐', color: '#e6a23c' },
    '晚餐': { text: '晚餐', color: '#409eff' }
  };

  // 如果包含逗号，说明是多个用餐时段
  if (mealTime.includes(',')) {
    return mealTime.split(',')
      .map(time => time.trim())
      .map(time => mealTimeMap[time] || { text: time, color: '#909399' })
      .filter(tag => tag.text);
  }

  const tag = mealTimeMap[mealTime] || { text: mealTime, color: '#909399' };
  return tag.text ? [tag] : [];
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

// 获取当前时间对应的默认用餐时段
function getDefaultMealTime() {
  const now = new Date()
  const currentHour = now.getHours()

  if (currentHour >= 19) {
    // 7pm后默认选择早餐
    return ['breakfast']
  } else if (currentHour >= 13) {
    // 1pm后默认选择晚餐
    return ['dinner']
  } else if (currentHour >= 8) {
    // 8am后默认选择午餐
    return ['lunch']
  } else {
    // 8am前默认选择早餐
    return ['breakfast']
  }
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    reservationDate: getDefaultReservationDate(), // 7pm前为当天，7pm后为第二天
    mealTime: getDefaultMealTime(), // 根据时间智能选择默认用餐时段
    quantity: 1,
    selectedMember: undefined,
    userName: undefined,
    userPhone: undefined,
    roomNumber: undefined,
    openId: undefined
  };
  proxy.resetForm("mealReservationRef");
}

const mealTimeFilter = ref(null);

/** 搜索按钮操作 */
function handleQuery(type = null) {
  queryParams.value.pageNum = 1;

  // 处理日期范围
  if (queryParams.value.reservationDateRange && queryParams.value.reservationDateRange.length === 2) {
    queryParams.value.reservationDateBegin = queryParams.value.reservationDateRange[0];
    queryParams.value.reservationDateEnd = queryParams.value.reservationDateRange[1];
  } else {
    queryParams.value.reservationDateBegin = undefined;
    queryParams.value.reservationDateEnd = undefined;
  }

  if(type === 'mealTime'){
    mealTimeFilter.value.blur()
  }

  getList();
}

/** 处理输入框变化（带防抖） */
function handleInputChange() {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // 设置新的定时器，500ms后执行查询
  debounceTimer = setTimeout(() => {
    handleQuery();
  }, 500);
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 获取入住会员列表 */
function getMemberList() {
  getAllMemberCheckins().then(response => {
    if (response.code === 200) {
      memberList.value = response.data || [];
    } else {
      proxy.$modal.msgError(response.msg || "获取会员列表失败");
    }
  }).catch(error => {
    console.error('获取会员列表失败:', error);
    proxy.$modal.msgError("获取会员列表失败");
  });
}

/** 会员选择变化处理 */
function handleMemberChange(memberId) {
  if (memberId) {
    const selectedMember = memberList.value.find(member => member.id === memberId);
    if (selectedMember) {
      form.value.userName = selectedMember.memberName;
      form.value.userPhone = selectedMember.phoneNumber;
      form.value.roomNumber = selectedMember.roomNumber;
      form.value.openId = selectedMember.openId;
    }
  } else {
    // 清空相关字段
    form.value.userName = undefined;
    form.value.userPhone = undefined;
    form.value.roomNumber = undefined;
    form.value.openId = undefined;
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getMemberList(); // 获取会员列表
  open.value = true;
  title.value = "添加餐食预约";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getMemberList(); // 获取会员列表
  const id = row.id || ids.value[0];
  getMealReservation(id).then(response => {
    form.value = response.data;

    // 处理用餐时段，如果是逗号分隔的字符串，转换为数组
    if (form.value.mealTime && typeof form.value.mealTime === 'string' && form.value.mealTime.includes(',')) {
      form.value.mealTime = form.value.mealTime.split(',').map(time => time.trim());
    } else if (form.value.mealTime && typeof form.value.mealTime === 'string') {
      form.value.mealTime = [form.value.mealTime];
    }

    // 根据memberCheckinId设置选中的会员
    if (form.value.memberCheckinId) {
      form.value.selectedMember = form.value.memberCheckinId;
    } else if (form.value.userPhone) {
      // 如果没有memberCheckinId，则根据用户信息找到对应的会员并设置选中
      const matchedMember = memberList.value.find(member =>
        member.phoneNumber === form.value.userPhone
      );
      if (matchedMember) {
        form.value.selectedMember = matchedMember.id;
      }
    }

    open.value = true;
    title.value = "修改餐食预约";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["mealReservationRef"].validate(valid => {
    if (valid) {
      // 创建提交数据的副本
      const submitData = { ...form.value };

      // 将选择的会员ID设置为memberCheckinId
      if (submitData.selectedMember) {
        submitData.memberCheckinId = submitData.selectedMember;
      }

      // 移除selectedMember字段
      delete submitData.selectedMember;

      // 处理多选的用餐时段，转换为逗号分隔的字符串
      if (submitData.mealTime && Array.isArray(submitData.mealTime)) {
        submitData.mealTime = submitData.mealTime.join(',');
      }

      if (form.value.id != undefined) {
        updateMealReservation(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMealReservation(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const reservationIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除餐食预约编号为"' + reservationIds + '"的数据项?').then(function() {
    return delMealReservation(reservationIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/mealReservation/export', {
    ...queryParams.value
  }, `餐食预约_${new Date().getTime()}.xlsx`);
}

onMounted(() => {
  getList();
});
</script> 