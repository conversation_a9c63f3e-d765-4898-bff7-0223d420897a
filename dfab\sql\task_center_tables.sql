-- 任务中心表（通用任务管理）
CREATE TABLE `task_center` (
  `id` bigint(20) NOT NULL COMMENT '任务ID',
  `title` varchar(100) NOT NULL COMMENT '任务标题',
  `task_type` int(11) NOT NULL COMMENT '任务类型：1-车牌新增，2-车牌删除，3-离所处理，4-会员注册，5-预约处理，6-投诉处理等',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '任务状态：0-待处理，1-已处理',
  `description` varchar(1000) DEFAULT NULL COMMENT '任务描述（详细信息）',
  `key_info` varchar(100) DEFAULT NULL COMMENT '关键信息（核心业务数据，如车牌号、会员号、订单号等）',
  `business_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID（可以是车辆ID、会员ID、订单ID等）',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型（car-车辆，member-会员，order-订单，complaint-投诉等）',
  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
  `open_id` varchar(100) DEFAULT NULL COMMENT '微信openId',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `extend_info` varchar(2000) DEFAULT NULL COMMENT '扩展信息（JSON格式，存储额外的业务数据）',
  `processor` varchar(50) DEFAULT NULL COMMENT '处理人员',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_remark` varchar(500) DEFAULT NULL COMMENT '处理备注',
  `priority` int(11) NOT NULL DEFAULT '2' COMMENT '优先级：1-低，2-中，3-高',
  `is_pushed` int(11) NOT NULL DEFAULT '0' COMMENT '是否已推送：0-未推送，1-已推送',
  `push_count` int(11) NOT NULL DEFAULT '0' COMMENT '推送次数',
  `last_push_time` datetime DEFAULT NULL COMMENT '最后推送时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_status` int(11) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_key_info` (`key_info`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务中心表（通用任务管理）';

-- 推送记录表
CREATE TABLE `push_record` (
  `id` bigint(20) NOT NULL COMMENT '推送记录ID',
  `task_id` bigint(20) DEFAULT NULL COMMENT '关联的任务ID',
  `push_type` int(11) NOT NULL COMMENT '推送类型：1-任务创建通知，2-任务完成通知，3-任务提醒',
  `title` varchar(100) NOT NULL COMMENT '推送标题',
  `content` varchar(500) NOT NULL COMMENT '推送内容',
  `receiver_open_id` varchar(100) DEFAULT NULL COMMENT '接收者微信openId',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '推送状态：0-推送失败，1-推送成功',
  `push_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间',
  `response` varchar(1000) DEFAULT NULL COMMENT '推送响应信息',
  `error_msg` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `template_id` varchar(50) DEFAULT NULL COMMENT '模板ID',
  `page_path` varchar(200) DEFAULT NULL COMMENT '跳转页面路径',
  `push_data` varchar(2000) DEFAULT NULL COMMENT '推送数据（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_status` int(11) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_push_type` (`push_type`),
  KEY `idx_status` (`status`),
  KEY `idx_receiver_open_id` (`receiver_open_id`),
  KEY `idx_push_time` (`push_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送记录表';

-- 插入任务类型字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('任务类型', 'sys_task_type', '0', 'admin', NOW(), '任务中心任务类型列表');

-- 客户用品补充需求表
CREATE TABLE `customer_supply_request` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户微信openId',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `member_checkin_id` bigint(20) DEFAULT NULL COMMENT '会员入住ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `user_phone` varchar(20) DEFAULT NULL COMMENT '用户手机号',
  `request_content` text NOT NULL COMMENT '需求内容描述',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理，1-已处理，2-已拒绝',
  `admin_remark` varchar(500) DEFAULT NULL COMMENT '管理员备注',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modifier` varchar(64) DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `delete_status` int(11) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_member_checkin_id` (`member_checkin_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户用品补充需求表';

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '车牌新增', '1', 'sys_task_type', '', 'primary', 'N', '0', 'admin', NOW(), '车牌新增任务'),
(2, '车牌删除', '2', 'sys_task_type', '', 'warning', 'N', '0', 'admin', NOW(), '车牌删除任务'),
(3, '离所处理', '3', 'sys_task_type', '', 'danger', 'N', '0', 'admin', NOW(), '离所处理任务'),
(4, '会员注册', '4', 'sys_task_type', '', 'success', 'N', '0', 'admin', NOW(), '会员注册任务'),
(5, '预约处理', '5', 'sys_task_type', '', 'info', 'N', '0', 'admin', NOW(), '预约处理任务'),
(6, '投诉处理', '6', 'sys_task_type', '', 'danger', 'N', '0', 'admin', NOW(), '投诉处理任务');

-- 客户用品补充需求状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('客户用品补充需求状态', 'customer_supply_request_status', '0', 'admin', NOW(), '客户用品补充需求处理状态');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待处理', '0', 'customer_supply_request_status', '', 'warning', 'Y', '0', 'admin', NOW(), '待处理状态'),
(2, '已处理', '1', 'customer_supply_request_status', '', 'success', 'N', '0', 'admin', NOW(), '已处理状态'),
(3, '已拒绝', '2', 'customer_supply_request_status', '', 'danger', 'N', '0', 'admin', NOW(), '已拒绝状态');

-- 插入业务类型字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('业务类型', 'sys_business_type', '0', 'admin', NOW(), '任务中心业务类型列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '车辆', 'car', 'sys_business_type', '', 'primary', 'N', '0', 'admin', NOW(), '车辆相关业务'),
(2, '会员', 'member', 'sys_business_type', '', 'success', 'N', '0', 'admin', NOW(), '会员相关业务'),
(3, '订单', 'order', 'sys_business_type', '', 'warning', 'N', '0', 'admin', NOW(), '订单相关业务'),
(4, '投诉', 'complaint', 'sys_business_type', '', 'danger', 'N', '0', 'admin', NOW(), '投诉相关业务'),
(5, '其他', 'other', 'sys_business_type', '', 'info', 'N', '0', 'admin', NOW(), '其他业务');

-- 插入任务状态字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('任务状态', 'sys_task_status', '0', 'admin', NOW(), '任务中心任务状态列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待处理', '0', 'sys_task_status', '', 'warning', 'N', '0', 'admin', NOW(), '待处理状态'),
(2, '已处理', '1', 'sys_task_status', '', 'success', 'N', '0', 'admin', NOW(), '已处理状态');

-- 插入任务优先级字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('任务优先级', 'sys_task_priority', '0', 'admin', NOW(), '任务中心任务优先级列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '低', '1', 'sys_task_priority', '', 'info', 'N', '0', 'admin', NOW(), '低优先级'),
(2, '中', '2', 'sys_task_priority', '', 'warning', 'Y', '0', 'admin', NOW(), '中优先级'),
(3, '高', '3', 'sys_task_priority', '', 'danger', 'N', '0', 'admin', NOW(), '高优先级');

-- 插入推送类型字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('推送类型', 'sys_push_type', '0', 'admin', NOW(), '微信推送类型列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '任务创建通知', '1', 'sys_push_type', '', 'primary', 'N', '0', 'admin', NOW(), '任务创建时的通知'),
(2, '任务完成通知', '2', 'sys_push_type', '', 'success', 'N', '0', 'admin', NOW(), '任务完成时的通知'),
(3, '任务提醒', '3', 'sys_push_type', '', 'warning', 'N', '0', 'admin', NOW(), '任务提醒通知');

-- 插入推送状态字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('推送状态', 'sys_push_status', '0', 'admin', NOW(), '微信推送状态列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '推送失败', '0', 'sys_push_status', '', 'danger', 'N', '0', 'admin', NOW(), '推送失败状态'),
(2, '推送成功', '1', 'sys_push_status', '', 'success', 'N', '0', 'admin', NOW(), '推送成功状态');
