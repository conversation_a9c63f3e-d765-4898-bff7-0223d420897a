package com.dfab.customerSupplyRequest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.customerSupplyRequest.entity.CustomerSupplyRequest;
import com.dfab.customerSupplyRequest.mapper.CustomerSupplyRequestMapper;
import com.dfab.customerSupplyRequest.service.CustomerSupplyRequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户用品补充需求 Service 实现类
 */
@Slf4j
@Service
public class CustomerSupplyRequestServiceImpl extends ServiceImpl<CustomerSupplyRequestMapper, CustomerSupplyRequest> implements CustomerSupplyRequestService {

    @Override
    public List<CustomerSupplyRequest> getByOpenId(String openId) {
        if (openId == null || openId.trim().isEmpty()) {
            log.warn("getByOpenId方法接收到空的openId参数");
            return new ArrayList<>();
        }

        LambdaQueryWrapper<CustomerSupplyRequest> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerSupplyRequest::getOpenId, openId);
        queryWrapper.eq(CustomerSupplyRequest::getDeleteStatus, 0);
        queryWrapper.orderByDesc(CustomerSupplyRequest::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public Page<CustomerSupplyRequest> pageQuery(Page<CustomerSupplyRequest> page, CustomerSupplyRequest customerSupplyRequest) {
        LambdaQueryWrapper<CustomerSupplyRequest> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据用户姓名模糊查询
        if (StringUtils.hasText(customerSupplyRequest.getUserName())) {
            queryWrapper.like(CustomerSupplyRequest::getUserName, customerSupplyRequest.getUserName());
        }
        
        // 根据用户手机号模糊查询
        if (StringUtils.hasText(customerSupplyRequest.getUserPhone())) {
            queryWrapper.like(CustomerSupplyRequest::getUserPhone, customerSupplyRequest.getUserPhone());
        }
        
        // 根据处理状态查询
        if (customerSupplyRequest.getStatus() != null) {
            queryWrapper.eq(CustomerSupplyRequest::getStatus, customerSupplyRequest.getStatus());
        }
        
        // 根据需求内容模糊查询
        if (StringUtils.hasText(customerSupplyRequest.getRequestContent())) {
            queryWrapper.like(CustomerSupplyRequest::getRequestContent, customerSupplyRequest.getRequestContent());
        }

        // 根据会员入住ID查询
        if (customerSupplyRequest.getMemberCheckinId() != null) {
            queryWrapper.eq(CustomerSupplyRequest::getMemberCheckinId, customerSupplyRequest.getMemberCheckinId());
        }
        
        // 只查询未删除的记录
        queryWrapper.eq(CustomerSupplyRequest::getDeleteStatus, 0);
        queryWrapper.orderByDesc(CustomerSupplyRequest::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public Page<CustomerSupplyRequest> getSupplyRequestsPageByMybatisPlus(int pageNum, int pageSize, CustomerSupplyRequest queryParams) {
        // 使用MyBatis-Plus的分页插件
        Page<CustomerSupplyRequest> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<CustomerSupplyRequest> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数构建查询条件
        if (queryParams != null) {
            if (StringUtils.hasText(queryParams.getOpenId())) {
                wrapper.eq(CustomerSupplyRequest::getOpenId, queryParams.getOpenId());
            }
            if (queryParams.getMemberCheckinId() != null) {
                wrapper.eq(CustomerSupplyRequest::getMemberCheckinId, queryParams.getMemberCheckinId());
            }
            if (queryParams.getStatus() != null) {
                wrapper.eq(CustomerSupplyRequest::getStatus, queryParams.getStatus());
            }
            if (StringUtils.hasText(queryParams.getUserName())) {
                wrapper.like(CustomerSupplyRequest::getUserName, queryParams.getUserName());
            }
            if (StringUtils.hasText(queryParams.getUserPhone())) {
                wrapper.like(CustomerSupplyRequest::getUserPhone, queryParams.getUserPhone());
            }
            if (StringUtils.hasText(queryParams.getRequestContent())) {
                wrapper.like(CustomerSupplyRequest::getRequestContent, queryParams.getRequestContent());
            }
            if (StringUtils.hasText(queryParams.getProcessBy())) {
                wrapper.like(CustomerSupplyRequest::getProcessBy, queryParams.getProcessBy());
            }
        }

        // 只查询未删除的记录
        wrapper.eq(CustomerSupplyRequest::getDeleteStatus, 0);
        wrapper.orderByDesc(CustomerSupplyRequest::getCreateTime);

        // 使用MyBatis-Plus的分页查询
        return page(page, wrapper);
    }

    @Override
    public boolean processRequest(Long id, Integer status, String adminRemark, String processBy) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }

        if (status == null) {
            throw new RuntimeException("处理状态不能为空");
        }

        if (status < 0 || status > 2) {
            throw new RuntimeException("处理状态值无效，必须为0-2之间");
        }

        CustomerSupplyRequest request = this.getById(id);
        if (request == null) {
            throw new RuntimeException("需求记录不存在");
        }

        // 检查是否已经被删除
        if (request.getDeleteStatus() != null && request.getDeleteStatus() == 1) {
            throw new RuntimeException("需求记录已被删除，无法处理");
        }

        request.setStatus(status);
        request.setAdminRemark(adminRemark != null ? adminRemark.trim() : null);
        request.setProcessBy(processBy != null ? processBy.trim() : null);
        request.setProcessTime(LocalDateTime.now());

        return this.updateById(request);
    }
}
