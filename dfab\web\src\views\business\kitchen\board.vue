<template>
  <div class="kitchen-container">
    <!-- 新的表格布局页面 -->
    <div class="table-layout-page">
      <!-- 顶部房间号码区域 -->
      <div class="room-header" :style="roomHeaderStyle">
        <!-- 上部分：预约时间和用餐时段信息 -->
        <div class="meal-info-top">
          <!-- 左侧：当前时间、预约时间、总会员数和用餐时段 -->
          <div class="meal-info-left">
            <div class="meal-info-item">
              <span class="info-label">当前时间:</span>
              <span class="info-value">{{ currentSystemTime }}</span>
            </div>
            <div class="meal-info-item">
              <span class="info-label">总会员数:</span>
              <span class="info-value total-members">{{ patientData.length }}人</span>
            </div>
            <div class="meal-info-item">
              <span class="info-label">用餐时段:</span>
              <span class="info-value meal-period" :class="getMealPeriodClass()">{{ currentMealPeriod }}</span>
            </div>
          </div>

          <!-- 右侧：各用餐时段总份数 -->
          <div class="meal-info-right">
            <div class="meal-total-item breakfast-total">
              <span class="total-label">早餐:</span>
              <span class="total-value">{{ mealTotals.breakfast }}份</span>
            </div>
            <div class="meal-total-item lunch-total">
              <span class="total-label">午餐:</span>
              <span class="total-value">{{ mealTotals.lunch }}份</span>
            </div>
            <div class="meal-total-item dinner-total">
              <span class="total-label">晚餐:</span>
              <span class="total-value">{{ mealTotals.dinner }}份</span>
            </div>
          </div>
        </div>

        <!-- 下部分：房间网格 - 以房间为维度统计当前时段预约餐总数 -->
        <div class="room-grid">
          <!-- 有预约房间时显示房间卡片 -->
          <template v-if="roomsWithReservations.length > 0">
            <div class="room-card" v-for="room in roomsWithReservations" :key="room.roomNumber" :class="[
              { active: selectedRoom === room.roomNumber },
              { 'red-border-blink': shouldShowRedBorder(room) },
              getMealPeriodClass()
            ]" :title="`房间${room.roomNumber}在${currentMealPeriod}时段共有${room.quantity}份预约餐`">
              <span class="room-text">{{ room.roomNumber }} ×{{ room.quantity }}</span>
            </div>
          </template>

          <!-- 无预约房间时显示空状态占位 -->
          <div v-else class="empty-rooms-state">
            <div class="empty-text">当前{{ currentMealPeriod }}时段暂无房间预约餐食</div>
          </div>
        </div>
      </div>

      <!-- 主要表格区域 -->
      <div class="main-table-container table-with-border">
        <div class="table-wrapper">
          <!-- 表格标题行 -->
          <div class="table-header-container">
            <!-- 第一行表头 -->
            <div class="table-header-main">
              <div class="header-cell-main room-column bold-header">房号</div>
              <div class="header-cell-main name-column">姓名</div>
              <div class="header-cell-main stay-dates-column">入住-离店</div>
              <div class="header-cell-main restriction-column bold-header">用餐禁忌</div>
              <div class="header-cell-main premium-main-column">高档餐记录</div>
            </div>
            <!-- 第二行表头 -->
            <div class="table-header-sub">
              <div class="header-cell-sub room-column-sub"></div>
              <div class="header-cell-sub name-column-sub"></div>
              <div class="header-cell-sub stay-dates-column-sub"></div>
              <div class="header-cell-sub restriction-column-sub"></div>
              <div
                v-for="index in maxPremiumMealCount"
                :key="index"
                class="header-cell-sub premium-meal-header"
              >
                {{ index }}
              </div>
            </div>
          </div>

          <!-- 表格内容行 -->
          <div class="table-body">
            <!-- Loading状态 -->
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载数据...</div>
            </div>

            <!-- 无数据状态 -->
            <div v-else-if="patientData.length === 0" class="no-data-container">
              <div class="no-data-text">暂无数据</div>
            </div>

            <!-- 数据行 -->
            <div v-else class="table-row" :class="{ 'red-border-blink-row': shouldShowRedBorderForRow(item) }" v-for="(item, index) in patientData" :key="item.roomNumber">
              <div class="table-cell room-column bold-cell">{{ item.roomNumber }}</div>
              <div class="table-cell name-column">{{ item.patientName }}</div>
              <div class="table-cell stay-dates-column">
                <div class="stay-dates">
                  {{ item.checkInDate }}-{{ item.checkOutDate }}
                </div>
              </div>
              <div class="table-cell restriction-column bold-cell">
                <template v-if="item.dietaryRestrictions">
                    <el-tooltip
                      :content="extractTextFromHtml(item.dietaryRestrictions)"
                      placement="top"
                      effect="dark"
                    >
                      <span class="restriction-text" v-html="item.dietaryRestrictions"></span>
                    </el-tooltip>
                </template>
                <span v-else>-</span>
              </div>
              <!-- <div class="table-cell restriction-column">
                <span class="restriction-tag" v-if="item.dietaryRestrictions">{{ item.dietaryRestrictions }}</span>
                <span class="no-restriction" v-else>无</span>
              </div> -->
              <div
                v-for="index in maxPremiumMealCount"
                :key="index"
                class="table-cell premium-meal-cell"
              >
                <span class="recent-premium-meal">{{ getRecentPremiumMeal(item, index - 1) }}</span>
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>

    <!-- 高档餐详情弹窗 -->
    <div v-if="showPremiumMealModal" class="modal-overlay" @click="closePremiumMealModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>高档餐使用详情</h3>
          <button class="close-btn" @click="closePremiumMealModal">×</button>
        </div>
        <div class="modal-body">
          <div class="member-info">
            <p><strong>会员姓名：</strong>{{ selectedMember.patientName }}</p>
            <p><strong>房间号：</strong>{{ selectedMember.roomNumber }}</p>
            <p><strong>总高档餐：</strong>{{ selectedMember.totalPremiumMeals }}份</p>
            <p><strong>已使用：</strong>{{ selectedMember.usedPremiumMeals }}份</p>
            <p><strong>剩余：</strong>{{ selectedMember.remainingPremiumMeals }}份</p>
          </div>
          <div class="premium-meal-details">
            <h4>使用记录：</h4>
            <div v-if="premiumMealRecords.length > 0" class="records-list">
              <div v-for="(record, index) in premiumMealRecords" :key="record.id || index" class="record-item">
                <div class="record-header">
                  <div class="record-date">{{ formatFullDate(record.consumeTime) }}</div>
                </div>
                <div class="record-content">
                  <div class="meal-detail">
                    <strong>高档餐详情：</strong>{{ record.premiumMealDetail }}
                  </div>
                  <div class="record-info">
                    <span class="quantity">数量：{{ record.quantity }}份</span>
                  </div>
                  <div class="record-remark" v-if="record.remark && record.remark !== record.premiumMealDetail">
                    <strong>备注：</strong>{{ record.remark }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-records">
              <p>暂无使用记录</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, toRaw } from 'vue'
import { allListMealReservation, updateMealReservation } from "@/api/business/mealReservation";
import { getAllMemberCheckins } from "@/api/business/memberCheckin";
import { listRecord } from "@/api/business/material";
// import { listPremiumMeal } from "@/api/business/premiumMeal"; // 不再使用，改为从getAllMemberCheckins数据中获取


// 响应式数据
const selectedRoom = ref('101')



// 时间检查定时器
let timeCheckTimer = null

// 数据刷新定时器
let dataRefreshTimer = null

// 实时系统时间
const currentSystemTime = ref('')

// 房间数据，包含房间号和份数 (27个房间)
const roomData = ref([
  { roomNumber: '101', quantity: 0 },
  { roomNumber: '102', quantity: 0 },
  { roomNumber: '103', quantity: 0 },
  { roomNumber: '104', quantity: 0 },
  { roomNumber: '105', quantity: 0 },
  { roomNumber: '106', quantity: 0 },
  { roomNumber: '107', quantity: 0 },
  { roomNumber: '108', quantity: 0 },
  { roomNumber: '109', quantity: 0 },
  { roomNumber: '201', quantity: 0 },
  { roomNumber: '202', quantity: 0 },
  { roomNumber: '203', quantity: 0 },
  { roomNumber: '204', quantity: 0 },
  { roomNumber: '205', quantity: 0 },
  { roomNumber: '206', quantity: 0 },
  { roomNumber: '207', quantity: 0 },
  { roomNumber: '208', quantity: 0 },
  { roomNumber: '209', quantity: 0 },
  { roomNumber: '301', quantity: 0 },
  { roomNumber: '302', quantity: 0 },
  { roomNumber: '303', quantity: 0 },
  { roomNumber: '304', quantity: 0 },
  { roomNumber: '305', quantity: 0 },
  { roomNumber: '306', quantity: 0 },
  { roomNumber: '307', quantity: 0 },
  { roomNumber: '308', quantity: 0 },
  { roomNumber: '309', quantity: 0 }
])

// 餐食预约数据
const mealReservationData = ref([])

// 弹窗相关数据
const showPremiumMealModal = ref(false)
const selectedMember = ref({})
const premiumMealRecords = ref([])

// 会员数据 - 从API获取
const patientData = ref([])
const loading = ref(false) // 数据加载状态


// 计算属性

// 只显示有预约的房间 - 过滤出当前时段有预约餐的房间
const roomsWithReservations = computed(() => {
  const roomsWithMeals = roomData.value.filter(room => room.quantity > 0)
  // 按房间号排序，确保显示顺序一致

  return roomsWithMeals.sort((a, b) => {
    const roomA = a.roomNumber
    const roomB = b.roomNumber

    // 提取数字部分进行比较
    const numA = parseInt(roomA.replace(/[^\d]/g, '')) || 0
    const numB = parseInt(roomB.replace(/[^\d]/g, '')) || 0

    // 如果数字部分相同，按字符串排序
    if (numA === numB) {
      return roomA.localeCompare(roomB)
    }

    // 按数字大小排序
    return numA - numB
  })
})

// 动态计算房间区域高度
const roomHeaderStyle = computed(() => {
  const roomCount = roomsWithReservations.value.length
  if (roomCount === 0) {
    return { minHeight: '80px' } // 没有房间时最小化
  } else if (roomCount <= 9) {
    return { minHeight: '120px' } // 少量房间时适中高度
  } else {
    return { minHeight: '165zpx' } // 房间较多时使用更大高度
  }
})

// 计算各用餐时段的总份数
const mealTotals = computed(() => {
  const totals = {
    breakfast: 0,
    lunch: 0,
    dinner: 0
  }

  mealReservationData.value.forEach((reservation, index) => {
    if (reservation.roomNumber && reservation.mealTime && reservation.quantity) {
      // 使用标准化方法处理mealTime
      const mealTimes = normalizeMealTime(reservation.mealTime)

      mealTimes.forEach(mealTime => {
        if (mealTime === 'breakfast') {
          totals.breakfast += reservation.quantity
        } else if (mealTime === 'lunch') {
          totals.lunch += reservation.quantity
        } else if (mealTime === 'dinner') {
          totals.dinner += reservation.quantity
        } else {
        }
      })
    } else {
    }
  })

  return totals
})

// 获取当前时间对应的预约日期和用餐时段
const getMealSchedule = () => {
  const now = new Date()
  const currentHour = now.getHours()

  let targetDate = new Date(now)
  let mealPeriod = ''

  if (currentHour >= 19) {
    // 7pm后显示次日早餐
    targetDate.setDate(targetDate.getDate() + 1)
    mealPeriod = '早餐'
  } else if (currentHour >= 13) {
    // 1pm后显示当天晚餐
    mealPeriod = '晚餐'
  } else if (currentHour >= 8) {
    // 8am后显示当天午餐
    mealPeriod = '午餐'
  } else {
    // 8am前显示当天早餐
    mealPeriod = '早餐'
  }

  const year = targetDate.getFullYear()
  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0')
  const day = targetDate.getDate().toString().padStart(2, '0')
  const dateString = `${year}.${month}.${day}`

  return { date: dateString, period: mealPeriod }
}

// 当前预约日期（显示格式：YYYY.MM.DD）
const currentDate = computed(() => {
  return getMealSchedule().date
})

// 当前预约日期（API格式：YYYY-MM-DD）
const currentDateForAPI = computed(() => {
  return getMealSchedule().date.replace(/\./g, '-')
})

// 当前用餐时段
const currentMealPeriod = computed(() => {
  return getMealSchedule().period
})

// 获取当前用餐时段对应的英文值
const getCurrentMealType = () => {
  const period = currentMealPeriod.value
  if (period === '早餐') return 'breakfast'
  if (period === '午餐') return 'lunch'
  if (period === '晚餐') return 'dinner'
  return 'breakfast' // 默认
}

// 标准化mealTime值，确保匹配准确
const normalizeMealTime = (mealTime) => {
  if (!mealTime) return []

  // 分割并清理每个时段
  const times = mealTime.split(',').map(time => time.trim().toLowerCase())

  // 标准化可能的变体
  return times.map(time => {
    switch(time) {
      case 'breakfast':
      case '早餐':
        return 'breakfast'
      case 'lunch':
      case '午餐':
      case '午餐':
        return 'lunch'
      case 'dinner':
      case '晚餐':
        return 'dinner'
      default:
        return time
    }
  })
}

// 计算实际显示的数据条数
const actualDataCount = computed(() => {
  return patientData.value.length
})

// 计算行高用的数据条数：小于10条按10条计算，超过10条按实际条数计算
const currentPageDataCount = computed(() => {
  const actualCount = patientData.value.length
  return actualCount < 10 ? 10 : actualCount + 1
})

// 计算最大高档餐数量，用于动态生成表头，基于havePremiumMealNum（总高档餐数量）
const maxPremiumMealCount = computed(() => {
  if (!patientData.value || patientData.value.length === 0) {
    return 0 // 没有数据时返回0列
  }

  let maxCount = 0

  patientData.value.forEach((item) => {
    // 基于havePremiumMealNum（总高档餐数量）或totalPremiumMeals
    const premiumMealCount = item.havePremiumMealNum || item.totalPremiumMeals || 0
    if (premiumMealCount && typeof premiumMealCount === 'number') {
      maxCount = Math.max(maxCount, premiumMealCount)
    }
  })

  console.log('最大高档餐数量(基于havePremiumMealNum):', maxCount) // 调试日志
  return maxCount // 返回havePremiumMealNum的最大值，比如最大是5，就生成5列
})

// 计算高档餐列宽，刚好够显示数字内容，不要省略号
const dynamicColumnWidth = computed(() => {
  // 固定列宽，90px足够显示数字和日期内容
  return '90px'  // 90px列宽，适合显示数字表头和日期内容
})

// 动态计算网格布局，优化各列宽度分配
const dynamicGridColumns = computed(() => {
  // 基础列宽度：刚好够内容显示，不要省略号
  const baseColumns = '60px 70px 110px 1fr' // 房号、姓名、入住-离店、用餐禁忌(自适应)
  // 高档餐列：所有列宽一致，90px适合显示数字表头和日期内容
  const premiumColumns = Array(maxPremiumMealCount.value).fill(dynamicColumnWidth.value).join(' ')
  return `${baseColumns} ${premiumColumns}`
})

// 方法
const selectRoom = (room) => {
  selectedRoom.value = room
  // 这里可以根据房间号加载对应的数据
  loadRoomData(room)
}

const loadRoomData = (roomNumber) => {
  // 模拟根据房间号加载数据
  // 实际项目中这里会调用API获取数据
}

// 获取餐食预约数据
const loadMealReservationData = async () => {
  try {
    const queryData = {
      reservationDate: currentDateForAPI.value
    }

    const response = await allListMealReservation(queryData)

    mealReservationData.value = response || []

    // 更新房间数据统计
    updateRoomQuantities()

    // 验证数据一致性
    setTimeout(() => {
      validateDataConsistency()
      logAllRoomNumbers()
    }, 100) // 延迟一点确保所有计算完成
  } catch (error) {
  }
}

// 获取入住会员数据（使用getAllMemberCheckins，不分页）
const loadMemberCheckinData = async () => {
  try {
    loading.value = true // 开始加载

    const response = await getAllMemberCheckins()

    // 获取所有数据
    const memberList = response.data || response || []

    // 将所有入住会员数据转换为表格需要的格式，不进行去重
    const transformedData = await Promise.all(memberList.map(async (member, index) => {
      // 获取该会员的最后高档餐使用日期
      // const lastSupplementDate = await getLastPremiumMealDate(member.id)
      const lastSupplementDate = member.haveUsePremiumMealList.length > 0 ? formatDate(member.haveUsePremiumMealList[0].consumeTime) : ''


      // 进补食材详情改为点击时获取，不在初始加载时获取
      const supplementDetails = []

      return {
        id: member.id, // 添加会员ID用于查询进补记录
        roomNumber: member.roomNumber || '',
        patientName: member.memberName || '',
        dietaryRestrictions: member.allergyDetail || '',
        checkInDate: member.checkinDate ? formatDate(member.checkinDate) : '',
        checkOutDate: member.checkoutDate ? formatDate(member.checkoutDate) : '',
        checkinDateRaw: member.checkinDate, // 保留原始入住日期用于排序
        totalPremiumMeals: member.havePremiumMealNum || 0, // 总高档餐数量
        usedPremiumMeals: member.haveUsePremiumMealNum || 0, // 已使用高档餐数量
        remainingPremiumMeals: (member.havePremiumMealNum || 0) - (member.haveUsePremiumMealNum || 0), // 剩余高档餐数量
        lastSupplementDate: lastSupplementDate, // 最后进补日期
        supplementDetails: supplementDetails, // 进补详情
        supplementLoading: false, // 进补详情加载状态
        haveUsePremiumMealList: member.haveUsePremiumMealList || [], // 保存原始的高档餐使用记录
        haveUsePremiumMealNum: member.haveUsePremiumMealNum || 0, // 已使用高档餐数量
        havePremiumMealNum: member.havePremiumMealNum || 0 // 总高档餐数量 - 用于动态生成列
      }
    }))

    // 按楼层分组，然后在每个楼层内按入住时间排序
    const sortedData = transformedData.sort((a, b) => {
      const roomA = a.roomNumber || ''
      const roomB = b.roomNumber || ''

      // 如果房间号为空，放到最后
      if (!roomA && !roomB) return 0
      if (!roomA) return 1
      if (!roomB) return -1

      // 提取楼层信息（房间号的第一位数字）
      const floorA = Math.floor(parseInt(roomA.replace(/\D/g, '')) / 100) || 0
      const floorB = Math.floor(parseInt(roomB.replace(/\D/g, '')) / 100) || 0

      // 先按楼层排序
      if (floorA !== floorB) {
        return floorA - floorB
      }

      // 同一楼层内，按入住时间排序（越早入住越在上面）
      const checkinA = a.checkinDateRaw ? new Date(a.checkinDateRaw) : new Date(0)
      const checkinB = b.checkinDateRaw ? new Date(b.checkinDateRaw) : new Date(0)

      // 如果入住时间相同，再按房间号排序
      if (checkinA.getTime() === checkinB.getTime()) {
        const numA = parseInt(roomA.replace(/\D/g, '')) || 0
        const numB = parseInt(roomB.replace(/\D/g, '')) || 0
        return numA - numB
      }

      return checkinA - checkinB
    })

    // 使用排序后的数据
    patientData.value = sortedData

    // 调试：显示排序后的房间号和入住时间
    console.log('排序后的数据（前10个）:', sortedData.slice(0, 10).map(item => ({
      roomNumber: item.roomNumber,
      patientName: item.patientName,
      checkInDate: item.checkInDate,
      checkinDateRaw: item.checkinDateRaw
    })))

    // 显示排序后的房间号顺序（前10个）
    const roomNumbers = sortedData.slice(0, 10).map(item => item.roomNumber).filter(Boolean)
    

  } catch (error) {
    patientData.value = []
  } finally {
    loading.value = false // 结束加载
  }
}

// 格式化日期函数 (月.日)
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}.${day}`
}

// 格式化完整日期函数 (2025.07.06)
const formatFullDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}.${month}.${day}`
}

// 获取所有进补记录（物料出库记录）
const getSupplementRecords = async () => {
  try {
    const response = await listRecord({
      recordType: 2, // 出库记录
      useType: 1     // 入住会员使用
    })
    return response || []
  } catch (error) {
    return []
  }
}

// 获取指定会员的最后高档餐使用日期
const getLastPremiumMealDate = async (memberCheckinId) => {
  if (!memberCheckinId) return ''

  try {
    // 调用API获取该会员的高档餐使用记录
    const response = await listPremiumMeal({
      memberCheckinId: memberCheckinId
    })

    if (!response || response.length === 0) return ''

    // 按食用时间排序，取最新的一条
    const sortedRecords = response.sort((a, b) =>
      new Date(b.consumeTime) - new Date(a.consumeTime)
    )

    const lastRecord = sortedRecords[0]
    return lastRecord.consumeTime ? formatDate(lastRecord.consumeTime) : ''
  } catch (error) {
    return ''
  }
}

// 获取指定会员的进补食材详情
const getSupplementDetails = async (memberCheckinId) => {
  if (!memberCheckinId) return []

  try {
    // 调用API获取该会员的高档餐使用记录
    const response = await listPremiumMeal({
      memberCheckinId: memberCheckinId
    })

    if (!response || response.length === 0) return []

    // 按食用时间排序
    const sortedRecords = response.sort((a, b) =>
      new Date(a.consumeTime) - new Date(b.consumeTime)
    )

    // 转换为进补详情格式
    return sortedRecords.map((record, index) => ({
      index: index + 1,
      date: record.consumeTime ? formatDate(record.consumeTime) : '',
      ingredient: record.premiumMealDetail || ''
    }))
  } catch (error) {
    return []
  }
}

// 获取指定会员的最后进补日期（保留原方法，以防其他地方使用）
const getLastSupplementDate = (memberCheckinId, supplementRecords) => {
  if (!memberCheckinId || !supplementRecords.length) return ''

  // 筛选出该会员的进补记录
  const memberRecords = supplementRecords.filter(record =>
    record.memberCheckinId === memberCheckinId
  )

  if (memberRecords.length === 0) return ''

  // 按创建时间排序，取最新的一条
  const sortedRecords = memberRecords.sort((a, b) =>
    new Date(b.createTime) - new Date(a.createTime)
  )

  const lastRecord = sortedRecords[0]
  return lastRecord.createTime ? formatDate(lastRecord.createTime) : ''
}

// 更新房间数量统计 - 以房间为维度统计当前时段预约餐总数
const updateRoomQuantities = () => {
  // 重置所有房间数量为0
  roomData.value.forEach(room => {
    room.quantity = 0
  })

  const currentMealType = getCurrentMealType()

  // 收集所有需要统计的房间号
  const roomsToProcess = new Set()

  // 遍历餐食预约数据，收集当前时段的所有房间号
  mealReservationData.value.forEach(reservation => {
    if (reservation.roomNumber && reservation.mealTime && reservation.quantity) {
      const mealTimes = normalizeMealTime(reservation.mealTime)

      // 如果包含当前用餐时段，记录这个房间号
      if (mealTimes.includes(currentMealType)) {
        roomsToProcess.add(reservation.roomNumber)
      }
    }
  })


  // 确保所有需要的房间都在roomData中
  roomsToProcess.forEach(roomNumber => {
    const existingRoom = roomData.value.find(r => r.roomNumber === roomNumber)
    if (!existingRoom) {
      roomData.value.push({ roomNumber: roomNumber, quantity: 0 })
    }
  })

  // 遍历餐食预约数据，按房间维度统计当前时段的预约总数
  mealReservationData.value.forEach(reservation => {
    if (reservation.roomNumber && reservation.mealTime && reservation.quantity) {
      // 使用标准化方法处理mealTime
      const mealTimes = normalizeMealTime(reservation.mealTime)

      // 检查是否包含当前用餐时段
      if (mealTimes.includes(currentMealType)) {
        // 找到对应的房间并累加数量
        const room = roomData.value.find(r => r.roomNumber === reservation.roomNumber)
        if (room) {
          room.quantity += reservation.quantity
        } else {
        }
      } else {
      }
    }
  })

  // 统计结果
  const roomsWithMeals = roomData.value.filter(room => room.quantity > 0)
  const roomTotal = roomsWithMeals.reduce((sum, room) => sum + room.quantity, 0)

  // 对比汇总数据
  const mealTotalValue = currentMealType === 'breakfast' ? mealTotals.value.breakfast :
                        currentMealType === 'lunch' ? mealTotals.value.lunch :
                        mealTotals.value.dinner

}



// 获取高档餐使用情况，按时间正序排列（1、2、3...）
const getRecentPremiumMeal = (member, index) => {
  // 获取总高档餐数量和已使用数量
  const totalPremiumMeals = member.havePremiumMealNum || member.totalPremiumMeals || 0
  const usedPremiumMeals = member.haveUsePremiumMealNum || member.usedPremiumMeals || 0

  // 如果当前索引超出总高档餐数量，返回空白
  if (index >= totalPremiumMeals) {
    return ''
  }

  // 如果当前索引在已使用范围内，显示具体的使用记录
  if (index < usedPremiumMeals && member.haveUsePremiumMealList && member.haveUsePremiumMealList.length > 0) {
    // 按消费时间正序排列，时间早的在前（1、2、3...）
    const sortedRecords = [...member.haveUsePremiumMealList].sort((a, b) =>
      new Date(a.consumeTime) - new Date(b.consumeTime)
    )

    if (index < sortedRecords.length) {
      const record = sortedRecords[index]
      const type = record.type === 'breakfast' ? '早' : record.type === 'lunch' ? '午' : '晚'
      const detail = record.premiumMealDetail || ''

      // 格式化日期为 M.d 格式，不填充0
      if (record.consumeTime) {
        const date = new Date(record.consumeTime)
        const month = date.getMonth() + 1
        const day = date.getDate()
        return `${month}.${day}(${type}${detail})`
      }
    }
  }

  // 如果当前索引在未使用范围内，显示序号
  if (index >= usedPremiumMeals) {
    return (index + 1).toString()
  }

  return ''
}

// 获取用餐时段对应的CSS类名
const getMealPeriodClass = () => {
  const period = currentMealPeriod.value
  if (period === '早餐') return 'breakfast'
  if (period === '午餐') return 'lunch'
  if (period === '晚餐') return 'dinner'
  return 'breakfast' // 默认
}

// 检查是否需要显示红色闪烁边框
const shouldShowRedBorder = (room) => {
  const now = new Date()
  const currentHour = now.getHours()

  // 检查当前时间是否在指定时间段内 (9-12am 或 4-6pm)
  const isInTimeRange = (currentHour >= 9 && currentHour < 12) || (currentHour >= 16 && currentHour < 18)

  if (!isInTimeRange) {
    return false
  }

  // 检查房间内是否有当天入住的会员
  const today = new Date()
  const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`

  const hasCheckinToday = patientData.value.some(patient => {
    return patient.roomNumber === room.roomNumber &&
           patient.checkinDateRaw &&
           patient.checkinDateRaw.startsWith(todayString)
  })

  return hasCheckinToday
}

// 检查表格行是否需要显示红色闪烁边框
const shouldShowRedBorderForRow = (patient) => {
  const now = new Date()
  const currentHour = now.getHours()

  // 检查当前时间是否在指定时间段内 (9-12am 或 4-6pm)
  const isInTimeRange = (currentHour >= 9 && currentHour < 12) || (currentHour >= 16 && currentHour < 18)

  if (!isInTimeRange) {
    return false
  }

  // 检查患者是否为当天入住
  const today = new Date()
  const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`

  return patient.checkinDateRaw && patient.checkinDateRaw.startsWith(todayString)
}

// 更新系统时间
const updateSystemTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  currentSystemTime.value = `${year}-${month}-${day} ${hours}:${minutes}`
}

// 启动时间检查定时器
const startTimeCheck = () => {
  // 立即更新一次系统时间
  updateSystemTime()

  timeCheckTimer = setInterval(() => {
    // 每分钟检查一次时间变化，触发计算属性重新计算
    updateSystemTime() // 更新系统时间显示
    const schedule = getMealSchedule()
  }, 60000) // 每分钟检查一次
}

// 停止时间检查定时器
const stopTimeCheck = () => {
  if (timeCheckTimer) {
    clearInterval(timeCheckTimer)
    timeCheckTimer = null
  }
}

// 启动数据刷新定时器
const startDataRefresh = () => {

  dataRefreshTimer = setInterval(async () => {
    try {
      // 刷新餐食预约数据
      await loadMealReservationData()
      // 刷新入住会员数据
      await loadMemberCheckinData()
    } catch (error) {
      console.error('定时刷新数据失败:', error)
    }
  }, 3600000) // 60分钟 = 3600000毫秒
}

// 停止数据刷新定时器
const stopDataRefresh = () => {
  if (dataRefreshTimer) {
    clearInterval(dataRefreshTimer)
    dataRefreshTimer = null
  }
}

// 跳转到指定页面
const goToPage = (pageIndex) => {
  currentPage.value = pageIndex
  // 点击后重新启动自动翻页
  stopAutoPage()
  startAutoPage()
}

// 显示高档餐详情弹窗
const showPremiumMealDetails = async (member) => {
  selectedMember.value = member
  showPremiumMealModal.value = true

  try {
    // 从会员数据中的haveUsePremiumMealList获取高档餐使用记录
    const premiumMealList = member.haveUsePremiumMealList || []

    // 转换数据格式以适配弹窗显示
    premiumMealRecords.value = premiumMealList.map(record => ({
      id: record.id,
      createTime: record.consumeTime, // 使用食用时间
      mealType: getMealTypeFromTime(record.consumeTime), // 根据时间推断用餐时段
      quantity: record.quantity || 1, // 默认数量为1
      remark: record.premiumMealDetail, // 使用高档餐详情作为备注
      premiumMealDetail: record.premiumMealDetail,
      consumeTime: record.consumeTime
    })).sort((a, b) =>
      // 按消费时间倒序排列，最新的在前面
      new Date(b.consumeTime) - new Date(a.consumeTime)
    )

    // 在点击时获取进补食材详情，并更新到会员数据中
    if (!member.supplementDetails || member.supplementDetails.length === 0) {

      // 设置加载状态
      const memberIndex = patientData.value.findIndex(item => item.id === member.id)
      if (memberIndex !== -1) {
        patientData.value[memberIndex].supplementLoading = true
      }

      try {
        const supplementDetails = await getSupplementDetails(member.id)

        // 更新patientData中对应会员的supplementDetails
        if (memberIndex !== -1) {
          patientData.value[memberIndex].supplementDetails = supplementDetails
          patientData.value[memberIndex].supplementLoading = false
        }
      } catch (error) {
        if (memberIndex !== -1) {
          patientData.value[memberIndex].supplementLoading = false
        }
      }
    }

  } catch (error) {
    premiumMealRecords.value = []
  }
}

// 关闭高档餐详情弹窗
const closePremiumMealModal = () => {
  showPremiumMealModal.value = false
  selectedMember.value = {}
  premiumMealRecords.value = []
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${month}.${day} ${hours}:${minutes}`
}

// 获取用餐时段名称
const getMealTypeName = (mealType) => {
  const mealTypeMap = {
    'breakfast': '早餐',
    'lunch': '午餐',
    'dinner': '晚餐'
  }
  return mealTypeMap[mealType] || mealType
}

// 根据时间推断用餐时段
const getMealTypeFromTime = (dateTimeStr) => {
  if (!dateTimeStr) return 'breakfast'

  const date = new Date(dateTimeStr)
  const hour = date.getHours()

  if (hour >= 6 && hour < 11) {
    return 'breakfast' // 早餐 6:00-11:00
  } else if (hour >= 11 && hour < 17) {
    return 'lunch' // 午餐 11:00-17:00
  } else {
    return 'dinner' // 晚餐 17:00-次日6:00
  }
}

// 生成进补食材详情的完整文本（用于悬浮提示）
const getSupplementDetailsText = (supplementDetails) => {
  if (!supplementDetails || supplementDetails.length === 0) {
    return '暂无进补记录'
  }

  return supplementDetails.map(detail =>
    `${detail.index}、${detail.date}（${detail.ingredient}）`
  ).join('；')
}

// 从HTML富文本中提取纯文本内容（用于tooltip显示）
const extractTextFromHtml = (htmlString) => {
  if (!htmlString) return ''

  // 创建一个临时的DOM元素来解析HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlString

  // 提取纯文本内容
  return tempDiv.textContent || tempDiv.innerText || ''
}

// 验证房间统计与汇总数据的一致性
const validateDataConsistency = () => {

  // 获取当前时段
  const currentMealType = getCurrentMealType()
  const currentPeriod = currentMealPeriod.value

  // 计算房间统计总数
  const roomsWithMeals = roomData.value.filter(room => room.quantity > 0)
  const roomTotal = roomsWithMeals.reduce((sum, room) => sum + room.quantity, 0)

  // 获取对应的汇总数据
  const mealTotalValue = currentMealType === 'breakfast' ? mealTotals.value.breakfast :
                        currentMealType === 'lunch' ? mealTotals.value.lunch :
                        mealTotals.value.dinner

  if (roomTotal !== mealTotalValue) {

    // 详细分析原始数据
    mealReservationData.value.forEach(reservation => {
      if (reservation.roomNumber && reservation.mealTime && reservation.quantity) {
        const mealTimes = reservation.mealTime.split(',')
      }
    })
  } else {
  }

}

// 显示所有预约数据中的房间号
const logAllRoomNumbers = () => {

  const allRoomNumbers = new Set()
  const roomNumberDetails = []

  mealReservationData.value.forEach((reservation, index) => {
    if (reservation.roomNumber) {
      allRoomNumbers.add(reservation.roomNumber)
      roomNumberDetails.push({
        index,
        roomNumber: reservation.roomNumber,
        mealTime: reservation.mealTime,
        quantity: reservation.quantity
      })
    }
  })
}



// 监听用餐时段变化，重新加载数据
watch(currentMealPeriod, (newPeriod, oldPeriod) => {
  if (newPeriod !== oldPeriod) {
    loadMealReservationData() // 重新加载餐食预约数据
  }
})

// 监听预约日期变化，重新加载数据
watch(currentDate, (newDate, oldDate) => {
  if (newDate !== oldDate) {
    loadMealReservationData() // 重新加载餐食预约数据
  }
})



// 生命周期
onMounted(() => {
  loadRoomData(selectedRoom.value)
  loadMealReservationData() // 加载餐食预约数据
  loadMemberCheckinData() // 加载真实数据
  startTimeCheck() // 启动时间检查
  startDataRefresh() // 启动数据刷新定时器
})

onUnmounted(() => {
  stopTimeCheck() // 组件销毁时停止时间检查
  stopDataRefresh() // 组件销毁时停止数据刷新定时器
})
</script>

<style scoped>
.kitchen-container {
  padding: 0;
  background-color: #e2efda;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 1200px; /* 设置最小宽度 */
  color: #333;
}

.table-layout-page {
  background: #e2efda;
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  color: #333;
}

/* 顶部房间号码区域 */
.room-header {
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  flex-shrink: 0;
  min-height: 120px; /* 设置最小高度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 预约时间和用餐时段信息 - 上部分 */
.meal-info-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px; /* 进一步缩小到4px */
  padding: 4px 12px; /* 进一步缩小到6px 12px */
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  border: 2px solid #c5d9b7;
  flex-shrink: 0;
}

/* 左侧信息区域 */
.meal-info-left {
  display: flex;
  gap: 35px; /* 从40px进一步缩小到35px */
}

/* 右侧总份数区域 */
.meal-info-right {
  display: flex;
  gap: 20px; /* 从25px进一步缩小到20px */
}

.meal-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 总份数项目样式 */
.meal-total-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 早餐总份数样式 */
.breakfast-total {
  color: #C72C41;
}

/* 午餐总份数样式 */
.lunch-total {
  color: #E6AF2E;
}

/* 晚餐总份数样式 */
.dinner-total {
  color: #3A6EA5;
}

.total-label {
  font-size: 35px;
  font-weight: 700;
}

.total-value {
  font-size: 35px;
  font-weight: 700;
}

.info-label {
  color: #333;
  font-size: 22px; /* 进一步缩小到14px */
  font-weight: 500;
}

.info-value {
  color: #333;
  font-size: 27px; /* 进一步缩小到16px */
  font-weight: 700;
  background: rgba(255, 255, 255, 0.8);
  padding: 3px 6px; /* 进一步缩小到3px 6px */
  border-radius: 8px;
  border: 2px solid #c5d9b7;
}

/* 用餐时段背景色 */
.meal-period.breakfast {
  background: #C72C41 !important;
  color: white !important;
}

.meal-period.lunch {
  background: #E6AF2E !important;
  color: white !important;
}

.meal-period.dinner {
  background: #3A6EA5 !important;
  color: white !important;
}

.room-grid {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 8px;
  width: 100%;
  flex: 1;
  align-content: flex-start;
  min-height: 60px; /* 最小高度确保至少显示一行 */
}

.room-card {
  background: #f0f0f0;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 固定尺寸 */
  width: 118px;
  height: 60px;
  flex-shrink: 0; /* 防止被压缩 */
}

/* 根据用餐时段动态设置背景色 */
.room-card.breakfast {
  background: #C72C41;
}

.room-card.lunch {
  background: #E6AF2E;
}

.room-card.dinner {
  background: #3A6EA5;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

.room-card.active {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 红色闪烁边框样式 */
.room-card.red-border-blink {
  border: 3px solid #ff0000 !important;
  animation: redBorderBlink 1s infinite;
}

@keyframes redBorderBlink {
  0%, 50% {
    border-color: #ff0000;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
  }
  51%, 100% {
    border-color: transparent;
    box-shadow: none;
  }
}

/* 表格行红色闪烁边框样式 */
.table-row.red-border-blink-row {
  border-top: 3px solid #ff0000 !important;
  border-bottom: 3px solid #ff0000 !important;
  animation: redBorderBlink 1s infinite;
  background-color: rgba(255, 0, 0, 0.05) !important;
}

.table-row.red-border-blink-row:hover {
  background-color: rgba(255, 0, 0, 0.1) !important;
}

.table-row.red-border-blink-row .table-cell {
  border-right: 4px solid transparent;
}

/* 房间区域空状态样式 */
.empty-rooms-state {
  width: 100%; /* 占据整个容器宽度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #999;
  background: rgba(255, 255, 255, 0.5);
  border: 2px dashed #c5d9b7;
  border-radius: 12px;
  min-height: 80px;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
}

.empty-hint {
  font-size: 14px;
  color: #999;
  font-style: italic;
}

.room-text {
  font-size: 35px;
  font-weight: 700;
  white-space: nowrap;
}

/* 主要表格区域 */
.main-table-container {
  padding: 0;
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 控制整体溢出 */
  height: calc(100vh - 100px); /* 进一步最大化表格显示空间 */
  min-height: calc(100vh - 100px); /* 确保最小高度 */
}

/* 表格顶部边框样式 */
.table-with-border {
  border-top: 4px solid #c5d9b7;
}

.table-wrapper {
  border: none;
  border-radius: 0;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 表头容器 - 使用动态Grid布局 */
.table-header-container {
  display: grid;
  grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  grid-template-rows: 40px 40px; /* 增加子表头行高确保"最近X次"文字不换行 */
  background: rgba(255, 255, 255, 0.8);
  /*border-bottom: 4px solid #d4e4c1; /* 与数据行边框颜色一致 */
  flex-shrink: 0;
  color: #333;
  white-space: nowrap; /* 确保整个表头容器不换行 */
  overflow: visible; /* 允许内容显示 */
}

/* 移除原来的主表头和子表头容器样式 */
.table-header-main,
.table-header-sub {
  display: contents; /* 让子元素直接参与父级Grid布局 */
}

/* 通用表头单元格样式 */
.header-cell-main,
.header-cell-sub {
  padding: 6px 3px;
  font-weight: 400; /* 默认不加粗 */
  color: #333;
  text-align: center;
  border-right: 4px solid #c5d9b7;
  border-bottom: 4px solid #c5d9b7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: center;

  font-weight: 700;
}

/* 主表头字体稍大 */
.header-cell-main {
  font-size: 20px;
}

/* 子表头字体 */
.header-cell-sub {
  font-size: 18px;
  font-weight: 700;
}

/* 房号 - 跨两行 */
.header-cell-main.room-column {
  grid-area: 1 / 1 / 3 / 2; /* 行1-2，列1 */
}

/* 姓名 - 跨两行 */
.header-cell-main.name-column {
  grid-area: 1 / 2 / 3 / 3; /* 行1-2，列2 */
}

/* 入住-离店 - 跨两行 */
.header-cell-main.stay-dates-column {
  grid-area: 1 / 3 / 3 / 4; /* 行1-2，列3 */
}

/* 饮食禁忌 - 跨两行 */
.header-cell-main.restriction-column {
  grid-area: 1 / 4 / 3 / 5; /* 行1-2，列4 */
}

/* 高档餐 - 动态跨列 */
.header-cell-main.premium-main-column {
  grid-column: 5 / -1; /* 从第5列开始到最后一列 */
  grid-row: 1 / 2; /* 第1行 */
}

/* 动态生成的高档餐子列不需要特殊Grid定位 */

/* 动态生成的高档餐表头样式 */
.header-cell-sub.premium-meal-header {
  white-space: nowrap !important; /* 强制不换行 */
  overflow: visible; /* 允许内容显示，不隐藏 */
  text-overflow: clip; /* 不显示省略号 */
  font-size: 14px; /* 增大字体，数字显示更清晰 */
  padding: 4px 2px; /* 增加内边距，利用更大的列宽空间 */
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 4px solid #d4e4c1; /* 与数据行边框颜色一致 */
  font-weight: 700; /* 表头加粗 */
  min-width: 90px; /* 更新最小宽度到90px */
  max-width: 90px; /* 更新最大宽度到90px，保持一致 */
  flex-shrink: 0; /* 不允许收缩，保持列宽一致 */
  /* 完全依赖Grid布局控制宽度 */
}

/* 动态生成的高档餐数据列样式 */
.table-cell.premium-meal-cell {
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容完全显示 */
  text-overflow: clip; /* 不显示省略号 */
  /* 完全依赖Grid布局确保列宽一致 */
}

/* 隐藏不需要的子表头元素 */
.header-cell-sub.room-column-sub,
.header-cell-sub.name-column-sub,
.header-cell-sub.stay-dates-column-sub,
.header-cell-sub.restriction-column-sub {
  display: none;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  flex: 1;
  overflow: hidden; /* 不显示滚动条 */
  display: flex;
  flex-direction: column;
  height: calc(100% - 90px); /* 减去两行表头高度 (45px + 45px) */
  /* 确保表格体能容纳所有数据行 */
}

.table-row {
  display: grid;
  grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  border-bottom: 4px solid #d4e4c1;
  transition: background-color 0.2s ease;
  height: calc((100vh - 200px) / v-bind(currentPageDataCount)); /* 基于视窗高度直接计算，确保所有行都能显示 */
  min-height: 25px; /* 进一步减小最小高度，确保能容纳更多行 */
  max-height: 80px; /* 设置最大高度，避免数据太少时行高过大 */
}

.table-row:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.table-cell {
  padding: 4px 2px; /* 减少内边距，为更多数据留空间 */
  text-align: center;
  border-right: 4px solid #d4e4c1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(0.4vw + 6px + (100vh - 220px) / v-bind(currentPageDataCount) * 0.06); /* 更小的基础字体，适应更多数据 */
  font-weight: 400; /* 默认不加粗 */
  overflow: hidden; /* 控制溢出 */
  line-height: 1.2; /* 紧凑的行高 */
  word-break: keep-all; /* 保持单词完整 */
  white-space: nowrap; /* 不换行 */
}

/* 饮食禁忌列特殊处理 */
.table-cell.restriction-column {
  justify-content: flex-start; /* 左对齐 */
  padding-left: 8px; /* 左边距稍大一些 */
}

/* 入住-离店列特殊处理 */
.table-cell.stay-dates-column {
  /* 移除不换行限制，让内容完整显示 */
}

/* 高档餐子列特殊处理 */
.table-cell.recent-premium-1-column,
.table-cell.recent-premium-2-column,
.table-cell.recent-premium-3-column {
  /* 移除不换行限制，让内容完整显示 */
}

.table-cell:last-child {
  border-right: none;
  /* 移除左对齐，保持默认的居中对齐 */
}

/* 饮食禁忌标签 */
.restriction-tag {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 24px; /* 增大禁忌标签字体 */
  font-weight: 600;
  display: inline-block;
  text-align: center; /* 标签内文字居中 */
}



.no-restriction {
  color: #333;
  font-size: 24px; /* 增大"无禁忌"字体 */
  text-align: center; /* 居中对齐 */
}

.no-supplement {
  color: #333;
  font-size: 24px;
}

/* 总高档餐数量样式 */
.total-premium-count {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
}

/* 已使用高档餐次数样式 */
.premium-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
}

/* 可点击样式 */
.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 剩余高档餐次数样式 */
.remaining-premium-count {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
}

/* 进补日期样式 */
.supplement-date {
  color: #333;
  font-size: 24px; /* 增大进补日期字体 */
}

/* 进补食材详情样式 */
.supplement-text {
  color: #409eff;
  font-weight: 500;
  background-color: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.ellipsis-text {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.supplement-item {
  margin-bottom: 2px;
  color: #666;
}


/* 入住-离店日期样式 */
.stay-dates {
  text-align: center;
  color: #333;
  white-space: nowrap;
  width: 100%;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 2px solid #c5d9b7;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.member-info {
  background: rgba(255, 255, 255, 0.6);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.member-info p {
  margin: 8px 0;
  font-size: 16px;
}

.premium-meal-details h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
}

.records-list {
  max-height: 300px;
  overflow-y: auto;
}

.record-item {
  background: white;
  border: 2px solid #c5d9b7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.record-date {
  font-size: 14px;
  color: #666;
  font-weight: 600;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meal-detail {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.record-info {
  display: flex;
  gap: 15px;
}

.meal-type {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.quantity {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.record-remark {
  color: #666;
  font-style: italic;
  font-size: 13px;
}

.no-records {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

/* 新列样式 */
.premium-meals-column,
.last-supplement-column,
.supplement-details-column,
.stay-dates-column,
.recent-premium-1-column,
.recent-premium-2-column,
.recent-premium-3-column {
  text-align: center;
}

/* 总会员数蓝色加粗样式 */
.total-members {
  color: #007bff !important;
  font-weight: 700 !important;
}

/* 表头加粗样式 */
.bold-header {
  font-weight: 700 !important;
}

/* 表格内容加粗样式 */
.bold-cell {
  font-weight: 700 !important;
}

/* 最近高档餐使用情况样式 */
.recent-premium-meal {
  color: #333;
  font-weight: 500;
  width: 100%; /* 占满容器宽度 */
  /* 字体大小继承父元素 .table-cell 的设置 */
  /* 移除不换行限制，让内容完整显示 */
}

/* 饮食禁忌文本完整显示样式 */
.restriction-text {
  width: 100%;
  white-space: nowrap; /* 不换行但完整显示 */
  overflow: visible; /* 允许溢出显示 */
  display: inline-block;
}

/* 饮食禁忌富文本内容样式 */
.restriction-text p {
  margin: 0;
  padding: 0;
  display: inline;
}

.restriction-text span {
  display: inline;
}

/* 姓名列样式 - 处理长姓名 */
.name-column {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 饮食禁忌列样式 - 内容居中对齐 */
.table-row .restriction-column {
  overflow: hidden;
  justify-content: center !important;
  text-align: center !important;
}

/* 饮食禁忌表头样式 - 表头居中 */
.table-header .restriction-column {
  justify-content: center !important;
  text-align: center !important;
}

/* 进补食材详情列样式 - 处理长文本 */
.supplement-details-column {
  overflow: hidden;
}



/* 空状态 */
.table-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  grid-column: 1 / -1;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-description {
  font-size: 14px;
  color: #999;
}

/* 底部统计 */
.bottom-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-top: 4px solid #c5d9b7;
  flex-shrink: 0;
  height: 40px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.stat-value.completed {
  color: #2e7d32;
}

.stat-value.pending {
  color: #f57c00;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header-container,
  .table-row {
    grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  }

  .header-cell {
    padding: 12px 8px;
    font-size: 12px;
  }

  .room-grid {
    gap: 6px;
  }

  .empty-rooms-state {
    padding: 16px;
    min-height: 60px;
  }

  .empty-icon {
    font-size: 24px;
    margin-bottom: 6px;
  }

  .empty-text {
    font-size: 14px;
    margin-bottom: 3px;
  }

  .empty-hint {
    font-size: 12px;
  }

  .meal-info-top {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .meal-info-item {
    justify-content: flex-start;
  }

  .info-label,
  .info-value {
    font-size: 12px;
  }



  .bottom-stats {
    flex-direction: column;
    gap: 16px;
  }
}

/* Loading状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* 无数据状态样式 */
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 300px;
}

.no-data-text {
  font-size: 16px;
  color: #999;
  font-style: italic;
}

/* 进补详情加载状态样式 */
.supplement-loading {
  color: #666;
  font-style: italic;
}

.loading-dots {
  font-size: 12px;
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  80%, 100% {
    opacity: 0.2;
  }
}

/* 响应式设计 - 适配不同分辨率 */

/* 超大屏幕 (1920px+) */
@media (min-width: 1920px) {
  .kitchen-container {
    min-width: 1800px;
  }

  .room-header {
    min-height: 140px;
  }

  .meal-info-top {
    padding: 4px 12px;
  }

  .meal-info-item {
    font-size: 18px;
  }

  .meal-total-item {
    font-size: 18px;
  }


  .header-cell {
    font-size: 20px;
    padding: 12px 8px;
  }

  .table-header-container {
    grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  }

  .table-row {
    grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  }
}

/* 40寸屏幕及以上 (3840px+) */
@media (min-width: 3840px) {
  .kitchen-container {
    min-width: 3600px;
  }

  .table-header-container {
    grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  }

  .table-row {
    grid-template-columns: v-bind(dynamicGridColumns); /* 动态列布局 */
  }

  .header-cell {
    font-size: 20px;
    padding: 10px 8px;
    height: 45px;
  }


  .table-row {
    min-height: 50px;
  }

  .meal-info-item {
    font-size: 22px;
  }

  .meal-total-item {
    font-size: 22px;
  }
}

/* 大屏幕 (1440px - 1919px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  .kitchen-container {
    min-width: 1400px;
  }

  .room-header {
    min-height: 130px;
  }

  .meal-info-item {
    font-size: 16px;
  }

  .meal-total-item {
    font-size: 16px;
  }


  .header-cell {
    font-size: 18px;
  }

}


/* 高度适配 */
@media (max-height: 768px) {
  .room-header {
    min-height: 80px;
    /* max-height: 25vh; */
  }

  .table-row {
    min-height: 45px;
  }
}

@media (max-height: 600px) {
  .room-header {
    min-height: 60px;
    /* max-height: 20vh; */
  }
  .table-row {
    min-height: 35px;
  }


  .meal-info-top {
    padding: 4px 8px;
  }
}

/* 超宽屏适配 (21:9 比例) */
@media (min-aspect-ratio: 21/9) {
  .meal-info-top {
    justify-content: space-between;
    padding: 4px 20px;
  }

  .room-grid {
    justify-content: center;
    max-width: 80%;
    margin: 0 auto;
  }
}

</style>