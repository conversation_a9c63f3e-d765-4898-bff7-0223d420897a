# 后端自动生成代码规范文档

## 1. 控制器（Controller）规范

### 1.1 控制器分类
- **小程序控制器**：使用 `/api/` 前缀，如 `/api/customerSupplyRequest`
- **管理后台控制器**：使用 `/admin/` 前缀，如 `/admin/customerSupplyRequest`

### 1.2 小程序控制器规范

#### 1.2.1 基础结构
```java
@Slf4j
@RestController
@RequestMapping("/api/{entityName}")
@Tag(name = "{EntityName}Controller", description = "小程序{业务名称}接口，提供对{业务名称}信息的增删改查功能")
public class {EntityName}Controller {
    
    @Autowired
    private HttpServletRequest request;
    
    @Autowired
    private {EntityName}Service {entityName}Service;
}
```

#### 1.2.2 必需接口
- **新增接口**：`/add`
- **查询接口**：`/list`、`/get`
- **更新接口**：`/update`
- **删除接口**：`/remove`

#### 1.2.3 接口实现规范
```java
// 新增接口
@Operation(summary = "新增{业务名称}", description = "用户提交新的{业务名称}")
@PostMapping("/add")
@Log(title = "{业务名称}-新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
public {EntityName} add(@Parameter(description = "{业务名称}信息", required = true) @RequestBody {EntityName} {entityName}) {
    String openId = (String) request.getAttribute("openId");
    {entityName}.setOpenId(openId);
    return {entityName}Service.save({entityName});
}

// 查询列表接口
@Operation(summary = "获取{业务名称}列表", description = "根据用户openId获取{业务名称}列表")
@PostMapping("/list")
public List<{EntityName}> list() {
    String openId = (String) request.getAttribute("openId");
    return {entityName}Service.getByOpenId(openId);
}

// 根据ID查询接口
@Operation(summary = "根据ID获取{业务名称}详情", description = "通过ID获取单个{业务名称}的详细信息")
@PostMapping("/get")
public {EntityName} getById(@Parameter(description = "{业务名称}ID", required = true) @RequestBody {EntityName} {entityName}) {
    String openId = (String) request.getAttribute("openId");
    return {entityName}Service.getByIdAndOpenId({entityName}.getId(), openId);
}

// 更新接口
@Operation(summary = "更新{业务名称}", description = "更新{业务名称}信息")
@PostMapping("/update")
@Log(title = "{业务名称}-更新", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
public {EntityName} update(@Parameter(description = "{业务名称}信息", required = true) @RequestBody {EntityName} {entityName}) {
    String openId = (String) request.getAttribute("openId");
    {entityName}.setOpenId(openId);
    return {entityName}Service.updateByIdAndOpenId({entityName});
}

// 删除接口
@Operation(summary = "删除{业务名称}", description = "根据ID删除{业务名称}")
@PostMapping("/remove")
@Log(title = "{业务名称}-删除", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
public Boolean remove(@RequestBody {EntityName} {entityName}) {
    String openId = (String) request.getAttribute("openId");
    return {entityName}Service.deleteByIdAndOpenId({entityName}.getId(), openId);
}
```

### 1.3 管理后台控制器规范

#### 1.3.1 基础结构
```java
@Slf4j
@RestController
@RequestMapping("/admin/{entityName}")
@Tag(name = "{EntityName}AdminController", description = "{业务名称}后台管理接口，提供对{业务名称}信息的增删改查功能")
public class {EntityName}AdminController {
    
    @Autowired
    private {EntityName}Service {entityName}Service;
}
```

#### 1.3.2 必需接口
- **分页查询接口**：`/page`
- **列表查询接口**：`/list`
- **详情查询接口**：`/getById`
- **新增接口**：`/add`
- **更新接口**：`/update`
- **删除接口**：`/delete`

#### 1.3.3 接口实现规范
```java
// 分页查询接口
@Operation(summary = "分页查询{业务名称}列表", description = "根据条件分页查询{业务名称}信息")
@PostMapping("/page")
public Page<{EntityName}> page(@Parameter(description = "查询条件和分页参数", required = true) @RequestBody {EntityName} {entityName}) {
    if ({entityName} == null) {
        {entityName} = new {EntityName}();
    }

    int pageNum = {entityName}.getPageNum() != null ? {entityName}.getPageNum() : 1;
    int pageSize = {entityName}.getPageSize() != null ? {entityName}.getPageSize() : 10;

    if (pageSize > 100) pageSize = 100;
    if (pageNum < 1) pageNum = 1;

    return {entityName}Service.getPageByMybatisPlus(pageNum, pageSize, {entityName});
}

// 列表查询接口
@Operation(summary = "查询{业务名称}列表", description = "根据条件查询{业务名称}信息列表")
@PostMapping("/list")
public List<{EntityName}> list(@Parameter(description = "查询条件", required = true) @RequestBody {EntityName} {entityName}) {
    if ({entityName} == null) {
        {entityName} = new {EntityName}();
    }
    return {entityName}Service.getList({entityName});
}

// 详情查询接口
@Operation(summary = "根据ID获取{业务名称}详情", description = "通过ID获取单个{业务名称}的详细信息")
@PostMapping("/getById")
public {EntityName} getById(@Parameter(description = "{业务名称}ID", required = true) @RequestBody {EntityName} {entityName}) {
    if ({entityName} == null || {entityName}.getId() == null) {
        throw new RuntimeException("ID不能为空");
    }
    {EntityName} result = {entityName}Service.getById({entityName}.getId());
    if (result == null) {
        throw new RuntimeException("记录不存在");
    }
    return result;
}

// 新增接口
@Operation(summary = "新增{业务名称}", description = "新增{业务名称}信息")
@PostMapping("/add")
@Log(title = "{业务名称}后台管理-新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
public {EntityName} add(@Parameter(description = "{业务名称}信息", required = true) @RequestBody {EntityName} {entityName}) {
    {entityName}Service.save({entityName});
    return {entityName};
}

// 更新接口
@Operation(summary = "更新{业务名称}", description = "更新{业务名称}信息")
@PostMapping("/update")
@Log(title = "{业务名称}后台管理-更新", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
public Boolean update(@Parameter(description = "{业务名称}信息", required = true) @RequestBody {EntityName} {entityName}) {
    if ({entityName}.getId() == null) {
        throw new RuntimeException("ID不能为空");
    }
    return {entityName}Service.updateById({entityName});
}

// 删除接口
@Operation(summary = "删除{业务名称}", description = "根据ID删除{业务名称}")
@PostMapping("/delete")
@Log(title = "{业务名称}后台管理-删除", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
public Boolean delete(@Parameter(description = "{业务名称}ID", required = true) @RequestBody {EntityName} {entityName}) {
    if ({entityName}.getId() == null) {
        throw new RuntimeException("ID不能为空");
    }
    return {entityName}Service.removeById({entityName}.getId());
}
```

### 1.4 通用控制器规范
- 所有接口必须使用 **POST** 请求
- 参数接收必须使用 **@RequestBody** 注解
- 参数接收必须使用**实体类**直接接收，且实体类必须继承 **BaseEntity**
- 增删改操作必须添加 **@Log** 注解，查询操作不需要日志
- 所有数据返回直接查询到什么就返回什么，不使用任何包装类
- 修改和删除接口返回 **Boolean** 类型，前端根据 true/false 判断操作成功与否
- 分页查询必须使用 **Page**，直接返回 MyBatis-Plus 的结果
- 后台管理使用分页查询，小程序直接使用 list 查询
- 控制器层不进行异常捕获，让异常直接抛出由全局异常处理器处理

## 2. 实体类（Entity）规范

### 2.1 基础结构
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("{table_name}")
@Schema(description = "{业务名称}实体类")
@Builder
public class {EntityName} extends BaseEntity {
    
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID", example = "1234567890")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    // 小程序实体类必须包含的字段
    @NotBlank(message = "用户openId不能为空")
    @Size(max = 100, message = "openId长度不能超过100个字符")
    @Schema(description = "用户的微信 openId", example = "wx1234567890", required = true)
    private String openId;
    
    @Schema(description = "用户ID", example = "1001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    
    @Size(max = 50, message = "用户姓名长度不能超过50个字符")
    @Schema(description = "用户姓名", example = "张三")
    private String userName;
    
    @Size(max = 20, message = "用户手机号长度不能超过20个字符")
    @Schema(description = "用户手机号", example = "13800138000")
    private String userPhone;
    
    // 其他业务字段...
}
```

### 2.2 实体类规范
- 必须继承 **BaseEntity** 实体类
- ID 为 **Long** 类型，必须有 **@TableId(type = IdType.ASSIGN_ID)** 注解
- 必须有 **@TableName("table_name")** 注解，使用下划线命名
- 小程序实体类必须包含：**openId**、**userId**、**userName**、**userPhone** 字段
- 需要校验的字段必须加上 **@Schema** 注解
- 字段采用驼峰命名
- 必须包含所有注解：**@Data**、**@NoArgsConstructor**、**@AllArgsConstructor**、**@TableName**、**@Schema**、**@Builder**

### 2.3 分页参数
分页参数不需要额外设置，直接使用实体类中 BaseEntity 的分页字段：
```java
@TableField(exist = false)
private Integer pageNum;

@TableField(exist = false)
private Integer pageSize;
```

## 3. Service 接口规范

### 3.1 基础结构
```java
public interface {EntityName}Service extends IService<{EntityName}> {
    
    // 小程序相关方法
    List<{EntityName}> getByOpenId(String openId);
    {EntityName} getByIdAndOpenId(Long id, String openId);
    {EntityName} updateByIdAndOpenId({EntityName} {entityName});
    boolean deleteByIdAndOpenId(Long id, String openId);
    
    // 后台管理相关方法
    Page<{EntityName}> getPageByMybatisPlus(int pageNum, int pageSize, {EntityName} queryParams);
    List<{EntityName}> getList({EntityName} queryParams);
}
```

### 3.2 Service 规范
- 必须继承 **IService<{EntityName}>**
- 必须包含基础的增删改查接口
- 小程序接口需要带上 **openId** 进行新增或查询
- 后台管理接口需要支持分页查询和列表查询

## 4. ServiceImpl 实现类规范

### 4.1 基础结构
```java
@Slf4j
@Service
public class {EntityName}ServiceImpl extends ServiceImpl<{EntityName}Mapper, {EntityName}> implements {EntityName}Service {
    
    @Override
    public List<{EntityName}> getByOpenId(String openId) {
        if (openId == null || openId.trim().isEmpty()) {
            log.warn("getByOpenId方法接收到空的openId参数");
            return new ArrayList<>();
        }

        LambdaQueryWrapper<{EntityName}> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq({EntityName}::getOpenId, openId);
        queryWrapper.orderByDesc({EntityName}::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public {EntityName} getByIdAndOpenId(Long id, String openId) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        if (openId == null || openId.trim().isEmpty()) {
            throw new RuntimeException("openId不能为空");
        }

        LambdaQueryWrapper<{EntityName}> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq({EntityName}::getId, id);
        queryWrapper.eq({EntityName}::getOpenId, openId);
        return this.getOne(queryWrapper);
    }
    
    @Override
    public {EntityName} updateByIdAndOpenId({EntityName} {entityName}) {
        if ({entityName}.getId() == null) {
            throw new RuntimeException("id不能为空");
        }
        if ({entityName}.getOpenId() == null || {entityName}.getOpenId().trim().isEmpty()) {
            throw new RuntimeException("openId不能为空");
        }
        
        // 先查询确认记录存在且属于当前用户
        LambdaQueryWrapper<{EntityName}> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq({EntityName}::getId, {entityName}.getId());
        queryWrapper.eq({EntityName}::getOpenId, {entityName}.getOpenId());
        {EntityName} existing = this.getOne(queryWrapper);

        if (existing == null) {
            throw new RuntimeException("记录不存在或无权限操作");
        }
        
        boolean result = this.updateById({entityName});
        if (!result) {
            throw new RuntimeException("更新失败");
        }
        return {entityName};
    }
    
    @Override
    public boolean deleteByIdAndOpenId(Long id, String openId) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        if (openId == null || openId.trim().isEmpty()) {
            throw new RuntimeException("openId不能为空");
        }
        
        LambdaQueryWrapper<{EntityName}> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq({EntityName}::getId, id);
        queryWrapper.eq({EntityName}::getOpenId, openId);
        {EntityName} existing = this.getOne(queryWrapper);
        
        if (existing == null) {
            log.error("不能删除别人数据或记录不存在");
            return false;
        }
        
        return this.removeById(id);
    }
    
    @Override
    public Page<{EntityName}> getPageByMybatisPlus(int pageNum, int pageSize, {EntityName} queryParams) {
        Page<{EntityName}> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<{EntityName}> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数添加条件
        if (queryParams != null) {
            // 添加具体的查询条件...
        }

        wrapper.orderByDesc({EntityName}::getCreateTime);
        return this.page(page, wrapper);
    }

    @Override
    public List<{EntityName}> getList({EntityName} queryParams) {
        LambdaQueryWrapper<{EntityName}> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数添加条件
        if (queryParams != null) {
            // 添加具体的查询条件...
        }

        wrapper.orderByDesc({EntityName}::getCreateTime);
        return this.list(wrapper);
    }
}
```

### 4.2 ServiceImpl 规范
- 必须继承 **ServiceImpl<{EntityName}Mapper, {EntityName}>** 并实现 **{EntityName}Service**
- 查询采用 **LambdaQueryWrapper**
- 更新需要加上必要的校验，如 id 为空时抛出异常：`throw new RuntimeException("id不能为空");`
- 小程序的更新、删除必须先根据 openId 进行查询验证权限
- 分页查询使用 MyBatis-Plus 的 **Page** 对象
- 查询条件中不需要手动添加 **deleteStatus** 条件，框架会自动处理逻辑删除

## 5. Mapper 接口规范

### 5.1 基础结构
```java
@Mapper
public interface {EntityName}Mapper extends BaseMapper<{EntityName}> {
    // 如有额外的 SQL 方法，可以在此添加
}
```

### 5.2 Mapper 规范
- 必须继承 **BaseMapper<{EntityName}>**
- 必须添加 **@Mapper** 注解
- 基础 CRUD 操作由 MyBatis-Plus 自动提供
- 如需自定义 SQL，可在此接口中添加方法

## 6. 通用规范

### 6.1 异常处理
- 参数校验失败时抛出 **RuntimeException**
- 业务逻辑错误时抛出 **RuntimeException**
- 权限验证失败时抛出 **RuntimeException**
- 控制器层不进行 try-catch 包装，让异常直接抛出，由全局异常处理器统一处理
- Service 层进行必要的业务校验，校验失败时抛出 RuntimeException

### 6.2 日志规范
- 所有 Service 实现类必须添加 **@Slf4j** 注解
- 关键操作必须记录日志
- 异常情况必须记录错误日志

### 6.3 返回值规范
- 小程序接口直接返回实体对象或集合
- 后台管理接口直接返回相应的数据类型，不使用包装类
- 查询接口返回实体对象、集合或 **Page** 对象
- 新增接口返回保存后的实体对象
- 修改和删除接口返回 **Boolean** 类型，前端根据 true/false 判断操作成功与否
- 所有数据返回直接查询到什么就返回什么

### 6.4 命名规范
- 类名使用大驼峰命名法
- 方法名和变量名使用小驼峰命名法
- 数据库表名和字段名使用下划线命名法
- 包名使用小写字母

### 6.5 注解规范
- 控制器必须添加：**@RestController**、**@RequestMapping**、**@Tag**
- 方法必须添加：**@Operation**、**@PostMapping**、**@Log**（仅增删改操作，查询操作不需要）
- 实体类必须添加：**@Data**、**@TableName**、**@Schema** 等
- Service 实现类必须添加：**@Service**、**@Slf4j**
- Mapper 接口必须添加：**@Mapper**

这份文档涵盖了基于现有代码分析得出的所有开发规范，可以作为后续自动生成代码的标准模板。
