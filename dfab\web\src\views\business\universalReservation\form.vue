<template>
  <el-dialog :title="title" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="800px" append-to-body @close="handleClose">
    <el-form ref="universalReservationRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预约类型" prop="reservationType">
            <el-select v-model="form.reservationType" placeholder="请选择预约类型" style="width: 100%">
              <el-option
                v-for="(name, code) in reservationTypes"
                :key="code"
                :label="name"
                :value="parseInt(code)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预约标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入预约标题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择会员" prop="selectedMember">
            <el-select
              v-model="form.selectedMember"
              placeholder="请选择入住会员"
              style="width: 100%"
              @change="handleMemberChange"
              filterable
            >
              <el-option
                v-for="member in memberList"
                :key="member.id"
                :label="`${member.memberName} - ${member.phoneNumber} - ${member.roomNumber}`"
                :value="member.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="userName">
            <el-input v-model="form.userName" placeholder="自动填充" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户电话" prop="userPhone">
            <el-input v-model="form.userPhone" placeholder="自动填充" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="form.contactPhone" placeholder="自动填充" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="房间号" prop="roomNumber">
            <el-input v-model="form.roomNumber" placeholder="自动填充" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预约时间" prop="reservationTime">
            <el-date-picker
              v-model="form.reservationTime"
              type="datetime"
              placeholder="选择预约时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              placeholder="选择结束时间（可选）"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预约地点" prop="location">
            <el-input v-model="form.location" placeholder="请输入预约地点" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预约人数" prop="peopleCount">
            <el-input-number v-model="form.peopleCount" :min="1" :max="10" placeholder="请输入预约人数" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预约状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择预约状态" style="width: 100%">
              <el-option label="待确认" :value="0" />
              <el-option label="已确认" :value="1" />
              <el-option label="已完成" :value="2" />
              <el-option label="已取消" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费用金额" prop="amount">
            <el-input-number v-model="form.amount" :precision="2" :min="0" placeholder="请输入费用金额" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处理人员" prop="handlerName">
            <el-input v-model="form.handlerName" placeholder="请输入处理人员姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理人员电话" prop="handlerPhone">
            <el-input v-model="form.handlerPhone" placeholder="请输入处理人员电话" />
          </el-form-item>
        </el-col>
      </el-row>



      <el-form-item label="预约描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入预约描述" />
      </el-form-item>

      <!-- 根据预约类型显示不同的扩展属性表单 -->
      <div v-if="form.reservationType !== null && form.reservationType !== undefined">
        <!-- 试餐预约扩展属性 -->
        <template v-if="form.reservationType === 0">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用餐时段">
                <el-select v-model="extendedProps.mealTime" placeholder="请选择用餐时段" style="width: 100%">
                  <el-option label="早餐" value="早餐" />
                  <el-option label="中餐" value="中餐" />
                  <el-option label="晚餐" value="晚餐" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参与人数">
                <el-input-number v-model="extendedProps.participantCount" :min="1" :max="10" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="饮食偏好">
            <el-input v-model="extendedProps.dietaryPreference" type="textarea" :rows="2" placeholder="请输入饮食偏好或忌口信息" />
          </el-form-item>
        </template>

        <!-- 看房预约扩展属性 -->
        <template v-if="form.reservationType === 1">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="感兴趣房型">
                <el-select v-model="extendedProps.roomType" placeholder="请选择房型" style="width: 100%">
                  <el-option label="标准间" value="标准间" />
                  <el-option label="豪华间" value="豪华间" />
                  <el-option label="套房" value="套房" />
                  <el-option label="VIP套房" value="VIP套房" />
                  <el-option label="总统套房" value="总统套房" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参观人数">
                <el-input-number v-model="extendedProps.participantCount" :min="1" :max="10" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预产期">
                <el-date-picker v-model="extendedProps.dueDate" type="date" placeholder="请选择预产期" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="特殊需求">
            <el-input v-model="extendedProps.specialRequirements" type="textarea" :rows="2" placeholder="请输入特殊需求" />
          </el-form-item>
        </template>

        <!-- 用车预约扩展属性 -->
        <template v-if="form.reservationType === 2">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="车型">
                <el-select v-model="extendedProps.carType" placeholder="请选择车型" style="width: 100%">
                  <el-option label="轿车" value="轿车" />
                  <el-option label="商务车" value="商务车" />
                  <el-option label="SUV" value="SUV" />
                  <el-option label="面包车" value="面包车" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="乘车人数">
                <el-input-number v-model="extendedProps.passengerCount" :min="1" :max="10" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否需要等候">
                <el-switch v-model="extendedProps.needWaiting" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="出发地点">
                <el-input v-model="extendedProps.startLocation" placeholder="请输入出发地点" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目的地">
                <el-input v-model="extendedProps.destination" placeholder="请输入目的地" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="等候时间" v-if="extendedProps.needWaiting">
            <el-input v-model="extendedProps.waitingTime" placeholder="请输入预计等候时间" />
          </el-form-item>
          <el-form-item label="特殊需求">
            <el-input v-model="extendedProps.specialRequirements" type="textarea" :rows="2" placeholder="请输入特殊需求" />
          </el-form-item>
        </template>

        <el-form-item label="备注">
          <el-input v-model="extendedProps.remarks" type="textarea" :rows="2" placeholder="请输入备注信息" />
        </el-form-item>
      </div>

      <el-form-item label="取消原因" prop="cancelReason" v-if="form.status === 3">
        <el-input v-model="form.cancelReason" type="textarea" :rows="2" placeholder="请输入取消原因" />
      </el-form-item>

      <el-row :gutter="20" v-if="form.status === 2">
        <el-col :span="12">
          <el-form-item label="实际开始时间" prop="actualStartTime">
            <el-date-picker
              v-model="form.actualStartTime"
              type="datetime"
              placeholder="选择实际开始时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际结束时间" prop="actualEndTime">
            <el-date-picker
              v-model="form.actualEndTime"
              type="datetime"
              placeholder="选择实际结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.status === 2">
        <el-col :span="12">
          <el-form-item label="评价分数" prop="rating">
            <el-rate v-model="form.rating" :max="5" show-text />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="评价内容" prop="review" v-if="form.status === 2">
        <el-input v-model="form.review" type="textarea" :rows="2" placeholder="请输入评价内容" />
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="UniversalReservationForm">
import { ref, reactive, toRefs, watch, onMounted, getCurrentInstance } from 'vue';
import {
  getUniversalReservation,
  addUniversalReservation,
  updateUniversalReservation,
  getReservationTypes
} from "@/api/business/universalReservation";
import { getAllMemberCheckins } from "@/api/business/memberCheckin";

const { proxy } = getCurrentInstance();

// 定义组件的 props 和 emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  reservationId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:visible', 'success']);

const universalReservationRef = ref();
const reservationTypes = ref({});
const memberList = ref([]);
const title = ref("");

const data = reactive({
  form: {
    status: 0,
    peopleCount: 1,
    selectedMember: undefined
  },
  rules: {
    reservationType: [
      { required: true, message: "预约类型不能为空", trigger: "change" }
    ],
    title: [
      { required: true, message: "预约标题不能为空", trigger: "blur" }
    ],
    selectedMember: [
      { required: true, message: "请选择会员", trigger: "change" }
    ],
    userName: [
      { required: true, message: "用户姓名不能为空", trigger: "blur" }
    ],
    reservationTime: [
      { required: true, message: "预约时间不能为空", trigger: "change" }
    ]
  }
});

const { form, rules } = toRefs(data);

// 扩展属性对象
const extendedProps = ref({
  // 试餐预约
  mealTime: '',
  participantCount: 1,
  dietaryPreference: '',
  // 看房预约
  roomType: '',
  dueDate: null,
  specialRequirements: '',
  // 用车预约
  carType: '',
  passengerCount: 1,
  startLocation: '',
  destination: '',
  needWaiting: false,
  waitingTime: '',
  // 通用
  remarks: ''
});

/** 获取预约类型 */
function getReservationTypesList() {
  getReservationTypes().then(response => {
    reservationTypes.value = response.types || {};
  });
}

/** 获取入住会员列表 */
function getMemberList() {
  getAllMemberCheckins().then(response => {
    if (response.code === 200) {
      memberList.value = response.data || [];
    } else {
      proxy.$modal.msgError(response.msg || "获取会员列表失败");
    }
  }).catch(error => {
    console.error('获取会员列表失败:', error);
    proxy.$modal.msgError("获取会员列表失败");
  });
}

/** 会员选择变化处理 */
function handleMemberChange(memberId) {
  if (memberId) {
    const selectedMember = memberList.value.find(member => member.id === memberId);
    if (selectedMember) {
      form.value.userName = selectedMember.memberName;
      form.value.userPhone = selectedMember.phoneNumber;
      form.value.contactPhone = selectedMember.phoneNumber;
      form.value.roomNumber = selectedMember.roomNumber;
      form.value.openId = selectedMember.openId;
      form.value.memberCheckinId = selectedMember.id;
    }
  } else {
    // 清空相关字段
    form.value.userName = '';
    form.value.userPhone = '';
    form.value.contactPhone = '';
    form.value.roomNumber = '';
    form.value.openId = '';
    form.value.memberCheckinId = '';
  }
}

/** 重置表单 */
function reset() {
  form.value = {
    status: 0,
    peopleCount: 1,
    selectedMember: undefined
  };
  // 重置扩展属性
  extendedProps.value = {
    mealTime: '',
    participantCount: 1,
    dietaryPreference: '',
    roomType: '',
    dueDate: null,
    specialRequirements: '',
    carType: '',
    passengerCount: 1,
    startLocation: '',
    destination: '',
    needWaiting: false,
    waitingTime: '',
    remarks: ''
  };
  universalReservationRef.value?.resetFields();
}

/** 获取预约详情 */
function getReservationDetail() {
  if (props.reservationId) {
    getUniversalReservation({ id: props.reservationId }).then(response => {
      form.value = response;
      // 如果有会员入住记录ID，设置选中的会员
      if (response.memberCheckinId) {
        form.value.selectedMember = response.memberCheckinId;
      }
      // 解析扩展属性
      if (response.extendedProperties) {
        try {
          const props = JSON.parse(response.extendedProperties);
          Object.assign(extendedProps.value, props);
        } catch (error) {
          console.error('解析扩展属性失败:', error);
        }
      }
    });
  }
}

/** 提交表单 */
function submitForm() {
  universalReservationRef.value.validate(valid => {
    if (valid) {
      // 创建提交数据的副本
      const submitData = { ...form.value };

      // 确保memberCheckinId被正确设置
      if (submitData.selectedMember) {
        submitData.memberCheckinId = submitData.selectedMember;
      }

      // 移除selectedMember字段
      delete submitData.selectedMember;

      // 将扩展属性对象转换为JSON字符串
      const filteredProps = {};
      Object.keys(extendedProps.value).forEach(key => {
        const value = extendedProps.value[key];
        if (value !== null && value !== undefined && value !== '') {
          filteredProps[key] = value;
        }
      });
      submitData.extendedProperties = Object.keys(filteredProps).length > 0 ? JSON.stringify(filteredProps) : '';

      if (form.value.id != null) {
        updateUniversalReservation(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          handleClose();
          emit('success');
        });
      } else {
        addUniversalReservation(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          handleClose();
          emit('success');
        });
      }
    }
  });
}

/** 关闭弹窗 */
function handleClose() {
  reset();
  emit('update:visible', false);
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    reset();
    getReservationTypesList();
    getMemberList();
    if (props.reservationId) {
      title.value = "修改通用预约";
      getReservationDetail();
    } else {
      title.value = "新增通用预约";
    }
  }
});

onMounted(() => {
  getReservationTypesList();
});
</script>
