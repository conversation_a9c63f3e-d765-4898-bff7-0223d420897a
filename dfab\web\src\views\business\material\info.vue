<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>物料信息管理</span>

        </div>
      </template>
      
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable style="width: 200px" @keyup.enter="handleQuery" @input="handleQuery"/>
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="queryParams.specification" placeholder="请输入规格型号" clearable style="width: 200px" @keyup.enter="handleQuery"  @input="handleQuery"/>
        </el-form-item>
        <el-form-item label="是否套餐物料" prop="isPackageMaterial">
          <el-select v-model="queryParams.isPackageMaterial" placeholder="请选择" clearable style="width: 150px"  @change="handleQuery">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAdd">新增物料</el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="multipleSelection.length === 0">批量删除</el-button>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" width="50" />
        <el-table-column label="物料名称" prop="materialName" />
        <el-table-column label="规格型号" prop="specification" />
        <el-table-column label="单位" prop="unit" />
        <el-table-column label="提醒数量" prop="warnQuantity" />
        <el-table-column label="单价" prop="unitPrice" />
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <!-- 添加是否套餐物料列 -->
        <el-table-column label="是否套餐物料" prop="isPackageMaterial">
          <template #default="scope">
            {{ scope.row.isPackageMaterial === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <!-- 添加套餐包含数量列 -->
        <el-table-column label="套餐包含数量" prop="packageQuantity" />
        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleUpdate(scope.row)">修改</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>

      </el-table>

      <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
        <el-pagination
          v-show="total > 0"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加或修改物料信息对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <!-- 修改表单标签宽度 -->
      <el-form ref="materialForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="提醒数量" prop="warnQuantity">
          <el-input-number v-model="form.warnQuantity" :min="0" placeholder="请输入提醒数量（可选）" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number v-model="form.unitPrice" :precision="2" :step="0.1" :min="0" />
        </el-form-item>
        <el-form-item label="是否套餐物料" prop="isPackageMaterial">
          <el-select v-model="form.isPackageMaterial" placeholder="请选择" clearable>
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐包含数量" prop="packageQuantity" v-if="form.isPackageMaterial === 1">
          <el-input-number v-model="form.packageQuantity" :min="0" placeholder="请输入套餐包含数量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listMaterial, pageMaterial, getMaterial, addMaterial, updateMaterial, deleteMaterial } from '@/api/business/material'

const loading = ref(false)
const total = ref(0)
const open = ref(false)
const title = ref('')
const materialList = ref([])
const multipleSelection = ref([])

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  materialName: '',
  specification: '',
  isPackageMaterial: undefined
})

const form = reactive({
  id: undefined,
  materialName: '',
  specification: '',
  unit: '',
  warnQuantity: undefined,
  unitPrice: 0,
  isPackageMaterial: undefined,
  packageQuantity: undefined
})

const rules = reactive({
  materialName: [{ required: true, message: '物料名称不能为空', trigger: 'blur' }],
  specification: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
  unit: [{ required: true, message: '单位不能为空', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '单价不能为空', trigger: 'blur' }],
  packageQuantity: [
    {
      validator: (rule, value, callback) => {
        if (form.isPackageMaterial === 1) {
          if (value === null || value === undefined || value === '') {
            callback(new Error('套餐包含数量不能为空'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const materialForm = ref()
const queryForm = ref()

// 防抖定时器
let paginationTimer = null

/** 处理分页事件 */
function handlePagination() {
  if (paginationTimer) {
    clearTimeout(paginationTimer)
  }

  paginationTimer = setTimeout(() => {
    getList()
  }, 200)
}

/** 处理页面大小变化 */
function handleSizeChange(val) {
  queryParams.pageSize = val
  queryParams.pageNum = 1
  getList()
}

/** 处理当前页变化 */
function handleCurrentChange(val) {
  queryParams.pageNum = val
  getList()
}

/** 查询物料列表 */
function getList() {
  loading.value = true
  pageMaterial(queryParams).then(response => {
    // 处理不同的响应格式
    if (response) {
      if (response.records) {
        // 直接返回Page对象的情况
        materialList.value = response.records || []
        total.value = parseInt(response.total) || 0  // 转换为数字
      } else if (response.data && response.data.records) {
        // 嵌套在data属性中的情况
        materialList.value = response.data.records || []
        total.value = parseInt(response.data.total) || 0  // 转换为数字
      } else {
        // 其他情况，尝试适配
        materialList.value = Array.isArray(response) ? response : []
        total.value = Array.isArray(response) ? response.length : 0
      }
    } else {
      materialList.value = []
      total.value = 0
    }

    loading.value = false
  }).catch(error => {
    console.error('获取物料列表失败:', error)
    materialList.value = []
    total.value = 0
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryForm.value.resetFields()
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加物料信息'
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id
  getMaterial({ id: id }).then(response => {
    console.log('获取物料详情响应:', response);
    if (response && response.data) {
      Object.assign(form, response.data)
    } else if (response) {
      Object.assign(form, response)
    }
    open.value = true
    title.value = '修改物料信息'
  }).catch(error => {
    console.error('获取物料详情失败:', error)
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  ElMessageBox.confirm(
    `确定要删除物料"${row.materialName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteMaterial({ id: row.id }).then(response => {
      console.log('删除物料响应:', response);
      if (response && (response.code === 200 || response.data === true || response === true)) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('删除物料失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 批量删除操作 */
function handleBatchDelete() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的物料')
    return
  }

  const materialNames = multipleSelection.value.map(item => item.materialName).join('、')
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 个物料（${materialNames}）吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const ids = multipleSelection.value.map(item => item.id)
    deleteMaterial({ id: ids }).then(response => {
      console.log('批量删除物料响应:', response);
      if (response && (response.code === 200 || response.data === true || response === true)) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response?.msg || '删除失败')
      }
    }).catch(error => {
      console.error('批量删除物料失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    })
  }).catch(() => {
    // 用户取消删除
  })
}

/** 表格多选框选中数据 */
function handleSelectionChange(selection) {
  multipleSelection.value = selection
}

/** 重置表单 */
function reset() {
  form.id = undefined
  form.materialName = ''
  form.specification = ''
  form.unit = ''
  form.warnQuantity = undefined
  form.unitPrice = 0
  form.isPackageMaterial = undefined
  form.packageQuantity = undefined
  materialForm.value?.resetFields()
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 提交按钮 */
function submitForm() {
  materialForm.value?.validate(valid => {
    if (valid) {
      if (form.id) {
        updateMaterial(form).then(response => {
          console.log('更新物料响应:', response);
          if (response && (response.data === true || response === true)) {
            ElMessage.success('修改成功')
            open.value = false
            getList()
          } else {
            ElMessage.error('修改失败')
          }
        }).catch(error => {
          console.error('更新物料失败:', error)
          ElMessage.error('修改失败: ' + error.message)
        })
      } else {
        addMaterial(form).then(response => {
          console.log('添加物料响应:', response);
          if (response && (response.data === true || response === true)) {
            ElMessage.success('新增成功')
            open.value = false
            getList()
          } else {
            ElMessage.error('新增失败')
          }
        }).catch(error => {
          console.error('添加物料失败:', error)
          ElMessage.error('新增失败: ' + error.message)
        })
      }
    }
  })
}

// 监听是否套餐物料的变化
watch(() => form.isPackageMaterial, (newValue) => {
  // 如果不是选择"是"，则清空套餐包含数量
  if (newValue !== 1) {
    form.packageQuantity = undefined
  }
})

onMounted(() => {
  getList()
})
</script>
"}}}