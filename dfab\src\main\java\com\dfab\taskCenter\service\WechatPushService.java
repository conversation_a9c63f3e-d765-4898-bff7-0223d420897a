package com.dfab.taskCenter.service;

import com.dfab.taskCenter.entity.PushRecord;
import com.dfab.taskCenter.entity.TaskCenter;

/**
 * 微信推送Service接口
 */
public interface WechatPushService {

    /**
     * 推送任务通知
     * @param task 任务信息
     * @param pushType 推送类型：1-任务创建通知，2-任务完成通知，3-任务提醒
     * @return 推送记录
     */
    PushRecord pushTaskNotification(TaskCenter task, Integer pushType);

    /**
     * 推送任务完成通知给用户
     * @param task 任务信息
     * @return 推送记录
     */
    PushRecord pushTaskCompletionNotification(TaskCenter task);

    /**
     * 推送任务提醒
     * @param task 任务信息
     * @return 推送记录
     */
    PushRecord pushTaskReminder(TaskCenter task);

    /**
     * 批量推送未推送的任务
     * @return 推送成功数量
     */
    int batchPushUnsentTasks();

    /**
     * 重试失败的推送
     * @return 重试成功数量
     */
    int retryFailedPushes();

    /**
     * 发送自定义消息
     * @param openId 接收者openId
     * @param title 消息标题
     * @param content 消息内容
     * @param templateId 模板ID
     * @param pagePath 跳转页面路径
     * @param data 推送数据
     * @return 推送记录
     */
    PushRecord sendCustomMessage(String openId, String title, String content, 
                                String templateId, String pagePath, String data);

    /**
     * 检查推送状态
     * @param pushRecordId 推送记录ID
     * @return 推送状态
     */
    Integer checkPushStatus(Long pushRecordId);
}
