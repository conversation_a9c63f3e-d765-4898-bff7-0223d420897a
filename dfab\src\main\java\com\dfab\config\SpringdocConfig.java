package com.dfab.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SpringdocConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .components(new Components())
                .info(new Info()
                        .title("API 文档")
                        .description("东方爱堡 项目 API 文档")
                        .version("1.0")
                       );
    }

    // 指定要扫描的 Controller 包路径，并生成对应的 API 分组文档
    @Bean
    public GroupedOpenApi dfabPublicApi() {
        return GroupedOpenApi.builder()
                .group("dfab-public") // 分组名称
                .pathsToMatch("/api/**") // 要扫描的包路径
                .build();
    }

    // 指定要扫描的 Controller 包路径，并生成对应的 API 分组文档
    @Bean
    public GroupedOpenApi ryPublicApi() {
        return GroupedOpenApi.builder()
                .group("ruoyi-public") // 分组名称
                .pathsToExclude("/api/**")
                .build();
    }
}