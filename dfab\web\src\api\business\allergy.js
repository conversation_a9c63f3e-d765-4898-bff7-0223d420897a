import request from '@/utils/request'

// 查询用户忌口列表
export function listAllergy(query) {
  return request({
    url: '/admin/allergy/page',
    method: 'post',
    data: query
  })
}

// 查询用户忌口列表
export function boardListAllergy(query) {
  return request({
    url: '/admin/allergy/boardList',
    method: 'post',
    data: query
  })
}

// 查询用户忌口详细
export function getAllergy(data) {
  return request({
    url: '/admin/allergy/get',
    method: 'post',
    data: data
  })
}

// 添加用户忌口
export function addAllergy(data) {
  return request({
    url: '/admin/allergy/save',
    method: 'post',
    data: data
  })
}

// 修改用户忌口
export function updateAllergy(data) {
  return request({
    url: '/admin/allergy/update',
    method: 'post',
    data: data
  })
}

// 删除用户忌口
export function delAllergy(data) {
  return request({
    url: '/admin/allergy/delete',
    method: 'post',
    data: data
  })
}

// 添加投诉记录
export function addComplaint(data) {
  return request({
    url: '/admin/allergy/addComplaint',
    method: 'post',
    data: data
  })
}

// 获取投诉记录列表
export function getComplaintList(data) {
  return request({
    url: '/admin/allergy/getComplaintList',
    method: 'post',
    data: data
  })
}
