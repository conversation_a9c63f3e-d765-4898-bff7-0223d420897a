package com.dfab.appUser.controller;

/*import com.dfab.appUser.dto.LoginRequest;
import com.dfab.appUser.dto.LoginResponse;*/

import cn.hutool.core.util.StrUtil;
import com.dfab.appUser.dto.WechatLoginDTO;
import com.dfab.appUser.dto.WechatLoginResultDTO;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.util.JwtUtil;
import com.dfab.util.WechatUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping("/api/appUser")
@Tag(name = "AppUserController", description = "App 用户管理接口")
public class AppUserController {


    @Autowired
    private AppUserService userService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private WechatUtil wechatUtil;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/wechat/login")
    @Log(title = "小程序用户-登录", businessType = BusinessType.LOGIN,operatorType = OperatorType.MOBILE)
    public WechatLoginResultDTO wechatLogin(@RequestBody WechatLoginDTO loginDTO) {
        // 获取sessionKey和openId
        Map<String, String> sessionInfo = wechatUtil.getSessionKeyAndOpenId(loginDTO.getCode());

        // 获取手机号
        String phoneNumber = wechatUtil.getPhoneNumber(

                loginDTO.getPhoneCode()
        );

        if(StrUtil.isBlank(sessionInfo.get("openid")) || StrUtil.isBlank(phoneNumber)) {
            throw new RuntimeException("手机号或openid获取失败");
        }

        // 保存或更新用户信息
        AppUser appUser = AppUser.builder()
                .openId(sessionInfo.get("openid"))
                .phoneNumber(phoneNumber)
                .build();
        userService.createOrUpdateAppUser(appUser);

        // 获取完整用户信息
        appUser = userService.getByOpenId(sessionInfo.get("openid"));

        // 生成JWT token
        String token = jwtUtil.generateToken(appUser);

        // 创建并返回结果对象
        WechatLoginResultDTO result = new WechatLoginResultDTO();
        result.setOpenid(sessionInfo.get("openid"));
        result.setPhoneNumber(phoneNumber);
        result.setToken(token);
        result.setAppUser(appUser);
        return result;
    }




    /**
     * 根据 openId 查询用户
     * @return 用户信息
     */
    @Operation(summary = "根据 openID 查询用户", description = "通过用户 openID 获取单个 App 用户的信息")
    @PostMapping("/get")
    @Log(title = "小程序用户-根据token获取用户信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    public AppUser getByOpenId() {
        String openId = (String)request.getAttribute("openId");
        return userService.getByOpenId(openId);
    }

    /**
     * 添加用户
     * @param user 用户信息
     * @return 新增的用户信息
     */
    @Operation(summary = "添加用户或修改用户", description = "添加用户或修改用户")
    @PostMapping("addOrUpdate")
    @Log(title = "小程序用户-更新用户信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MOBILE)
    public AppUser createUser(@Parameter(description = "用户信息", required = true) @RequestBody AppUser user) {
        String openId = (String)request.getAttribute("openId");
        user.setOpenId(openId);
        return userService.createOrUpdateAppUser(user);
    }


}