<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>预约详情</span>
          <div>
            <el-button type="primary" @click="handleEdit" v-hasPermi="['business:universalReservation:edit']">编辑</el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="预约ID">{{ detail.id }}</el-descriptions-item>
        <el-descriptions-item label="预约类型">
          <el-tag :type="getReservationTypeTag(detail.reservationType).type">
            {{ getReservationTypeTag(detail.reservationType).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预约标题">{{ detail.title }}</el-descriptions-item>
        <el-descriptions-item label="预约状态">
          <el-tag :type="getStatusTag(detail.status).type">
            {{ getStatusTag(detail.status).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户姓名">{{ detail.userName }}</el-descriptions-item>
        <el-descriptions-item label="用户电话">{{ detail.userPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detail.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="房间号">{{ detail.roomNumber }}</el-descriptions-item>
        <el-descriptions-item label="预约时间">
          {{ parseTime(detail.reservationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间" v-if="detail.endTime">
          {{ parseTime(detail.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="预约地点">{{ detail.location }}</el-descriptions-item>
        <el-descriptions-item label="预约人数">{{ detail.peopleCount }}</el-descriptions-item>
        <el-descriptions-item label="处理人员" v-if="detail.handlerName">
          <div>{{ detail.handlerName }}</div>
          <div style="font-size: 12px; color: #999;">{{ detail.handlerPhone }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="费用金额" v-if="detail.amount">
          ¥{{ detail.amount }}
        </el-descriptions-item>
        <el-descriptions-item label="会员入住记录ID" v-if="detail.memberCheckinId">
          {{ detail.memberCheckinId }}
        </el-descriptions-item>
        <el-descriptions-item label="用户OpenID">{{ detail.openId }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(detail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="detail.updateTime">
          {{ parseTime(detail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="1" border style="margin-top: 20px;" v-if="detail.description">
        <el-descriptions-item label="预约描述">{{ detail.description }}</el-descriptions-item>
      </el-descriptions>

<!--      <el-descriptions :column="1" border style="margin-top: 20px;" v-if="detail.extendedProperties">
        <el-descriptions-item label="扩展属性">
          <pre style="white-space: pre-wrap; font-family: inherit;">{{ formatJson(detail.extendedProperties) }}</pre>
        </el-descriptions-item>
      </el-descriptions>-->

      <el-descriptions :column="1" border style="margin-top: 20px;" v-if="detail.cancelReason">
        <el-descriptions-item label="取消原因">{{ detail.cancelReason }}</el-descriptions-item>
      </el-descriptions>

      <!-- 实际执行时间 -->
      <el-descriptions :column="2" border style="margin-top: 20px;" v-if="detail.actualStartTime || detail.actualEndTime">
        <el-descriptions-item label="实际开始时间" v-if="detail.actualStartTime">
          {{ parseTime(detail.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="实际结束时间" v-if="detail.actualEndTime">
          {{ parseTime(detail.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 评价信息 -->
      <el-descriptions :column="2" border style="margin-top: 20px;" v-if="detail.rating || detail.review">
        <el-descriptions-item label="评价分数" v-if="detail.rating">
          <el-rate v-model="detail.rating" disabled show-text />
        </el-descriptions-item>
        <el-descriptions-item label="评价内容" v-if="detail.review">
          {{ detail.review }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 快捷操作 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="detail.status !== undefined">
      <template #header>
        <span>快捷操作</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-if="detail.status === 0">
          <el-button type="success" @click="handleConfirm" style="width: 100%">确认预约</el-button>
        </el-col>
        <el-col :span="6" v-if="detail.status === 1">
          <el-button type="primary" @click="handleComplete" style="width: 100%">完成预约</el-button>
        </el-col>
        <el-col :span="6" v-if="detail.status < 2">
          <el-button type="danger" @click="handleCancel" style="width: 100%">取消预约</el-button>
        </el-col>
        <el-col :span="6" v-if="detail.status < 2">
          <el-button type="warning" @click="handleAssignHandler" style="width: 100%">分配处理人员</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 分配处理人员对话框 -->
    <el-dialog title="分配处理人员" v-model="assignDialogVisible" width="400px">
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="处理人员" required>
          <el-input v-model="assignForm.handlerName" placeholder="请输入处理人员姓名" />
        </el-form-item>
        <el-form-item label="联系电话" required>
          <el-input v-model="assignForm.handlerPhone" placeholder="请输入处理人员电话" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAssign">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog title="取消预约" v-model="cancelDialogVisible" width="400px">
      <el-form :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" required>
          <el-input v-model="cancelForm.cancelReason" type="textarea" :rows="3" placeholder="请输入取消原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UniversalReservationDetail">
import { 
  getUniversalReservation,
  updateReservationStatus,
  assignHandler,
  completeReservation,
  cancelReservation
} from "@/api/business/universalReservation";

const { proxy } = getCurrentInstance();
const { parseTime } = proxy.useDict();
const route = useRoute();
const router = useRouter();

const detail = ref({});
const assignDialogVisible = ref(false);
const cancelDialogVisible = ref(false);
const assignForm = ref({
  handlerName: '',
  handlerPhone: ''
});
const cancelForm = ref({
  cancelReason: ''
});

/** 获取预约详情 */
function getDetail() {
  const id = route.params.id;
  getUniversalReservation({ id: id }).then(response => {
    detail.value = response;
  });
}

/** 预约类型标签 */
function getReservationTypeTag(type) {
  const typeMap = {
    1: { text: '餐食预约', type: 'success' },
    2: { text: '用车预约', type: 'warning' },
    3: { text: '试餐预约', type: 'primary' },
    4: { text: '看房预约', type: 'info' },
    5: { text: '其他预约', type: '' }
  };
  return typeMap[type] || { text: '未知', type: 'danger' };
}

/** 状态标签 */
function getStatusTag(status) {
  const statusMap = {
    0: { text: '待确认', type: 'warning' },
    1: { text: '已确认', type: 'primary' },
    2: { text: '已完成', type: 'success' },
    3: { text: '已取消', type: 'danger' }
  };
  return statusMap[status] || { text: '未知', type: 'info' };
}

/** 格式化JSON */
function formatJson(jsonStr) {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2);
  } catch (e) {
    return jsonStr;
  }
}

/** 编辑按钮 */
function handleEdit() {
  router.push('/business/universalReservation/edit/' + detail.value.id);
}

/** 返回按钮 */
function goBack() {
  router.push('/business/universalReservation');
}

/** 确认预约 */
function handleConfirm() {
  proxy.$modal.confirm('确认要确认此预约吗？').then(() => {
    updateReservationStatus({
      id: detail.value.id,
      status: 1
    }).then(() => {
      proxy.$modal.msgSuccess("确认成功");
      getDetail();
    });
  });
}

/** 完成预约 */
function handleComplete() {
  proxy.$modal.confirm('确认要完成此预约吗？').then(() => {
    const now = new Date();
    const nowStr = now.getFullYear() + '-' + 
                   String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(now.getDate()).padStart(2, '0') + ' ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0');
    
    completeReservation({
      id: detail.value.id,
      actualStartTime: detail.value.reservationTime,
      actualEndTime: nowStr
    }).then(() => {
      proxy.$modal.msgSuccess("完成成功");
      getDetail();
    });
  });
}

/** 分配处理人员 */
function handleAssignHandler() {
  assignForm.value = {
    handlerName: detail.value.handlerName || '',
    handlerPhone: detail.value.handlerPhone || ''
  };
  assignDialogVisible.value = true;
}

/** 确认分配 */
function confirmAssign() {
  if (!assignForm.value.handlerName || !assignForm.value.handlerPhone) {
    proxy.$modal.msgError("请填写完整的处理人员信息");
    return;
  }
  
  assignHandler({
    id: detail.value.id,
    handlerName: assignForm.value.handlerName,
    handlerPhone: assignForm.value.handlerPhone
  }).then(() => {
    proxy.$modal.msgSuccess("分配成功");
    assignDialogVisible.value = false;
    getDetail();
  });
}

/** 取消预约 */
function handleCancel() {
  cancelForm.value.cancelReason = '';
  cancelDialogVisible.value = true;
}

/** 确认取消 */
function confirmCancel() {
  if (!cancelForm.value.cancelReason) {
    proxy.$modal.msgError("请输入取消原因");
    return;
  }
  
  cancelReservation({
    id: detail.value.id,
    cancelReason: cancelForm.value.cancelReason
  }).then(() => {
    proxy.$modal.msgSuccess("取消成功");
    cancelDialogVisible.value = false;
    getDetail();
  });
}

onMounted(() => {
  getDetail();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 20px;
}
</style>
