<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px" >
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable style="width: 160px">
          <el-option label="车牌新增" value="1" />
          <el-option label="车牌删除" value="2" />
          <!-- <el-option label="离所处理" value="3" /> -->
          <el-option label="客户临近预产期提醒" value="7" />
          <el-option label="入院待产通知" value="8" />
          <el-option label="出院手续办理通知" value="9" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 160px">
          <el-option label="待处理" value="0" />
          <el-option label="已处理" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="关键信息" prop="keyInfo">
        <el-input
          v-model="queryParams.keyInfo"
          placeholder="请输入关键信息（如车牌号、会员号等）"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          size="mini"
          :disabled="single"
          @click="handleProcess"
          v-hasPermi="['business:taskCenter:process']"
        >处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Edit"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess"
          v-hasPermi="['business:taskCenter:process']"
        >批量处理</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
<!--      <el-table-column label="任务标题" align="center" prop="title" :show-overflow-tooltip="true" />-->
      <el-table-column label="详情" align="center" prop="description" :show-overflow-tooltip="true" width="300"/>
      <el-table-column label="任务类型" align="center"  prop="taskType" width="140">
        <template #default="scope">
          <el-tag v-if="scope.row.taskType === 1" type="primary">车牌新增</el-tag>
          <el-tag v-else-if="scope.row.taskType === 2" type="warning">车牌删除</el-tag>
          <el-tag v-else-if="scope.row.taskType === 3" type="danger">离所处理</el-tag>
          <el-tag v-else-if="scope.row.taskType === 7" type="warning">预产期提醒</el-tag>
          <el-tag v-else-if="scope.row.taskType === 8" type="info">入院待产通知</el-tag>
          <el-tag v-else-if="scope.row.taskType === 9" type="success">出院手续通知</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 0" type="warning">待处理</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="success">已处理</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="关键信息" align="center" prop="keyInfo" width="120" />
      <el-table-column label="客户姓名" align="center" prop="customerName" width="100" />
      <el-table-column label="客户电话" align="center" prop="customerPhone" width="120" />
      <el-table-column label="业务类型" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.businessType === 'car'" type="primary">车辆</el-tag>
          <el-tag v-else-if="scope.row.businessType === 'member'" type="success">会员</el-tag>
          <el-tag v-else-if="scope.row.businessType === 'order'" type="warning">订单</el-tag>
          <el-tag v-else type="info">{{ scope.row.businessType || '其他' }}</el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column label="优先级" align="center" prop="priority" width="80">
        <template #default="scope">
          <el-tag v-if="scope.row.priority === 1" type="info">低</el-tag>
          <el-tag v-else-if="scope.row.priority === 2" type="warning">中</el-tag>
          <el-tag v-else-if="scope.row.priority === 3" type="danger">高</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>-->
      <el-table-column label="处理人员" align="center" prop="processor" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
<!--          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"

          >详情</el-button>-->
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleProcess(scope.row)"

            v-if="scope.row.status === 0"
          >处理</el-button>
          <el-button
            link
            type="warning"
            icon="Sort"
            @click="handlePriority(scope.row)"
            v-hasPermi="['business:taskCenter:priority']"
          >优先级</el-button>
          <el-button
            link
            type="info"
            icon="Edit"
            @click="handleEditBasic(scope.row)"
            v-hasPermi="['business:taskCenter:edit']"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务ID">{{ taskDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="任务标题">{{ taskDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <el-tag v-if="taskDetail.taskType === 1" type="primary">车牌新增</el-tag>
          <el-tag v-else-if="taskDetail.taskType === 2" type="warning">车牌删除</el-tag>
          <el-tag v-else-if="taskDetail.taskType === 3" type="danger">离所处理</el-tag>
          <el-tag v-else-if="taskDetail.taskType === 7" type="warning">预产期提醒</el-tag>
          <el-tag v-else-if="taskDetail.taskType === 8" type="info">入院待产通知</el-tag>
          <el-tag v-else-if="taskDetail.taskType === 9" type="success">出院手续通知</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag v-if="taskDetail.status === 0" type="warning">待处理</el-tag>
          <el-tag v-else-if="taskDetail.status === 1" type="success">已处理</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="关键信息">{{ taskDetail.keyInfo }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ taskDetail.businessType }}</el-descriptions-item>
        <el-descriptions-item label="客户姓名">{{ taskDetail.customerName }}</el-descriptions-item>
        <el-descriptions-item label="客户电话">{{ taskDetail.customerPhone }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag v-if="taskDetail.priority === 1" type="info">低</el-tag>
          <el-tag v-else-if="taskDetail.priority === 2" type="warning">中</el-tag>
          <el-tag v-else-if="taskDetail.priority === 3" type="danger">高</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="任务描述" :span="2">{{ taskDetail.description }}</el-descriptions-item>
        <el-descriptions-item label="处理人员">{{ taskDetail.processor || '未处理' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">
          {{ taskDetail.processTime ? parseTime(taskDetail.processTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未处理' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理备注" :span="2">{{ taskDetail.processRemark || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(taskDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(taskDetail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理任务对话框 -->
    <el-dialog title="处理任务" v-model="processOpen" width="500px" append-to-body>
      <el-form ref="processRef" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="处理备注" prop="processRemark">
          <el-input
            v-model="processForm.processRemark"
            type="textarea"
            placeholder="请输入处理备注"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitProcess">确 定</el-button>
          <el-button @click="cancelProcess">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 优先级设置对话框 -->
    <el-dialog title="设置优先级" v-model="priorityOpen" width="400px" append-to-body>
      <el-form ref="priorityRef" :model="priorityForm" :rules="priorityRules" label-width="80px">
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="priorityForm.priority">
            <el-radio :label="1">低</el-radio>
            <el-radio :label="2">中</el-radio>
            <el-radio :label="3">高</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPriority">确 定</el-button>
          <el-button @click="cancelPriority">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑基本信息对话框 -->
    <el-dialog title="编辑任务基本信息" v-model="editBasicOpen" width="600px" append-to-body>
      <el-form ref="editBasicRef" :model="editBasicForm" :rules="editBasicRules" label-width="100px">
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="editBasicForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        <el-form-item label="关键信息" prop="keyInfo">
          <el-input v-model="editBasicForm.keyInfo" placeholder="请输入关键信息" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="editBasicForm.description"
            type="textarea"
            placeholder="请输入任务描述"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="editBasicForm.priority">
            <el-radio :label="1">低</el-radio>
            <el-radio :label="2">中</el-radio>
            <el-radio :label="3">高</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEditBasic">确 定</el-button>
          <el-button @click="cancelEditBasic">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TaskCenterList">
import { getTasksPage, getTaskDetail, processTask, batchProcessTasks, updateTaskPriority, updateTaskBasicInfo } from "@/api/business/taskCenter"

const { proxy } = getCurrentInstance()
const { parseTime } = proxy

const taskList = ref([])
const detailOpen = ref(false)
const processOpen = ref(false)
const priorityOpen = ref(false)
const editBasicOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskType: null,
    status: null,
    keyInfo: null
  },
  taskDetail: {},
  processForm: {},
  priorityForm: {},
  editBasicForm: {},
  processRules: {
    processRemark: [
      { required: true, message: "处理备注不能为空", trigger: "blur" }
    ]
  },
  priorityRules: {
    priority: [
      { required: true, message: "优先级不能为空", trigger: "change" }
    ]
  },
  editBasicRules: {
    title: [
      { required: true, message: "任务标题不能为空", trigger: "blur" }
    ],
    keyInfo: [
      { required: true, message: "关键信息不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, taskDetail, processForm, priorityForm, editBasicForm, processRules, priorityRules, editBasicRules } = toRefs(data)

/** 查询任务列表 */
function getList() {
  loading.value = true
  // 构建查询参数对象（包含分页参数）
  const queryData = {
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    taskType: queryParams.value.taskType,
    status: queryParams.value.status,
    keyInfo: queryParams.value.keyInfo
  }

  getTasksPage(queryData).then(response => {
    taskList.value = response.list
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 详情按钮操作 */
function handleDetail(row) {
  const taskId = row.id || ids.value[0]
  getTaskDetail(taskId).then(response => {
    taskDetail.value = response
    detailOpen.value = true
  })
}

/** 处理按钮操作 */
function handleProcess(row) {
  reset()
  const taskId = row.id || ids.value[0]
  processForm.value.taskId = taskId
  processOpen.value = true
}

/** 批量处理按钮操作 */
function handleBatchProcess() {
  reset()
  processForm.value.taskIds = ids.value
  processOpen.value = true
}

/** 优先级按钮操作 */
function handlePriority(row) {
  resetPriority()
  const taskId = row.id
  priorityForm.value.taskId = taskId
  priorityForm.value.priority = row.priority
  priorityOpen.value = true
}

/** 编辑基本信息按钮操作 */
function handleEditBasic(row) {
  resetEditBasic()
  editBasicForm.value.taskId = row.id
  editBasicForm.value.title = row.title
  editBasicForm.value.keyInfo = row.keyInfo
  editBasicForm.value.description = row.description
  editBasicForm.value.priority = row.priority
  editBasicOpen.value = true
}

/** 提交处理 */
function submitProcess() {
  proxy.$refs["processRef"].validate(valid => {
    if (valid) {
      if (processForm.value.taskIds) {
        // 批量处理
        batchProcessTasks(processForm.value).then(response => {
          proxy.$modal.msgSuccess("批量处理成功")
          processOpen.value = false
          getList()
        })
      } else {
        // 单个处理
        processTask(processForm.value).then(response => {
          proxy.$modal.msgSuccess("处理成功")
          processOpen.value = false
          getList()
        })
      }
    }
  })
}

/** 提交优先级 */
function submitPriority() {
  proxy.$refs["priorityRef"].validate(valid => {
    if (valid) {
      updateTaskPriority(priorityForm.value).then(response => {
        proxy.$modal.msgSuccess("优先级设置成功")
        priorityOpen.value = false
        getList()
      })
    }
  })
}

/** 提交编辑基本信息 */
function submitEditBasic() {
  proxy.$refs["editBasicRef"].validate(valid => {
    if (valid) {
      updateTaskBasicInfo(editBasicForm.value).then(response => {
        proxy.$modal.msgSuccess("基本信息更新成功")
        editBasicOpen.value = false
        getList()
      })
    }
  })
}

/** 取消处理 */
function cancelProcess() {
  processOpen.value = false
  reset()
}

/** 取消优先级设置 */
function cancelPriority() {
  priorityOpen.value = false
  resetPriority()
}

/** 取消编辑基本信息 */
function cancelEditBasic() {
  editBasicOpen.value = false
  resetEditBasic()
}

/** 表单重置 */
function reset() {
  processForm.value = {
    taskId: null,
    taskIds: null,
    processRemark: null
  }
  proxy.resetForm("processRef")
}

/** 优先级表单重置 */
function resetPriority() {
  priorityForm.value = {
    taskId: null,
    priority: null
  }
  proxy.resetForm("priorityRef")
}

/** 编辑基本信息表单重置 */
function resetEditBasic() {
  editBasicForm.value = {
    taskId: null,
    title: null,
    keyInfo: null,
    description: null,
    priority: null
  }
  proxy.resetForm("editBasicRef")
}

getList()
</script>
