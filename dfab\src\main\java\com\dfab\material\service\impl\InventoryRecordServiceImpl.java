package com.dfab.material.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.material.dto.RecordQueryDto;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.entity.MaterialInfo;
import com.dfab.material.mapper.InventoryRecordMapper;
import com.dfab.material.mapper.MaterialInfoMapper;
import com.dfab.material.service.InventoryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出入库记录Service实现类
 */
@Service
@Slf4j
public class InventoryRecordServiceImpl extends ServiceImpl<InventoryRecordMapper, InventoryRecord> implements InventoryRecordService {

    @Autowired
    private MaterialInfoMapper materialInfoMapper;

    /**
     * 查询出入库记录列表（包含物料信息）
     * @return 出入库记录列表
     */
    @Override
    public List<Map<String, Object>> getRecordList(InventoryRecord queryDto) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 查询所有出入库记录，按创建时间倒序排序
        LambdaQueryWrapper<InventoryRecord> queryWrapper = new LambdaQueryWrapper<>();
        if(null != queryDto){
            if(null != queryDto.getMemberCheckinId()){
                queryWrapper.eq(InventoryRecord::getMemberCheckinId,queryDto.getMemberCheckinId());
            }
            if(null != queryDto.getMaterialId()){
                queryWrapper.eq(InventoryRecord::getMaterialId,queryDto.getMaterialId());
            }
        }
        queryWrapper.orderByDesc(InventoryRecord::getCreateTime);
        List<InventoryRecord> recordList = this.list(queryWrapper);
        
        // 遍历出入库记录，关联物料信息
        for (InventoryRecord record : recordList) {
            MaterialInfo materialInfo = materialInfoMapper.selectById(record.getMaterialId());
            if (materialInfo != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", record.getId());
                map.put("materialId", materialInfo.getId());
                map.put("materialName", materialInfo.getMaterialName());
                map.put("specification", record.getSpecification());
                map.put("recordType", record.getRecordType());
                map.put("quantity", record.getQuantity());
                map.put("useUnit", record.getUseUnit());
                map.put("remark", record.getRemark());
                map.put("creator", record.getCreator());
                map.put("createTime", record.getCreateTime());
                resultList.add(map);
            }
        }
        
        return resultList;
    }

    /**
     * 分页查询出入库记录列表（包含物料信息）
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页出入库记录列表
     */
    @Override
    public Page<Map<String, Object>> getRecordPage(int pageNum, int pageSize) {
        Page<InventoryRecord> page = new Page<>(pageNum, pageSize);

        // 添加按创建时间倒序排序的查询条件
        LambdaQueryWrapper<InventoryRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(InventoryRecord::getCreateTime);

        Page<InventoryRecord> recordPage = this.page(page, queryWrapper);

        Page<Map<String, Object>> resultPage = new Page<>(pageNum, pageSize);
        resultPage.setTotal(recordPage.getTotal());
        resultPage.setPages(recordPage.getPages());

        List<Map<String, Object>> resultList = new ArrayList<>();

        // 遍历出入库记录，关联物料信息
        for (InventoryRecord record : recordPage.getRecords()) {
            MaterialInfo materialInfo = materialInfoMapper.selectById(record.getMaterialId());
            if (materialInfo != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", record.getId());
                map.put("materialId", materialInfo.getId());
                map.put("materialName", materialInfo.getMaterialName());
                map.put("specification", record.getSpecification());
                map.put("recordType", record.getRecordType());
                map.put("quantity", record.getQuantity());
                map.put("useUnit", record.getUseUnit());
                map.put("remark", record.getRemark());
                map.put("creator", record.getCreator());
                map.put("createTime", record.getCreateTime());
                resultList.add(map);
            }
        }

        resultPage.setRecords(resultList);
        return resultPage;
    }

    /**
     * 分页查询出入库记录列表（包含物料信息，支持查询条件）
     * @param queryDto 查询条件
     * @return 分页出入库记录列表
     */
    @Override
    public Page<Map<String, Object>> getRecordPage(RecordQueryDto queryDto) {
        int pageNum = queryDto.getPageNum() != null ? queryDto.getPageNum() : 1;
        int pageSize = queryDto.getPageSize() != null ? queryDto.getPageSize() : 10;

        Page<InventoryRecord> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<InventoryRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 出入库类型查询
        if (queryDto.getRecordType() != null) {
            queryWrapper.eq(InventoryRecord::getRecordType, queryDto.getRecordType());
        }

        // 物料id
        if (queryDto.getMaterialId() != null) {
            queryWrapper.eq(InventoryRecord::getMaterialId, queryDto.getMaterialId());
        }

        // 数量查询
        if (queryDto.getQuantity() != null && !queryDto.getQuantity().isEmpty()) {
            queryWrapper.like(InventoryRecord::getQuantity, queryDto.getQuantity());
        }

        // 使用单位查询
        if (queryDto.getUseUnit() != null && !queryDto.getUseUnit().isEmpty()) {
            queryWrapper.like(InventoryRecord::getUseUnit, queryDto.getUseUnit());
        }

        // 备注查询
        if (queryDto.getRemark() != null && !queryDto.getRemark().isEmpty()) {
            queryWrapper.like(InventoryRecord::getRemark, queryDto.getRemark());
        }

        // 操作人查询
        if (queryDto.getCreator() != null && !queryDto.getCreator().isEmpty()) {
            queryWrapper.like(InventoryRecord::getCreator, queryDto.getCreator());
        }

        // 日期范围查询
        if (queryDto.getDateRange() != null && queryDto.getDateRange().size() == 2) {
            String startDate = queryDto.getDateRange().get(0);
            String endDate = queryDto.getDateRange().get(1);
            if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
                queryWrapper.between(InventoryRecord::getCreateTime, startDate + " 00:00:00", endDate + " 23:59:59");
            }
        }

        // 按创建时间倒序排序
        queryWrapper.orderByDesc(InventoryRecord::getCreateTime);

        Page<InventoryRecord> recordPage = this.page(page, queryWrapper);

        Page<Map<String, Object>> resultPage = new Page<>(pageNum, pageSize);
        resultPage.setTotal(recordPage.getTotal());
        resultPage.setPages(recordPage.getPages());

        List<Map<String, Object>> resultList = new ArrayList<>();

        // 遍历出入库记录，关联物料信息并应用物料相关查询条件
        for (InventoryRecord record : recordPage.getRecords()) {
            MaterialInfo materialInfo = materialInfoMapper.selectById(record.getMaterialId());
            if (materialInfo != null) {
                // 应用物料相关查询条件过滤
                boolean match = true;

                // 物料名称模糊查询
                if (queryDto.getMaterialName() != null && !queryDto.getMaterialName().isEmpty()) {
                    if (!materialInfo.getMaterialName().toLowerCase().contains(queryDto.getMaterialName().toLowerCase())) {
                        match = false;
                    }
                }

                // 规格型号模糊查询
                if (queryDto.getSpecification() != null && !queryDto.getSpecification().isEmpty()) {
                    if (!materialInfo.getSpecification().toLowerCase().contains(queryDto.getSpecification().toLowerCase())) {
                        match = false;
                    }
                }

                if (match) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", record.getId());
                    map.put("materialId", materialInfo.getId());
                    map.put("materialName", materialInfo.getMaterialName());
                    map.put("specification", record.getSpecification());
                    map.put("recordType", record.getRecordType());
                    map.put("quantity", record.getQuantity());
                    map.put("useUnit", record.getUseUnit());
                    map.put("remark", record.getRemark());
                    map.put("creator", record.getCreator());
                    map.put("createTime", record.getCreateTime());
                    resultList.add(map);
                }
            }
        }

        resultPage.setRecords(resultList);
        return resultPage;
    }
}