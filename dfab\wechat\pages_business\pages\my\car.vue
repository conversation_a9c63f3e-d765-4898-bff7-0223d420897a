<template>
	<view class="car-container">
		<!-- 自定义导航栏 -->
		<custom-nav-bar title="我的车辆" showBack></custom-nav-bar>

		<!-- 添加车辆表单 -->
		<view class="form-container" v-if="showForm">
			<view class="form-item">
				<text class="item-label required">车主姓名</text>
				<input type="text" v-model="carForm.ownerName" placeholder="请输入车主姓名（2-10字符）" />
				<text class="error-tip" v-if="errors.ownerName">{{ errors.ownerName }}</text>
			</view>

			<view class="form-item">
				<text class="item-label required">车牌号码</text>
				<!-- 使用点击输入框方式调用自定义车牌号输入键盘 -->
				<input type="text" class="input" v-model="carForm.plateNumber" placeholder="请输入车牌号码" @click="openPlateKeyboard" />
				<text class="error-tip" v-if="errors.plateNumber">{{ errors.plateNumber }}</text>
			</view>

			<view class="form-item">
				<text class="item-label required">联系电话</text>
				<input type="number" v-model="carForm.phoneNumber" placeholder="请输入11位手机号" maxlength="11" />
				<text class="error-tip" v-if="errors.phoneNumber">{{ errors.phoneNumber }}</text>
			</view>

			<!-- 车辆管理须知 -->
			<view class="notice-section">
				<view class="notice-title">车辆登记须知</view>
				<view class="notice-item">1. 请确保填写真实有效的车辆信息</view>
				<view class="notice-item">2. 车牌号格式示例：京A12345</view>
				<view class="notice-item">3. 一个用户可以登记多辆车辆</view>
				<view class="notice-item">4. 车辆信息将用于院内停车管理</view>
			</view>

			<button class="submit-btn" @click="submitForm">保存车辆信息</button>
			<button class="cancel-btn" @click="cancelForm">取消</button>
		</view>

		<!-- 车辆列表 -->
		<scroll-view
			class="car-list"
			v-if="!showForm"
			scroll-y
		>
			<view class="empty-tip" v-if="carList.length === 0 && !isLoading">
				<text>暂无车辆信息</text>
			</view>
			<view class="loading-container" v-if="isLoading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			
			<view class="car-item" v-for="(car, index) in carList" :key="car.id || index">
				<view class="car-info">
					<view class="info-row">
						<text class="info-label">车主：</text>
						<text class="info-value">{{ car.ownerName }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">车牌号：</text>
						<text class="info-value plate">{{ car.plateNumber }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">联系电话：</text>
						<text class="info-value">{{ car.phoneNumber }}</text>
					</view>
				</view>
				<button class="delete-btn" @click="deleteCar(car.id, car.plateNumber)">删除</button>
			</view>
			
			<button class="add-btn" @click="showAddForm">添加车辆</button>
		</scroll-view>
		
		<!-- 自定义车牌号输入键盘 -->
		<view class="plate-keyboard" v-if="showPlateKeyboard">
			<view class="keyboard-header">
				<view class="keyboard-title">请选择车牌号</view>
				<view class="keyboard-close" @click="closePlateKeyboard">×</view>
			</view>
			
			<view class="keyboard-preview">
				<text class="preview-text">{{ carForm.plateNumber }}</text>
				<view class="manual-input-toggle" @click="toggleManualInput">
					{{ isManualInput ? '键盘输入' : '手动输入' }}
				</view>
			</view>
			
			<!-- 手动输入模式 -->
			<view class="manual-input-section" v-if="isManualInput">
				<input 
					type="text" 
					class="manual-input" 
					:value="carForm.plateNumber"
					placeholder="请手动输入车牌号码" 
					@input="manualInputPlate"
					maxlength="8"
				/>
				<view class="manual-input-tip">支持特殊车牌格式，如：使领馆车牌等</view>
			</view>
			
			<!-- 键盘输入模式 -->
			<view v-else>
				<!-- 常用车牌前缀 -->
				<view class="common-plate-section" v-if="!carForm.plateNumber">
					<view class="section-title">常用车牌</view>
					<view class="common-plate-list">
						<view 
							class="common-plate-item" 
							v-for="(prefix, index) in commonPlate" 
							:key="'prefix-' + index"
							@click="selectCommonPlate(prefix)"
						>
							{{ prefix }}
						</view>
					</view>
				</view>
				
				<!-- 省份选择 -->
				<view class="keyboard-keys province-keys" v-if="!carForm.plateNumber">
					<view class="section-title">选择省份</view>
					<view class="province-grid">
						<view 
							class="key province-key" 
							v-for="(prov, index) in plateProvince" 
							:key="'prov-' + index"
							@click="selectProvince(prov)"
							:class="{'highlight': prov === '粤'}"
						>
							{{ prov }}
						</view>
					</view>
				</view>
				
				<!-- 字符选择 - 标准键盘布局 -->
				<view class="keyboard-keys character-keys" v-if="carForm.plateNumber">
					<view class="section-title">选择字符</view>
					
					<!-- 数字行 -->
					<view class="key-row">
						<view 
							class="key number-key" 
							v-for="(char, index) in plateRow1" 
							:key="'num-' + index"
							@click="selectCharacter(char)"
						>
							{{ char }}
						</view>
					</view>
					
					<!-- 第一行字母 -->
					<view class="key-row">
						<view 
							class="key letter-key" 
							v-for="(char, index) in plateRow2" 
							:key="'row2-' + index"
							@click="selectCharacter(char)"
						>
							{{ char }}
						</view>
					</view>
					
					<!-- 第二行字母 -->
					<view class="key-row key-row-middle">
						<view 
							class="key letter-key" 
							v-for="(char, index) in plateRow3" 
							:key="'row3-' + index"
							@click="selectCharacter(char)"
							:class="{'highlight': char === 'L' && carForm.plateNumber.startsWith('粤')}"
						>
							{{ char }}
						</view>
					</view>
					
					<!-- 第三行字母 -->
					<view class="key-row">
						<view class="key delete-key" @click="deleteCharacter">删除</view>
						<view 
							class="key letter-key" 
							v-for="(char, index) in plateRow4" 
							:key="'row4-' + index"
							@click="selectCharacter(char)"
						>
							{{ char }}
						</view>
					</view>
					
					<!-- 特殊字符 -->
					<view class="key-row">
						<view 
							class="key special-key" 
							v-for="(char, index) in plateSpecial" 
							:key="'special-' + index"
							@click="selectCharacter(char)"
						>
							{{ char }}
						</view>
						<view class="key confirm-key" @click="closePlateKeyboard">完成</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * 我的车辆页面
	 * 实现车辆信息的添加和展示功能
	 */
	// import { API, request } from '../../config/api.js';
	// import { API, request } from '../../../config/api.js';
	import { getCarInfoList, addOrUpdateCarInfo, deleteCarInfo } from '../../../config/api/car_info.js';
	import CustomNavBar from '@/components/custom-nav-bar/custom-nav-bar.vue';

	export default {
		components: {
			CustomNavBar
		},
		data() {
			return {
				showForm: false,
				carList: [],
				carForm: {
					ownerName: '',
					plateNumber: '',
					phoneNumber: '',
					openId: '', // 微信openId
					userId: '' // 添加用户ID字段，用于权限隔离
				},
				errors: {},
				isLoading: false,
				isRefreshing: false,
				userInfo: null,
				// 车牌号输入相关数据
				showPlateKeyboard: false,
				// 调整顺序，将粤放在前面
				plateProvince: ['粤', '京', '津', '冀', '晋', '蒙', '辽', '吉', '黑', '沪', '苏', '浙', '皖', '闽', '赣', '鲁', '豫', '鄂', '湘', '桂', '琼', '渝', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新', '港', '澳', '台'],
				// 按照标准键盘布局排列
				plateRow1: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
				plateRow2: ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
				plateRow3: ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
				plateRow4: ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
				plateSpecial: ['挂', '学', '警', '港', '澳'],
				commonPlate: ['粤L', '粤A', '粤B', '粤C'], // 常用车牌前缀
				isManualInput: false, // 是否显示手动输入模式
			};
		},
		onLoad() {
			// 检查登录状态
			this.checkLoginStatus();
		},
		onShow() {
			// 页面显示时检查登录状态，确保数据最新
			this.checkLoginStatus();
		},
		// 下拉刷新
		onPullDownRefresh() {
			console.log('触发下拉刷新 - 微信小程序');

			// 微信小程序环境下的下拉刷新处理
			// #ifdef MP-WEIXIN
			if (!this.userInfo) {
				// 如果未登录，立即停止下拉刷新
				try {
					wx.stopPullDownRefresh();
				} catch (e) {
					console.warn('stopPullDownRefresh failed:', e);
				}
				return;
			}

			// 显示导航栏加载状态
			try {
				wx.showNavigationBarLoading();
			} catch (e) {
				console.warn('showNavigationBarLoading failed:', e);
			}

			// 获取车辆列表
			this.getCarList();

			// 确保在任何情况下都能停止刷新动画
			setTimeout(() => {
				try {
					wx.hideNavigationBarLoading();
				} catch (e) {
					console.warn('hideNavigationBarLoading failed:', e);
				}
				
				try {
					wx.stopPullDownRefresh();
				} catch (e) {
					console.warn('stopPullDownRefresh failed:', e);
				}
			}, 3000);
			// #endif

			// 非微信小程序环境
			// #ifndef MP-WEIXIN
			this.refreshList();
			// #endif
		},
		methods: {
			/**
			 * 检查登录状态
			 */
			checkLoginStatus() {
				const userInfo = uni.getStorageSync('userInfo');
				const token = uni.getStorageSync('token');

				if (!userInfo || !token) {
					// 未登录，跳转到登录页面
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});

					// 停止所有加载动画
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('hideLoading failed:', e);
					}
					
					try {
						uni.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}

					// 微信小程序特有的处理
					// #ifdef MP-WEIXIN
					try {
						wx.hideNavigationBarLoading();
					} catch (e) {
						console.warn('hideNavigationBarLoading failed:', e);
					}
					
					try {
						wx.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
					// #endif

					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/login/login'
						});
					}, 1500);
					return false;
				}

				// 已登录，保存用户信息
				try {
					// 尝试解析用户信息
					this.userInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;

					// 确保用户ID存在
					if (!this.userInfo.id) {
						throw new Error('用户ID不存在');
					}

					console.log('当前登录用户ID:', this.userInfo.id);

					// 获取车辆列表
					this.getCarList();

					return true;
				} catch (error) {
					console.error('解析用户信息失败:', error);

					// 清除无效的用户信息
					uni.removeStorageSync('userInfo');
					uni.removeStorageSync('token');

					uni.showToast({
						title: '登录信息无效，请重新登录',
						icon: 'none'
					});

					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/login/login'
						});
					}, 1500);

					return false;
				}
			},

			/**
			 * 获取车辆列表
			 */
			getCarList() {
				if (!this.userInfo) {
					this.isLoading = false;
					// 隐藏所有加载提示
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('hideLoading failed:', e);
					}
					
					try {
						uni.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
					return;
				}

				this.isLoading = true;

				// 获取用户openId
				console.log('获取车辆列表，当前用户:', this.userInfo);

				// 使用新的API接口
				getCarInfoList()
				.then(data => {
					console.log('获取车辆列表成功:', data);
					this.carList = data || [];

					// 重置加载状态
					this.isLoading = false;

					// 隐藏所有加载提示
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('hideLoading failed:', e);
					}
					
					try {
						uni.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}

					// 微信小程序特有的处理
					// #ifdef MP-WEIXIN
					try {
						wx.hideNavigationBarLoading();
					} catch (e) {
						console.warn('hideNavigationBarLoading failed:', e);
					}
					
					try {
						wx.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
					// #endif
				})
				.catch(err => {
					console.error('获取车辆列表失败:', err);

					// 重置加载状态
					this.isLoading = false;

					// 隐藏所有加载提示
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('hideLoading failed:', e);
					}
					
					try {
						uni.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}

					// 微信小程序特有的处理
					// #ifdef MP-WEIXIN
					try {
						wx.hideNavigationBarLoading();
					} catch (e) {
						console.warn('hideNavigationBarLoading failed:', e);
					}
					
					try {
						wx.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
					// #endif

					// 显示空列表
					this.carList = [];

					// 显示错误提示
					uni.showToast({
						title: '获取车辆列表失败，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				});
			},

			/**
			 * 刷新列表
			 */
			refreshList() {
				// 微信小程序环境
				// #ifdef MP-WEIXIN
				// 显示导航栏加载状态
				try {
					wx.showNavigationBarLoading();
				} catch (e) {
					console.warn('showNavigationBarLoading failed:', e);
				}

				// 获取车辆列表
				this.getCarList();

				// 确保在网络请求失败时也能隐藏加载提示
				setTimeout(() => {
					try {
						wx.hideNavigationBarLoading();
					} catch (e) {
						console.warn('hideNavigationBarLoading failed:', e);
					}
					
					try {
						wx.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
				}, 3000);
				// #endif

				// 非微信小程序环境
				// #ifndef MP-WEIXIN
				// 显示加载中提示
				try {
					uni.showLoading({
						title: '刷新中...',
						mask: true
					});
				} catch (e) {
					console.warn('showLoading failed:', e);
				}

				// 获取车辆列表
				this.getCarList();

				// 确保在网络请求失败时也能隐藏加载提示
				setTimeout(() => {
					try {
						uni.hideLoading();
					} catch (e) {
						console.warn('hideLoading failed:', e);
					}
					
					try {
						uni.stopPullDownRefresh();
					} catch (e) {
						console.warn('stopPullDownRefresh failed:', e);
					}
				}, 2000);
				// #endif
			},

			/**
			 * 显示添加车辆表单
			 */
			showAddForm() {
				this.resetForm();
				this.showForm = true;
			},

			/**
			 * 取消表单
			 */
			cancelForm() {
				this.showForm = false;
				this.resetForm();
			},

			/**
			 * 重置表单
			 */
			resetForm() {
				if (!this.userInfo) return;

				this.carForm = {
					ownerName: '',
					plateNumber: '',
					phoneNumber: '',
					openId: this.userInfo.openId || '', // 设置用户openId
					userId: this.userInfo.id || '' // 设置用户ID
				};
				this.errors = {};
			},

			/**
			 * 打开车牌号输入键盘
			 */
			openPlateKeyboard() {
				// 显示自定义的车牌输入键盘
				this.showPlateKeyboard = true;
			},
			
			/**
			 * 关闭车牌号输入键盘
			 */
			closePlateKeyboard() {
				this.showPlateKeyboard = false;
			},
			
			/**
			 * 选择车牌省份
			 */
			selectProvince(province) {
				// 设置省份
				this.carForm.plateNumber = province;
			},
			
			/**
			 * 选择常用车牌前缀
			 */
			selectCommonPlate(prefix) {
				// 设置常用车牌前缀
				this.carForm.plateNumber = prefix;
			},
			
			/**
			 * 选择车牌字符
			 */
			selectCharacter(char) {
				// 追加字符到车牌号
				this.carForm.plateNumber += char;
				
				// 如果车牌号已达到标准长度(7或8位)，关闭键盘
				if (this.carForm.plateNumber.length >= 8) {
					this.closePlateKeyboard();
				}
			},
			
			/**
			 * 删除车牌号最后一个字符
			 */
			deleteCharacter() {
				if (this.carForm.plateNumber.length > 0) {
					this.carForm.plateNumber = this.carForm.plateNumber.slice(0, -1);
				}
			},

			/**
			 * 验证表单
			 */
			validateForm() {
				let isValid = true;
				this.errors = {};

				// 验证车主姓名
				if (!this.carForm.ownerName) {
					this.errors.ownerName = '车主姓名不能为空';
					isValid = false;
				} else if (this.carForm.ownerName.length < 2 || this.carForm.ownerName.length > 10) {
					this.errors.ownerName = '车主姓名长度必须在2-10个字符之间';
					isValid = false;
				}

				// 验证车牌号
				if (!this.carForm.plateNumber) {
					this.errors.plateNumber = '车牌号码不能为空';
					isValid = false;
				} else {
					// 中国车牌号正则验证
					const plateReg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/;
					if (!plateReg.test(this.carForm.plateNumber)) {
						this.errors.plateNumber = '车牌号格式不正确';
						isValid = false;
					}
				}

				// 验证手机号
				if (!this.carForm.phoneNumber) {
					this.errors.phoneNumber = '联系电话不能为空';
					isValid = false;
				} else if (!/^1[3-9]\d{9}$/.test(this.carForm.phoneNumber)) {
					this.errors.phoneNumber = '手机号格式不正确';
					isValid = false;
				}

				return isValid;
			},

			/**
			 * 提交表单
			 */
			submitForm() {
				if (!this.validateForm()) {
					return;
				}

				console.log('提交车辆表单:', this.carForm);

				// 使用新的添加或更新API接口
				addOrUpdateCarInfo(this.carForm)
				.then(() => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					this.showForm = false;
					this.getCarList();
				})
				.catch((err) => {
					console.error('保存车辆失败:', err);
					
					// 重置错误提示
					this.errors = {};

					// 处理特定错误
					if (err && err.message) {
						// 处理车牌号已存在的错误
						if (err.message.includes('车牌号已存在')) {
							this.errors.plateNumber = '车牌号已存在，请更换车牌号';
							uni.showToast({
								title: '车牌号已存在，请更换车牌号',
								icon: 'none',
								duration: 2000
							});
						} else {
							// 显示其他错误
							uni.showToast({
								title: err.message || '保存失败，请重试',
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						// 未知错误
						uni.showToast({
							title: '保存失败，请重试',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},

			/**
			 * 删除车辆
			 * @param id 车辆ID
			 * @param plateNumber 车牌号（用于提示）
			 */
			deleteCar(id, plateNumber) {
				if (!this.userInfo) return;

				uni.showModal({
					title: '确认删除',
					content: `确定要删除车牌号为 ${plateNumber} 的车辆吗？`,
					success: (res) => {
						if (res.confirm) {
							// 构造删除参数
							const params = {
								id: id
							};
							
							console.log('删除车辆，参数:', params);

							// 使用新的删除API接口
							deleteCarInfo(params)
							.then(() => {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								this.getCarList();
							})
							.catch((err) => {
								console.error('删除车辆失败:', err);
								uni.showToast({
									title: '删除失败',
									icon: 'none'
								});
							});
						}
					}
				});
			},

			/**
			 * 切换到手动输入模式
			 */
			toggleManualInput() {
				this.isManualInput = !this.isManualInput;
			},
			
			/**
			 * 手动输入车牌号
			 */
			manualInputPlate(e) {
				// 直接使用输入框的值
				this.carForm.plateNumber = e.detail.value;
			},
		}
	};
</script>

<style lang="scss" scoped>
	.car-container {
		padding: 0 20rpx 20rpx;
		background-color: #f8f5f2;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.form-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.form-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.form-item:first-child {
		padding-top: 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.required:after {
		content: '*';
		color: #8b5a2b;
		margin-left: 5rpx;
	}

	.error-tip {
		font-size: 24rpx;
		color: #FF4D4F;
		margin-top: 8rpx;
	}

	input {
		border: 2rpx solid #ddd;
		padding: 20rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		width: 100%;
		height: 100rpx;
		line-height: normal;
		box-sizing: border-box;
		background-color: #fafafa;
		color: #333;
		vertical-align: top;
	}

	/* 确保输入框获得焦点时的样式 */
	input:focus {
		border-color: #8b5a2b;
		background-color: #fff;
		outline: none;
		box-shadow: 0 0 0 2rpx rgba(139, 90, 43, 0.1);
	}

	/* 输入框占位符样式 */
	input::placeholder {
		color: #aaa;
		font-size: 32rpx;
		opacity: 0.8;
	}

	/* 针对不同平台的输入框优化 */
	/* #ifdef MP-WEIXIN */
	input {
		height: 100rpx !important;
		line-height: normal !important;
		padding: 30rpx 20rpx !important;
		font-size: 32rpx !important;
	}
	/* #endif */

	/* 确保输入框内容正确显示 */
	.form-item input {
		display: block;
		width: 100%;
	}

	.notice-section {
		margin: 30rpx 0;
		padding: 20rpx;
		background-color: #f8f5f2;
		border-radius: 8rpx;
		border-left: 4rpx solid #8b5a2b;
	}

	.notice-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 15rpx;
	}

	.notice-item {
		font-size: 24rpx;
		color: #666;
		line-height: 1.8;
	}

	.submit-btn {
		background-color: #8b5a2b !important;
		color: #fff !important;
		border-radius: 50rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		font-weight: bold;
		border: none !important;
		outline: none !important;
		height: 88rpx;
		line-height: 88rpx;
	}

	.submit-btn::after {
		border: none !important;
	}

	.submit-btn:hover {
		background-color: #7A4F26 !important;
		color: #fff !important;
	}

	.submit-btn:active {
		background-color: #6B4421 !important;
		color: #fff !important;
	}

	.cancel-btn {
		background-color: #f0f0f0;
		color: #8b5a2b;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 50rpx;
		font-size: 32rpx;
		margin-top: 20rpx;
		width: 100%;
		border: 1rpx solid #8b5a2b;
	}

	/* 车辆列表样式 */
	.car-list {
		margin-top: 20rpx;
		height: calc(100vh - 200rpx); /* 调整高度以适应导航栏 */
		box-sizing: border-box;
		padding: 0 10rpx;
	}

	.empty-tip {
		text-align: center;
		padding: 100rpx 0;
		color: #999;
		font-size: 28rpx;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #8b5a2b;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		margin-top: 20rpx;
		color: #999;
		font-size: 28rpx;
	}

	.car-item {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		position: relative;
	}

	.car-info {
		padding: 10rpx 0;
	}

	.info-row {
		display: flex;
		margin-bottom: 16rpx;
	}

	.info-label {
		width: 150rpx;
		color: #666;
		font-size: 28rpx;
	}

	.info-value {
		flex: 1;
		color: #333;
		font-size: 28rpx;
		word-break: break-all;
	}

	.info-value.plate {
		font-weight: bold;
		color: #8b5a2b;
	}

	.delete-btn {
		background-color: #ff4d4f;
		color: #fff;
		font-size: 24rpx;
		padding: 10rpx 24rpx;
		border-radius: 30rpx;
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		line-height: 1.5;
		box-shadow: 0 2rpx 5rpx rgba(255, 77, 79, 0.2);
	}

	.add-btn {
		background-color: #8b5a2b;
		color: #fff;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		margin-bottom: 40rpx;
		width: 100%;
		font-weight: bold;
	}

	/* 车牌号输入键盘 */
	.plate-keyboard {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #f8f8f8;
		border-top: 1rpx solid #ddd;
		z-index: 999;
		padding-bottom: env(safe-area-inset-bottom);
		border-radius: 20rpx 20rpx 0 0;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
	}
	
	.keyboard-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
	}
	
	.keyboard-title {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}
	
	.keyboard-close {
		font-size: 44rpx;
		color: #999;
		padding: 0 20rpx;
		height: 60rpx;
		width: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.keyboard-preview {
		background-color: #fff;
		padding: 24rpx 30rpx;
		text-align: center;
		margin: 20rpx;
		border-radius: 12rpx;
		border: 1rpx solid #ddd;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		position: relative;
	}
	
	.preview-text {
		font-size: 48rpx;
		color: #333;
		font-weight: bold;
		letter-spacing: 10rpx;
		min-height: 60rpx;
	}
	
	.manual-input-toggle {
		position: absolute;
		right: 20rpx;
		top: 20rpx;
		font-size: 24rpx;
		color: #8b5a2b;
		background-color: #f9f5f0;
		padding: 6rpx 16rpx;
		border-radius: 30rpx;
		border: 1rpx solid #ddc5a8;
	}
	
	/* 手动输入模式 */
	.manual-input-section {
		padding: 20rpx 30rpx;
	}
	
	.manual-input {
		background-color: #fff;
		border: 1rpx solid #ddd;
		border-radius: 12rpx;
		padding: 20rpx;
		width: 100%;
		font-size: 32rpx;
		box-sizing: border-box;
		margin-bottom: 16rpx;
	}
	
	.manual-input-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.keyboard-keys {
		padding: 20rpx;
	}
	
	.key {
		width: 62rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #fff;
		margin: 6rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		border: 1rpx solid #ddd;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		transition: all 0.2s;
	}
	
	.key:active {
		background-color: #f0f0f0;
		transform: scale(0.96);
	}
	
	/* 键盘行样式 */
	.key-row {
		display: flex;
		justify-content: center;
		margin-bottom: 10rpx;
	}
	
	.key-row-middle {
		padding-left: 30rpx; // 偏移让第二行居中
	}
	
	.number-key {
		color: #333;
	}
	
	.letter-key {
		color: #333;
		font-weight: bold;
	}
	
	.special-key {
		color: #8b5a2b;
		font-size: 28rpx;
	}
	
	.delete-key {
		width: 90rpx;
		color: #ff4d4f;
		background-color: #fff5f5;
		border-color: #ffcccb;
		font-size: 28rpx;
	}
	
	.confirm-key {
		width: 90rpx;
		color: #8b5a2b;
		background-color: #f9f5f0;
		border-color: #ddc5a8;
		font-size: 28rpx;
	}
	
	/* 常用车牌前缀样式 */
	.common-plate-section {
		margin: 20rpx;
	}

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 15rpx;
		padding-left: 10rpx;
	}

	.common-plate-list {
		display: flex;
		flex-wrap: wrap;
	}

	.common-plate-item {
		background-color: #f9f5f0;
		padding: 12rpx 24rpx;
		border-radius: 50rpx;
		margin: 8rpx;
		font-size: 28rpx;
		color: #8b5a2b;
		font-weight: bold;
		box-shadow: 0 2rpx 6rpx rgba(139, 90, 43, 0.15);
	}
	
	.common-plate-item:active {
		background-color: #f0e6dc;
		transform: scale(0.96);
	}

	/* 省份键盘样式 */
	.province-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
	}
	
	.province-key {
		color: #333;
		font-weight: bold;
	}
	
	.province-key.highlight {
		background-color: #f9f5f0;
		color: #8b5a2b;
		border-color: #8b5a2b;
		box-shadow: 0 2rpx 8rpx rgba(139, 90, 43, 0.2);
	}
	
	.highlight {
		background-color: #f9f5f0;
		color: #8b5a2b;
		border-color: #8b5a2b;
		box-shadow: 0 2rpx 8rpx rgba(139, 90, 43, 0.2);
	}
</style>