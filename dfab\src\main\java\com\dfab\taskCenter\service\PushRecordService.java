package com.dfab.taskCenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.taskCenter.entity.PushRecord;

import java.util.List;

/**
 * 推送记录Service接口
 */
public interface PushRecordService extends IService<PushRecord> {

    /**
     * 根据任务ID查询推送记录
     * @param taskId 任务ID
     * @return 推送记录列表
     */
    List<PushRecord> getRecordsByTaskId(Long taskId);

    /**
     * 根据推送状态查询推送记录
     * @param status 推送状态
     * @return 推送记录列表
     */
    List<PushRecord> getRecordsByStatus(Integer status);

    /**
     * 根据接收者openId查询推送记录
     * @param receiverOpenId 接收者openId
     * @return 推送记录列表
     */
    List<PushRecord> getRecordsByReceiverOpenId(String receiverOpenId);

    /**
     * 查询推送失败的记录
     * @return 推送失败的记录列表
     */
    List<PushRecord> getFailedRecords();

    /**
     * 查询需要重试的推送记录
     * @param maxRetryCount 最大重试次数
     * @return 需要重试的推送记录列表
     */
    List<PushRecord> getRetryRecords(Integer maxRetryCount);

    /**
     * 统计今日推送数量
     * @return 今日推送数量
     */
    int getTodayPushCount();

    /**
     * 统计推送成功率
     * @return 推送成功率
     */
    Double getSuccessRate();

    /**
     * 根据推送类型统计数量
     * @param pushType 推送类型
     * @return 推送数量
     */
    int getCountByPushType(Integer pushType);

    /**
     * 创建推送记录
     * @param record 推送记录
     * @return 创建结果
     */
    boolean createPushRecord(PushRecord record);

    /**
     * 更新推送记录状态
     * @param recordId 记录ID
     * @param status 推送状态
     * @param response 响应信息
     * @param errorMsg 错误信息
     * @return 更新结果
     */
    boolean updatePushStatus(Long recordId, Integer status, String response, String errorMsg);

    /**
     * 增加重试次数
     * @param recordId 记录ID
     * @return 更新结果
     */
    boolean incrementRetryCount(Long recordId);
}
