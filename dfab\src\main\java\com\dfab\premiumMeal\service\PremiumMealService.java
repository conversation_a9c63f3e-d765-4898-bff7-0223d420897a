package com.dfab.premiumMeal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.premiumMeal.entity.PremiumMeal;

import java.util.List;

/**
 * 高档餐 Service 接口
 */
public interface PremiumMealService extends IService<PremiumMeal> {

    /**
     * 根据会员入住ID获取高档餐列表
     * @param memberCheckinId 会员入住ID
     * @return 高档餐列表
     */
    List<PremiumMeal> getByMemberCheckinId(Long memberCheckinId);

    /**
     * 分页查询高档餐信息
     * @param premiumMeal 查询条件
     * @return 分页结果
     */
    IPage<PremiumMeal> page(PremiumMeal premiumMeal);

    /**
     * 添加高档餐记录
     * @param premiumMeal 高档餐信息
     * @return 添加结果
     */
    PremiumMeal addByAdmin(PremiumMeal premiumMeal);

    /**
     * 更新高档餐记录
     * @param premiumMeal 高档餐信息
     * @return 更新结果
     */
    Boolean updateByAdmin(PremiumMeal premiumMeal);

    /**
     * 删除高档餐记录
     * @param id 高档餐ID
     * @return 删除结果
     */
    Boolean removeByAdmin(Long id);
}
