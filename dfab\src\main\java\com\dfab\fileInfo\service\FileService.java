package com.dfab.fileInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.fileInfo.entity.FileInfo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;

public interface FileService extends IService<FileInfo> {
    FileInfo uploadFile(MultipartFile file, String packageName) throws IOException;
    void downloadFile(String id, HttpServletResponse response) throws IOException;
    void previewFile(String id, HttpServletResponse response) throws IOException;
}