package com.dfab.customerSupplyRequest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 客户用品补充需求实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("customer_supply_request")
@Schema(description = "客户用品补充需求实体类")
@Builder
public class CustomerSupplyRequest extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID", example = "1234567890")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户微信openId
     */
    @Size(max = 100, message = "openId长度不能超过100个字符")
    @Schema(description = "用户的微信 openId", example = "wx1234567890")
    private String openId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 会员入住ID
     */
    @Schema(description = "会员入住ID", example = "2001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberCheckinId;

    /**
     * 用户姓名
     */
    @Size(max = 50, message = "用户姓名长度不能超过50个字符")
    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    /**
     * 用户手机号
     */
    @Size(max = 20, message = "用户手机号长度不能超过20个字符")
    @Schema(description = "用户手机号", example = "13800138000")
    private String userPhone;

    /**
     * 需求内容描述
     */
    @NotBlank(message = "需求内容不能为空")
    @Size(min = 1, max = 1000, message = "需求内容长度必须在1-1000个字符之间")
    @Schema(description = "需求内容描述", example = "需要补充毛巾和洗发水")
    private String requestContent;

    /**
     * 处理状态：0-待处理，1-已处理，2-已拒绝
     */
    @Schema(description = "处理状态：0-待处理，1-已处理，2-已拒绝", example = "0")
    private Integer status;

    /**
     * 管理员备注
     */
    @Size(max = 500, message = "管理员备注长度不能超过500个字符")
    @Schema(description = "管理员备注", example = "已安排工作人员处理")
    private String adminRemark;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间", example = "2024-01-01 12:00:00")
    private LocalDateTime processTime;

    /**
     * 处理人
     */
    @Size(max = 50, message = "处理人长度不能超过50个字符")
    @Schema(description = "处理人", example = "管理员")
    private String processBy;



}
