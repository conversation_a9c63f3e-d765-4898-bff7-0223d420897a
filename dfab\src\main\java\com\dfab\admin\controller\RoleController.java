/*
package com.dfab.admin.controller;

import com.dfab.admin.entity.Role;
import com.dfab.admin.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/roles")
@Tag(name = "RoleController", description = "角色管理接口")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Operation(summary = "获取所有角色")
    @GetMapping
    public List<Role> getAllRoles() {
        return roleService.list();
    }

    @Operation(summary = "根据 ID 获取角色")
    @GetMapping("/{id}")
    public Role getRoleById(@PathVariable Long id) {
        return roleService.getById(id);
    }

    @Operation(summary = "创建新的角色")
    @PostMapping
    public Role createRole(@RequestBody Role role) {
        roleService.save(role);
        return role;
    }

    @Operation(summary = "更新角色信息")
    @PutMapping("/{id}")
    public Role updateRole(@PathVariable Long id, @RequestBody Role role) {
        role.setId(id);
        roleService.updateById(role);
        return role;
    }

    @Operation(summary = "删除角色")
    @DeleteMapping("/{id}")
    public void deleteRole(@PathVariable Long id) {
        roleService.removeById(id);
    }
}*/
