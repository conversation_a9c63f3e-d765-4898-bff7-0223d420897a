package com.dfab.premiumMeal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 高档餐实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("premium_meal")
@Schema(description = "高档餐实体类")
@Builder
public class PremiumMeal extends BaseEntity {
    /**
     * 高档餐记录的唯一标识，系统自动分配的 ID。
     * 使用JsonSerialize将Long转为String，避免前端精度丢失
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "高档餐记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 会员入住ID，关联member_checkin表
     */
    @Schema(description = "会员入住ID", example = "1", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberCheckinId;

    /**
     * 高档餐详情描述
     */
    @Schema(description = "高档餐详情", example = "澳洲龙虾套餐", required = true)
    private String premiumMealDetail;

    /**
     * 食用时间
     */
    @Schema(description = "食用时间", example = "2024-01-01", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date consumeTime;

    /**
     * 时间段
     */
    @Schema(description = "时间段", example = "早餐 breakfast  午餐 lunch  晚餐 supper")
    private String type;

    /**
     * 份数
     */
    @Schema(description = "份数", example = "2", required = true)
    private Integer quantity;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "特殊要求")
    private String remark;


}
