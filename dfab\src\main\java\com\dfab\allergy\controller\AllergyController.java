package com.dfab.allergy.controller;

import com.dfab.allergy.entity.Allergy;
import com.dfab.allergy.service.AllergyService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户忌口 Controller 类，提供用户忌口信息的增删改查接口。
 */
@RestController
@RequestMapping("/api/allergy")
@Tag(name = "AllergyController", description = "用户忌口管理接口，提供后台对用户忌口信息的增删改查功能")
public class AllergyController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AllergyService aergyService;


    /**
     * @return 用户的忌口信息
     */
    @Operation(summary = "根据 openId 获取用户最新忌口信息", description = "通过用户的微信 openId 获取用户最新忌口信息")
    @PostMapping("/get")
    @Log(title = "用户忌口-获取用户最新忌口信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    public Allergy getAllergyByOpenId() {
        String openId = (String)request.getAttribute("openId");
        return aergyService.getAllergyByOpenId(openId);
    }

    /**
     * @return 用户的忌口信息
     */
    @Operation(summary = "根据 openId 获取用户最新忌口信息", description = "通过用户的微信 openId 获取用户最新忌口信息")
    @PostMapping("/list")
    @Log(title = "用户忌口-获取用户最新忌口信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MOBILE)
    public List<Allergy> getAllergyListByOpenId() {
        String openId = (String)request.getAttribute("openId");
        return aergyService.getAllergyListByOpenId(openId);
    }

    /**
     * 创建或更新用户忌口信息
     * @param allergy 用户忌口信息实体
     * @return 创建或更新后的用户忌口信息
     */
    @Operation(summary = "创建用户忌口信息", description = "创建用户忌口信息")
    @Log(title = "用户忌口-创建用户忌口信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MOBILE)
    @PostMapping("save")
    public Allergy create(@Parameter(description = "用户忌口信息实体", required = true) @RequestBody Allergy allergy) {
        String openId = (String)request.getAttribute("openId");
        allergy.setOpenId(openId);
        return aergyService.create(allergy);
    }

    /**
     * 创建或更新用户忌口信息
     * @param allergy 用户忌口信息实体
     * @return 创建或更新后的用户忌口信息
     */
    @Operation(summary = "更新用户忌口信息", description = "更新用户忌口信息")
    @Log(title = "用户忌口-更新用户忌口信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MOBILE)
    @PostMapping("update")
    public Boolean update(@Parameter(description = "用户忌口信息实体", required = true) @RequestBody Allergy allergy) {
        String openId = (String)request.getAttribute("openId");
        allergy.setOpenId(openId);
        return aergyService.updateById(allergy);
    }


    /**
     * 根据 openId 删除用户忌口信息
     */
    @Operation(summary = "通过id删除对应的忌口信息", description = "通过id删除对应的忌口信息")
    @Log(title = "用户忌口-删除用户忌口信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MOBILE)
    @PostMapping("/delete")
    public void deleteAllergy(@RequestBody(required = false) Allergy allergy) {
        String openId = (String)request.getAttribute("openId");
        allergy.setOpenId(openId);
        aergyService.removeById(allergy);
    }
}