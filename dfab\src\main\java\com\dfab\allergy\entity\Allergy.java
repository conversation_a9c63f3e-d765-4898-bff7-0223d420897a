package com.dfab.allergy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.dfab.premiumMeal.entity.PremiumMeal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 忌口实体类，用于存储忌口信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("allergy")
@Schema(description = "忌口实体类")
@Builder
public class Allergy extends BaseEntity {

    /**
     * 忌口的唯一标识，系统自动分配的 ID。
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "忌口 ID", example = "1")
    private Long id;

    /**
     * 微信 openid
     */
    @Schema(description = "微信 openid", example = "wx1234567890", required = true)
    private String openId;

    @Schema(description = "入住会员ID", example = "1")
    private Long memberCheckinId;

    /**
     * 用户 ID，关联用户表。
     */
    @Schema(description = "用户 ID", example = "1")
    private Long userId;

    /**
     * 忌口描述。
     */
    @Schema(description = "忌口描述", example = "不要香菜", required = true)
    private String allergyDetail;

    /**
     * 用户姓名
     */
    @Schema(description = "姓名")
    private String userName;

    /**
     * 用户电话
     */
    @Schema(description = "用户电话")
    private String userPhone;

    /**
     * 房间号码
     */
    @Schema(description = "房间号码")
    private String roomNumber;

    /**
     * 是否废弃
     */
    @Schema(description = "是否废弃 1已废弃")
    private Integer isOut;

    /**
     * 投诉次数
     */
    @Schema(description = "投诉次数", example = "0")
    @TableField(exist = false)
    private Integer complaintCount;

    /**
     * 高档餐数量
     */
    @Schema(description = "高档餐数量", example = "2")
    @Excel(name = "高档餐数量")
    @TableField(exist = false)
    private Integer havePremiumMealNum;

    /**
     * 高档餐数量
     */
    @Schema(description = "已使用高档餐数量", example = "2")
    @Excel(name = "已使用高档餐数量")
    @TableField(exist = false)
    private Integer haveUsePremiumMealNum;

    @TableField(exist = false)
    private List<PremiumMeal> haveUsePremiumMealList;

    @Schema(description = "高档餐最后食用时间", example = "2024-01-01 12:00:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime consumeTime;

}