<template>
	<view class="baby-container">
		<custom-nav-bar title="宝宝护理"></custom-nav-bar>
		<view class="baby-content">
			<!-- 宝宝护理介绍 -->
			<view class="baby-intro">
				<view class="intro-text">
					<view class="intro-title">专业宝宝护理服务</view>
					<view class="intro-desc">
						我们提供专业的新生儿护理服务，由经验丰富的护理师24小时贴心照护，确保宝宝健康成长。从日常护理到专业指导，让新手父母安心无忧。
					</view>
				</view>
			</view>

			<!-- 宝宝护理图片展示 -->
			<view class="baby-section">
				<view class="baby-grid">
					<view
						class="baby-item"
						v-for="(item, index) in babyList"
						:key="index"
						@click="previewImage(item.mediumImage, item.largeImage)"
					>
						<image
							class="baby-image"
							:src="item.mediumImage"
							mode="aspectFill"
							:lazy-load="true"
						></image>
						<view class="baby-name">{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import imageApi from '@/config/api/image.js';
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 宝宝护理页面
	 * 展示月子中心的宝宝护理服务图片，从后端读取
	 */

	// 后端图片ID列表
	const imageIds = [
		'1927001407836155905',
		'1927001948804902914',
		'1927002067306573825'
	];

	// 宝宝护理服务名称列表
	const serviceNames = [
		' ',
		' ',
		' '
	];

	// 宝宝护理图片列表 - 使用封装的API生成
	const babyList = ref(imageApi.generateImageList(imageIds, serviceNames));

	// 图片预览功能 - 使用封装的API
	const previewImage = (currentImage, largeImage) => {
		imageApi.previewImage(currentImage, largeImage);
	};

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 专业宝宝护理',
			path: '/pages_business/pages/baby/baby',
			imageUrl: 'https://xiayuxinzhu.cn/api/files/previewResized/1926277348357857282/large'
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所专业宝宝护理，给宝宝最贴心的照顾',
			imageUrl: 'https://xiayuxinzhu.cn/api/files/previewResized/1926277348357857282/large'
		});
	}

	onMounted(() => {
		console.log('宝宝护理页面加载完成');
		console.log('宝宝护理图片列表:', babyList.value);
	});
</script>

<style>
	.baby-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.baby-content {
		padding: 20rpx;
	}

	.baby-intro {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.intro-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.intro-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		text-align: center;
	}

	.baby-section {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.baby-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.baby-item {
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		cursor: pointer;
	}

	.baby-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	}

	.baby-image {
		width: 100%;
		height: 400rpx;
		object-fit: cover;
	}

	.baby-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #8b5a2b;
		text-align: center;
		padding: 20rpx 15rpx 10rpx;
	}
</style>
