/*
 * uCharts®
 * 高性能跨平台图表库，支持H5、APP、小程序（微信/支付宝/百度/头条/QQ/360）、Vue、Taro等支持canvas的框架平台
 * Copyright (c) 2021 QIUN®秋云 https://www.ucharts.cn All rights reserved.
 * Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
 * 复制使用请保留本段注释，感谢支持开源！
 *
 * uCharts®官方网站
 * https://www.uCharts.cn
 *
 * 开源地址:
 * https://gitee.com/uCharts/uCharts
 *
 * uni-app插件市场地址：
 * http://ext.dcloud.net.cn/plugin?id=271
 *
 */

// 定义事件监听函数
function addEventListener(type, listener) {
  this.events = this.events || {};
  this.events[type] = this.events[type] || [];
  this.events[type].push(listener);
}

// 触发事件的函数
function trigger(type, ...params) {
  if (this.events && this.events[type]) {
    this.events[type].forEach(listener => {
      try {
        listener.apply(this, params);
      } catch (e) {
        console.error(e);
      }
    });
  }
}

// 简化版uCharts类
class uCharts {
  constructor(opts) {
    this.opts = opts;
    this.config = {
      yAxisWidth: 15,
      yAxisSplit: 5,
      xAxisHeight: 22,
      xAxisLineHeight: 22,
      legendHeight: 15,
      yAxisTitleWidth: 15,
      padding: [10, 10, 10, 10],
      pixelRatio: 1,
      rotate: false,
      columePadding: 3,
      fontSize: 13,
      fontColor: '#666666',
      dataPointShape: ['circle', 'circle', 'circle', 'circle'],
      color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
      linearColor: ['#0EE2F8', '#2BDCA8', '#FA7D8D', '#EB88E2', '#84E0BE', '#FBD54A'],
      pieChartLinePadding: 15,
      pieChartTextPadding: 5,
      xAxisTextPadding: 3,
      titleColor: '#333333',
      titleFontSize: 20,
      subtitleColor: '#999999',
      subtitleFontSize: 15,
      toolTipPadding: 3,
      toolTipBackground: '#000000',
      toolTipOpacity: 0.7,
      toolTipLineHeight: 20,
      radarLabelTextMargin: 13,
      gaugeLabelTextMargin: 13
    };
    this.context = opts.context;
    this.type = opts.type;
    this.categories = opts.categories || [];
    this.series = opts.series || [];
    this.width = opts.width || 375;
    this.height = opts.height || 250;
    this.background = opts.background || '#f8f5f2'; // 与首页背景色一致
    this.pixelRatio = opts.pixelRatio || 1;
    this.animation = opts.animation !== false;
    this.addEventListener = addEventListener;
    this.trigger = trigger;

    // 初始化图表
    this.init();
  }

  init() {
    // 绘制背景
    this.context.setFillStyle(this.background);
    this.context.fillRect(0, 0, this.width, this.height);

    // 根据图表类型绘制不同的图表
    switch(this.type) {
      case 'line':
        this.drawLineChart();
        break;
      case 'column':
        this.drawColumnChart();
        break;
      case 'pie':
        this.drawPieChart();
        break;
      case 'area':
        this.drawAreaChart();
        break;
      default:
        console.error('未支持的图表类型:', this.type);
    }

    // 触发渲染完成事件
    this.trigger('renderComplete');
  }

  drawLineChart() {
    const { width, height, context, series, categories } = this;
    // 根据屏幕宽度动态调整内边距
    const padding = width < 300 ? 20 : 30; // 小屏幕使用更小的内边距
    // 为Y轴标签预留更多空间
    const leftPadding = padding + 20; // 左侧多预留20px给Y轴标签
    const rightPadding = padding;
    const topPadding = padding;
    const bottomPadding = padding + 10; // 底部多预留10px给X轴标签

    const chartWidth = width - leftPadding - rightPadding;
    const chartHeight = height - topPadding - bottomPadding;

    // 找出数据中的最大值和最小值
    let maxValue = 0;
    let minValue = Infinity;

    series.forEach(item => {
      const data = item.data || [];
      const max = Math.max(...data);
      const min = Math.min(...data);
      if (max > maxValue) maxValue = max;
      if (min < minValue) minValue = min;
    });

    // 如果最小值等于最大值，调整一下范围
    if (maxValue === minValue) {
      maxValue += 10;
      if (minValue >= 10) {
        minValue -= 10;
      } else {
        minValue = 0;
      }
    }

    // 绘制坐标轴
    context.beginPath();
    context.setStrokeStyle('#CCCCCC');
    context.setLineWidth(1);

    // X轴
    context.moveTo(leftPadding, height - bottomPadding);
    context.lineTo(width - rightPadding, height - bottomPadding);

    // Y轴
    context.moveTo(leftPadding, topPadding);
    context.lineTo(leftPadding, height - bottomPadding);
    context.stroke();

    // 绘制X轴刻度和标签
    if (categories && categories.length > 0) {
      // 如果类别太多，只显示部分类别
      let displayCategories = categories;
      let displayIndices = [];

      if (categories.length > 6) {
        // 对于小屏幕，最多显示6个标签
        const step = Math.ceil(categories.length / 6);
        displayIndices = [];
        for (let i = 0; i < categories.length; i += step) {
          displayIndices.push(i);
        }
        // 确保最后一个类别显示
        if (!displayIndices.includes(categories.length - 1)) {
          displayIndices.push(categories.length - 1);
        }
      } else {
        // 显示所有类别
        displayIndices = categories.map((_, i) => i);
      }

      const xStep = chartWidth / (categories.length - 1);

      // 绘制所有刻度线
      categories.forEach((_, index) => {
        const x = leftPadding + index * xStep;

        // 刻度线
        context.beginPath();
        context.moveTo(x, height - bottomPadding);
        context.lineTo(x, height - bottomPadding + 5);
        context.stroke();
      });

      // 只绘制选定的标签
      displayIndices.forEach(index => {
        const category = categories[index];
        const x = leftPadding + index * xStep;

        // 标签
        context.setFontSize(10);
        context.setTextAlign('center');
        context.setTextBaseline('top');
        context.setFillStyle('#666666');
        context.fillText(category, x, height - bottomPadding + 7);
      });
    }

    // 绘制Y轴刻度和标签
    const yStep = chartHeight / 5;
    const valueStep = (maxValue - minValue) / 5;

    for (let i = 0; i <= 5; i++) {
      const y = height - bottomPadding - i * yStep;
      const value = minValue + i * valueStep;

      // 刻度线
      context.beginPath();
      context.moveTo(leftPadding, y);
      context.lineTo(leftPadding - 5, y);
      context.stroke();

      // 标签
      context.setFontSize(10);
      context.setTextAlign('right');
      context.setTextBaseline('middle');
      context.setFillStyle('#666666');

      // 根据数据类型格式化标签
      let formattedValue;
      if (value >= 10) {
        // 对于较大的数值，显示整数
        formattedValue = value.toFixed(0);
      } else if (value >= 1) {
        // 对于中等数值，显示一位小数
        formattedValue = value.toFixed(1);
      } else {
        // 对于小数值，显示两位小数
        formattedValue = value.toFixed(2);
      }

      context.fillText(formattedValue, leftPadding - 7, y);

      // 网格线
      context.beginPath();
      context.setLineDash([2, 2]);
      context.moveTo(leftPadding, y);
      context.lineTo(width - rightPadding, y);
      context.stroke();
      context.setLineDash([]);
    }

    // 绘制数据线
    series.forEach((serie, serieIndex) => {
      const data = serie.data || [];
      const color = serie.color || this.config.color[serieIndex % this.config.color.length];

      if (data.length > 0 && categories.length > 0) {
        const xStep = chartWidth / (categories.length - 1);

        // 绘制线条
        context.beginPath();
        context.setStrokeStyle(color);
        context.setLineWidth(2);

        data.forEach((value, index) => {
          if (index < categories.length) { // 确保数据点与类别对应
            const x = leftPadding + index * xStep;
            const y = height - bottomPadding - ((value - minValue) / (maxValue - minValue)) * chartHeight;

            if (index === 0) {
              context.moveTo(x, y);
            } else {
              context.lineTo(x, y);
            }
          }
        });

        context.stroke();

        // 绘制数据点
        data.forEach((value, index) => {
          if (index < categories.length) { // 确保数据点与类别对应
            const x = leftPadding + index * xStep;
            const y = height - bottomPadding - ((value - minValue) / (maxValue - minValue)) * chartHeight;

            context.beginPath();
            context.setFillStyle(color);
            context.arc(x, y, 3, 0, 2 * Math.PI);
            context.fill();
          }
        });
      }
    });

    // 绘制图例
    if (series.length > 0) {
      const legendY = topPadding / 2;
      let legendX = leftPadding;

      // 计算每个图例的宽度，确保适应小屏幕
      const legendWidth = width / series.length - 10;

      series.forEach((serie, index) => {
        const color = serie.color || this.config.color[index % this.config.color.length];
        const name = serie.name || `系列${index + 1}`;

        // 图例颜色块
        context.beginPath();
        context.setFillStyle(color);
        context.rect(legendX, legendY, 15, 10);
        context.fill();

        // 图例文字 - 根据屏幕宽度调整字体大小
        const fontSize = width < 300 ? 10 : 12;
        context.setFontSize(fontSize);
        context.setTextAlign('left');
        context.setTextBaseline('middle');
        context.setFillStyle('#666666');

        // 截断长文本
        let displayName = name;
        if (context.measureText(name).width > legendWidth - 30) {
          // 如果文本太长，截断并添加省略号
          let tempName = name;
          while (context.measureText(tempName + '...').width > legendWidth - 30 && tempName.length > 0) {
            tempName = tempName.substring(0, tempName.length - 1);
          }
          displayName = tempName + '...';
        }

        context.fillText(displayName, legendX + 20, legendY + 5);

        // 固定图例宽度，确保均匀分布
        legendX += legendWidth;
      });
    }

    // 完成绘制
    context.draw();
  }

  drawColumnChart() {
    // 简化版柱状图实现
    console.log('绘制柱状图');
  }

  drawPieChart() {
    // 简化版饼图实现
    console.log('绘制饼图');
  }

  drawAreaChart() {
    // 简化版面积图实现
    console.log('绘制面积图');
  }

  getCurrentDataIndex(e) {
    // 简化版获取点击位置对应的数据索引
    return -1;
  }

  touchStart(e) {
    // 触摸开始事件处理
  }

  touchMove(e) {
    // 触摸移动事件处理
  }

  touchEnd(e) {
    // 触摸结束事件处理
  }
}

export default uCharts;
