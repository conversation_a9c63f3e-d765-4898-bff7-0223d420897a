<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会员姓名" prop="memberName">
        <el-input
            v-model="queryParams.memberName"
            placeholder="请输入会员姓名"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
            @input="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input
            v-model="queryParams.phoneNumber"
            placeholder="请输入手机号码"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
            @input="handleQuery"
        />
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
            v-model="queryParams.roomNumber"
            placeholder="请输入房间号"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
            @input="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入住状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择入住状态" clearable style="width: 160px"
                   @change="handleQuery">
          <el-option label="待入住" value="3" />
          <el-option label="已入住" value="0" />
          <el-option label="已退住" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="入住日期" prop="checkinDate">
        <el-date-picker
            v-model="queryParams.checkinDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择入住日期"
            clearable
            @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['business:memberCheckin:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['business:memberCheckin:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['business:memberCheckin:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['business:memberCheckin:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberCheckinList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column label="会员姓名" align="center" prop="memberName" width="120" />
      <el-table-column label="手机号码" align="center" prop="phoneNumber" width="130" />
      <!-- 修改微信openId列 -->
      <!-- 原代码 -->
      <!-- <el-table-column label="微信openId" align="center" prop="openId" width="200" show-overflow-tooltip /> -->
      <!-- 新代码 -->
      <el-table-column label="套餐" align="center" width="150">
        <template #default="scope">
          <el-button type="text" @click="showPackageList(scope.row)">查看套餐</el-button>
        </template>
      </el-table-column>
      <el-table-column label="高档餐数量" align="center" prop="havePremiumMealNum" width="120" />
      <el-table-column label="已使用高档餐" align="center" width="120">
        <template #default="scope">
          <el-button
              v-if="scope.row.haveUsePremiumMealNum > 0"
              type="primary"
              link
              @click="showPremiumMealList(scope.row)"
              class="premium-meal-count-btn"
          >
            {{ scope.row.haveUsePremiumMealNum }}
          </el-button>
          <span v-else class="no-premium-meals">0</span>
        </template>
      </el-table-column>
      <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
      <el-table-column label="入住日期" align="center" prop="checkinDate" width="120" />
      <el-table-column label="退住日期" align="center" prop="checkoutDate" width="120" />
      <el-table-column label="入住状态" align="center" prop="status" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : scope.row.status === 1 ? 'info' : 'warning'">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="饮食禁忌" align="center" prop="allergyDetail" width="200">
        <template #default="scope">
          <div v-if="scope.row.allergyDetail">
            <el-tooltip
                :content="getPlainText(scope.row.allergyDetail)"
                placement="top"
                :disabled="getPlainText(scope.row.allergyDetail).length <= 20"
                effect="dark"
            >
              <span class="allergy-text ellipsis-text" v-html="scope.row.allergyDetail"></span>
            </el-tooltip>
          </div>
          <span v-else class="no-allergy">无</span>
        </template>
      </el-table-column>
      <el-table-column label="预产期" align="center" prop="expectedDeliveryDate" width="120" />
      <el-table-column label="入院待产日期" align="center" prop="hospitalAdmissionDate" width="130" />
      <el-table-column label="出院日期" align="center" prop="dischargeDate" width="120" />
      <el-table-column label="体重(kg)" align="center" prop="weight" width="100" />
      <el-table-column label="产检医院" align="center" prop="prenatalHospital" width="150" show-overflow-tooltip />
      <el-table-column label="推荐码" align="center" prop="referralCode" width="120" />
      <el-table-column label="中奖内容" align="center" prop="prizeContent" width="150" show-overflow-tooltip />
      <el-table-column label="紧急联系人" align="center" prop="emergencyContactName" width="120" />
      <el-table-column label="紧急联系电话" align="center" prop="emergencyContactPhone" width="130" />


      <el-table-column label="家属车辆" align="center" prop="carCount" width="100">
        <template #default="scope">
          <el-button
              v-if="scope.row.carCount > 0"
              type="primary"
              link
              @click="showCarDetails(scope.row)"
              class="car-count-btn"
          >
            {{ scope.row.carCount }}
          </el-button>
          <span v-else class="no-cars">0</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300" fixed="right">
        <template #default="scope">
          <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['business:memberCheckin:edit']"
          >修改</el-button>
          <el-button
              link
              type="warning"
              icon="House"
              @click="handleUpdateRoomNumber(scope.row)"
              v-hasPermi="['business:memberCheckin:edit']"
          >变更房号</el-button>
          <el-button
              link
              type="info"
              icon="Document"
              @click="showRoomChangeRecords(scope.row)"
              v-hasPermi="['business:memberCheckin:edit']"
          >换房记录</el-button>
          <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['business:memberCheckin:remove']"
          >删除</el-button>
          <el-button
              link
              type="success"
              icon="Food"
              @click="showPremiumMealList(scope.row)"
              v-hasPermi="['business:memberCheckin:edit']"
          >高档餐</el-button>
          <el-button
              link
              type="warning"
              icon="Warning"
              @click="handleAllergyInfo(scope.row)"
              v-hasPermi="['business:memberCheckin:edit']"
          >忌口信息</el-button>
        </template>
      </el-table-column>
    </el-table>

<!--    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />-->
    <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
      <el-pagination
          v-show="total > 0"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加或修改入住会员对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="memberCheckinRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员姓名" prop="memberName">
              <el-input v-model="form.memberName" placeholder="请输入会员姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入住日期" prop="checkinDate">
              <el-date-picker
                  v-model="form.checkinDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择入住日期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退住日期" prop="checkoutDate">
              <el-date-picker
                  v-model="form.checkoutDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择退住日期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="房间号" prop="roomNumber">
              <el-input
                v-model="form.roomNumber"
                placeholder="请输入房间号"
                :disabled="form.id != undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入住状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择入住状态" style="width: 100%">
                <el-option label="待入住" :value="3" />
                <el-option label="已入住" :value="0" />
                <el-option label="已退住" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急联系人" prop="emergencyContactName">
              <el-input v-model="form.emergencyContactName" placeholder="请输入紧急联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系电话" prop="emergencyContactPhone">
              <el-input v-model="form.emergencyContactPhone" placeholder="请输入紧急联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="预产期" prop="expectedDeliveryDate">
              <el-date-picker
                  v-model="form.expectedDeliveryDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择预产期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入院待产日期" prop="hospitalAdmissionDate">
              <el-date-picker
                  v-model="form.hospitalAdmissionDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择入院待产日期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="出院日期" prop="dischargeDate">
              <el-date-picker
                  v-model="form.dischargeDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择出院日期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="高档餐数量" prop="havePremiumMealNum">
              <el-input-number
                  v-model="form.havePremiumMealNum"
                  :min="0"
                  :max="999"
                  placeholder="请输入高档餐数量"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="体重(kg)" prop="weight">
              <el-input-number
                  v-model="form.weight"
                  :min="0"
                  :max="200"
                  :precision="1"
                  placeholder="请输入体重"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产检医院" prop="prenatalHospital">
              <el-input
                  v-model="form.prenatalHospital"
                  placeholder="请输入产检医院"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="推荐码" prop="referralCode">
              <el-input
                  v-model="form.referralCode"
                  placeholder="请输入推荐码"
                  :disabled="form.id != undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中奖内容" prop="prizeContent">
              <el-input
                  v-model="form.prizeContent"
                  placeholder="请输入中奖内容"
                  :disabled="form.id != undefined"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注信息" prop="remarks">
          <el-input
              v-model="form.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 车辆详情模态框 -->
    <el-dialog title="家属车辆信息" v-model="carDialogVisible" width="800px" append-to-body>
      <el-table :data="carList" v-loading="carLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="车主姓名" align="center" prop="ownerName" width="120" />
        <el-table-column label="车牌号码" align="center" prop="plateNumber" width="150" />
        <el-table-column label="联系电话" align="center" prop="phoneNumber" width="130" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="carDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加套餐列表对话框 -->
    <el-dialog title="会员套餐列表" v-model="packageDialogVisible" width="80%" append-to-body>
      <template #header>
        <div class="dialog-header" style="display: flex; justify-content: space-between; align-items: center;">
          <span>会员套餐列表</span>
          <div>
            <el-button type="primary" @click="showPaymentList">结算</el-button>
            <el-button v-if="packageList.length === 0" type="primary" @click="showDefaultPackageList">新增</el-button>
            <el-button v-else type="primary" @click="editPackage">{{!editPackageVisible?'修改':'取消'}}</el-button>
          </div>
        </div>
      </template>
      <el-table :data="packageList" v-loading="packageLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column label="单价" align="center" prop="unitPrice" />
        <el-table-column label="套餐数量" align="center">
          <template #default="scope">
            <el-input-number v-model="scope.row.packageQuantity" v-if="editPackageVisible" />
            <span v-else>{{scope.row.packageQuantity}}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际使用数量" align="center" prop="memberHaveUseNum">
          <template #default="scope">
            <el-button v-if="scope.row.memberHaveUseNum > 0" type="text" @click="showMaterialUse(scope.row)">{{scope.row.memberHaveUseNum}}</el-button>
            <span v-else>{{scope.row.memberHaveUseNum}}</span>
          </template>
        </el-table-column>
<!--        <el-table-column label="应补差价" align="center" prop="memberNeedPay" />-->
        <el-table-column label="操作" align="center" width="120" v-if="editPackageVisible ">
          <template #default="scope">
            <el-button 
              v-if="!scope.row.materialId"
              type="danger" 
              link 
              icon="Delete" 
              @click="removePackageItem(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="editPackageVisible" type="primary" @click="showMaterialSelection">添加物料</el-button>
          <el-button v-if="editPackageVisible" type="primary" @click="submitUpdateNewPackages">提交</el-button>
          <el-button @click="packageDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 物料使用详情 -->
    <el-dialog title="使用详情" v-model="materialUseVisible" width="80%" append-to-body>

      <el-table :data="materialUseList" v-loading="materialUseLoading">
        <el-table-column label="物料名称" prop="materialName" />
        <el-table-column label="规格型号" prop="specification" />
        <el-table-column label="类型" prop="recordType">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.recordType === 1">入库</el-tag>
            <el-tag type="warning" v-else-if="scope.row.recordType === 2">出库</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="quantity" />
        <el-table-column label="使用单位" prop="useUnit" />
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="操作人" prop="creator" />
        <el-table-column label="操作时间" prop="createTime" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialUseVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增默认套餐对话框 -->
    <el-dialog title="默认套餐列表" v-model="defaultPackageDialogVisible" width="80%" append-to-body>
      <el-table :data="defaultPackageList" v-loading="defaultPackageLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column label="单价" align="center" prop="unitPrice" />
        <el-table-column label="套餐数量" align="center">
          <template #default="scope">
            <el-input-number v-model="scope.row.packageQuantity"  />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
                type="danger"
                link
                icon="Delete"
                @click="removeDefaultPackageItem(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitNewPackages">提交</el-button>
          <el-button @click="defaultPackageDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
<el-dialog title="选择物料" v-model="materialSelectVisible" width="80%" append-to-body>
  <el-input v-model="materialSearch" placeholder="搜索物料名称" style="margin-bottom: 20px" @input="filterMaterialList" />
  <el-table 
    :data="filteredMaterialList"
    @selection-change="handleMaterialSelection"
    height="400"
    border
  >
    <el-table-column type="selection" width="55" />
    <el-table-column label="物料名称" prop="materialName" />
    <el-table-column label="规格型号" prop="specification" />
    <el-table-column label="单位" prop="unit" />
    <el-table-column label="单价" prop="unitPrice" />
    <el-table-column label="套餐数量" align="center" prop="packageQuantity"/>
  </el-table>
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="materialSelectVisible = false">取消</el-button>
      <el-button type="primary" @click="addSelectedMaterials">确定</el-button>
    </div>
  </template>
</el-dialog>

    <!-- 结算弹窗 -->
    <el-dialog :title="'结算列表 （合计金额: ' + truePay + '元）'" v-model="paymentDialogVisible" width="80%" append-to-body>
      <el-table :data="paymentList" v-loading="paymentLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column label="单价" align="center" prop="unitPrice" />
        <el-table-column label="套餐数量" align="center" prop="packageQuantity" />
        <el-table-column label="实际使用数量" align="center" prop="memberHaveUseNum">
          <template #default="scope">
            <el-button v-if="scope.row.memberHaveUseNum > 0" type="text" @click="showMaterialUse(scope.row)">{{scope.row.memberHaveUseNum}}</el-button>
            <span v-else>{{scope.row.memberHaveUseNum}}</span>
          </template>
        </el-table-column>
<!--        <el-table-column label="套餐成本" align="center" prop="packagePay" />-->
        <el-table-column label="实际金额" align="center" prop="truePay" />
<!--        <el-table-column label="应补差价" align="center" prop="memberNeedPay" />-->
      </el-table>
      <template #footer>
        <div class="dialog-footer">
<!--          <span style="margin-right: 20px">套餐成本: {{ packagePay }}</span>-->
          <span style="margin-right: 20px">合计金额: {{ truePay }}</span>
<!--          <span style="margin-right: 20px">结算金额: {{ needPay }}</span>-->
          <el-button @click="paymentDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更房号弹窗 -->
    <el-dialog title="变更房号" v-model="roomNumberDialogVisible" width="600px" append-to-body>
      <el-form ref="roomNumberRef" :model="roomNumberForm" :rules="roomNumberRules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员姓名">
              <el-input v-model="roomNumberForm.memberName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码">
              <el-input v-model="roomNumberForm.phoneNumber" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入住日期">
              <el-date-picker
                  v-model="roomNumberForm.checkinDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择入住日期"
                  style="width: 100%"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退住日期">
              <el-date-picker
                  v-model="roomNumberForm.checkoutDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择退住日期"
                  style="width: 100%"
                  disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="原房间号">
              <el-input v-model="roomNumberForm.oldRoomNumber" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入住状态">
              <el-select v-model="roomNumberForm.status" placeholder="请选择入住状态" style="width: 100%" disabled>
                <el-option label="待入住" :value="3" />
                <el-option label="已入住" :value="0" />
                <el-option label="已退住" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="新房间号" prop="roomNumber">
              <el-input v-model="roomNumberForm.roomNumber" placeholder="请输入新房间号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急联系人">
              <el-input v-model="roomNumberForm.emergencyContactName" placeholder="请输入紧急联系人姓名" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系电话">
              <el-input v-model="roomNumberForm.emergencyContactPhone" placeholder="请输入紧急联系人电话" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息">
              <el-input
                  v-model="roomNumberForm.remarks"
                  type="textarea"
                  placeholder="请输入备注信息"
                  :rows="3"
                  disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRoomNumberChange">确 定</el-button>
          <el-button @click="cancelRoomNumberChange">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 高档餐列表对话框 -->
    <el-dialog title="高档餐列表" v-model="premiumMealDialogVisible" width="80%" append-to-body>
      <template #header>
        <div class="dialog-header" style="display: flex; justify-content: space-between; align-items: center;">
          <span>高档餐列表</span>
          <div>
            <el-button type="primary" @click="showAddPremiumMealDialog">新增</el-button>
          </div>
        </div>
      </template>
      <el-table :data="premiumMealList" v-loading="premiumMealLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="高档餐详情" align="center" prop="premiumMealDetail" />
        <el-table-column label="食用日期" align="center" prop="consumeTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.consumeTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间段" align="center" prop="type" width="100">
          <template #default="scope">
            <span v-if="scope.row.type === 'breakfast'">早餐</span>
            <span v-else-if="scope.row.type === 'lunch'">午餐</span>
            <span v-else-if="scope.row.type === 'supper'">晚餐</span>
            <span v-else>{{ scope.row.type }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />

        <el-table-column label="份数" align="center" prop="quantity" width="100" />
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleEditPremiumMeal(scope.row)"
            >修改</el-button>
            <el-button
                type="danger"
                link
                icon="Delete"
                @click="handleDeletePremiumMeal(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="premiumMealDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/修改高档餐对话框 -->
    <el-dialog :title="premiumMealFormTitle" v-model="premiumMealFormVisible" width="800px" append-to-body>
      <el-form ref="premiumMealFormRef" :model="premiumMealForm" :rules="premiumMealRules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="高档餐详情" prop="premiumMealDetail">
              <el-select
                  v-model="premiumMealForm.premiumMealDetail"
                  placeholder="请选择高档餐详情"
                  style="width: 100%"
                  clearable
              >
                <el-option
                    v-for="dict in sys_premium_meal"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="食用时间" prop="consumeTime">
              <el-date-picker
                  v-model="premiumMealForm.consumeTime"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择食用日期"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间段" prop="type">
              <el-radio-group v-model="premiumMealForm.type" style="width: 100%">
                <el-radio value="breakfast">早餐</el-radio>
                <el-radio value="lunch">午餐</el-radio>
                <el-radio value="supper">晚餐</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="份数" prop="quantity">
              <el-input-number
                  v-model="premiumMealForm.quantity"
                  :min="1"
                  :max="99"
                  placeholder="请输入份数"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input
                  v-model="premiumMealForm.remark"
                  placeholder="请输入备注信息"
                  type="textarea"
                  :rows="2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPremiumMealForm">确 定</el-button>
          <el-button @click="cancelPremiumMealForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 换房记录弹窗 -->
    <el-dialog title="换房记录" v-model="roomChangeRecordsVisible" width="800px" append-to-body>
      <div class="room-change-header">
        <h4>{{ currentMemberInfo.memberName }} - {{ currentMemberInfo.phoneNumber }}</h4>
      </div>
      <el-table :data="roomChangeRecords" style="width: 100%" v-loading="roomChangeLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="操作详情" align="center" prop="detail" min-width="200" />
        <el-table-column label="操作类型" align="center" prop="type" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 'changeRoom'" type="warning">换房</el-tag>
            <el-tag v-else>{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="creator" width="120" />
      </el-table>
      <div v-if="roomChangeRecords.length === 0 && !roomChangeLoading" class="no-data">
        <el-empty description="暂无换房记录" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roomChangeRecordsVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 忌口信息弹窗 -->
    <el-dialog :title="allergyDialogTitle" v-model="allergyDialogVisible" width="500px" append-to-body>
      <el-form ref="allergyFormRef" :model="allergyForm" :rules="allergyRules" label-width="80px">
        <el-form-item label="会员姓名" prop="userName">
          <el-input v-model="allergyForm.userName" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="联系电话" prop="userPhone">
          <el-input v-model="allergyForm.userPhone" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="allergyForm.roomNumber" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="用餐忌口" prop="allergyDetail">
          <ColorTextEditor
            v-model="allergyForm.allergyDetail"
            placeholder="请输入用餐忌口信息"
            :height="120"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAllergyForm">取 消</el-button>
          <el-button type="primary" @click="submitAllergyForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { listMemberCheckin, getMemberCheckin, addMemberCheckin, updateMemberCheckin, updateRoomNumber, delMemberCheckin, exportMemberCheckin, getMemberPackage, getPackageDefaultList, updateMemberPackages,memberListPay } from "@/api/business/memberCheckin";
import { listRecord,listMaterial } from "@/api/business/material.js";
import { listPremiumMeal, addPremiumMeal, updatePremiumMeal, removePremiumMeal } from "@/api/business/premiumMeal.js";
import { getRoomChangeRecords } from "@/api/business/businessLog.js";
import { getAllergy, addAllergy, updateAllergy } from "@/api/business/allergy.js";
import request from '@/utils/request';
import {parseTime} from "@/utils/ruoyi.js";
import { useDict } from '@/utils/dict';
import ColorTextEditor from "@/components/ColorTextEditor/index.vue";

const { proxy } = getCurrentInstance();

// 获取字典数据
const { sys_premium_meal } = useDict('sys_premium_meal');

const memberCheckinList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 车辆详情相关变量
const carDialogVisible = ref(false);
const carList = ref([]);
const carLoading = ref(false);

const packageDialogVisible = ref(false);
const editPackageVisible = ref(false);
const packageList = ref([]);
const packageLoading = ref(false);

const materialUseVisible = ref(false);
const materialUseList = ref([]);
const materialUseLoading = ref(false);

const defaultPackageDialogVisible = ref(false);
const defaultPackageList = ref([]);
const defaultPackageLoading = ref(false);
let record ={};

// 变更房号相关变量
const roomNumberDialogVisible = ref(false);
const roomNumberForm = ref({
  id: undefined,
  memberName: '',
  phoneNumber: '',
  checkinDate: undefined,
  checkoutDate: undefined,
  roomNumber: '',
  oldRoomNumber: '',
  status: undefined,
  remarks: '',
  emergencyContactName: '',
  emergencyContactPhone: ''
});
const roomNumberRules = ref({
  roomNumber: [
    { required: true, message: "新房号不能为空", trigger: "blur" }
  ]
});

// 换房记录相关变量
const roomChangeRecordsVisible = ref(false);
const roomChangeRecords = ref([]);
const roomChangeLoading = ref(false);
const currentMemberInfo = ref({
  memberName: '',
  phoneNumber: ''
});



const data = reactive({
  form: {},
  rules: {
    memberName: [
      { required: true, message: "会员姓名不能为空", trigger: "blur" }
    ],
    phoneNumber: [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
      { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    checkinDate: [
      { required: true, message: "入住日期不能为空", trigger: "blur" }
    ],
    havePremiumMealNum: [
      { required: true, message: "高档餐数量不能为空", trigger: "blur" },
      { type: "number", min: 0, max: 999, message: "高档餐数量必须在0-999之间", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  memberName: undefined,
  phoneNumber: undefined,
  roomNumber: undefined,
  status: '0',
  checkinDate: undefined
})
/** 处理页面大小变化 */
function handleSizeChange(val) {
  queryParams.pageSize = val
  queryParams.pageNum = 1
  getList()
}

// 在methods中添加
function removeDefaultPackageItem(item) {
  defaultPackageList.value = defaultPackageList.value.filter(p => p !== item);
}

/** 处理当前页变化 */
function handleCurrentChange(val) {
  queryParams.pageNum = val
  getList()
}
function removePackageItem(item) {
  packageList.value = packageList.value.filter(p => p !== item);
}
const filteredMaterialList = ref([]);

function filterMaterialList() {
  if (!materialSearch.value) {
    filteredMaterialList.value = materialList.value;
    return;
  }
  filteredMaterialList.value = materialList.value.filter(item => 
    item.materialName.includes(materialSearch.value)
  );
}

// 在获取物料列表后初始化过滤列表
function showMaterialSelection() {
  materialSelectVisible.value = true;
  listMaterial().then(response => {
    materialList.value = response || [];
    filteredMaterialList.value = materialList.value;
  });
}

// 在script部分添加相关变量和方法
const paymentDialogVisible = ref(false);
const paymentList = ref([]);
const paymentLoading = ref(false);
const needPay = ref(0);
const truePay = ref(0);
const packagePay = ref(0);
const showPaymentList = () => {
  paymentLoading.value = true;
  memberListPay({
    memberId: record.id,
  }).then(response => {
    paymentList.value = response.packages;
    needPay.value = response.needPay;
    truePay.value = response.truePay;
    packagePay.value = response.packagePay;
    paymentDialogVisible.value = true;
    paymentLoading.value = false;
  }).catch(() => {
    paymentLoading.value = false;
  });
};

const materialSelectVisible = ref(false);
const materialSearch = ref('');
const materialList = ref([]);
const selectedMaterials = ref([]);

// 高档餐相关变量
const premiumMealDialogVisible = ref(false);
const premiumMealList = ref([]);
const premiumMealLoading = ref(false);
const premiumMealFormVisible = ref(false);
const premiumMealFormTitle = ref('');
const premiumMealForm = ref({
  id: undefined,
  memberCheckinId: undefined,
  premiumMealDetail: '',
  consumeTime: undefined,
  type: '',
  quantity: 1,
  remark: ''
});
const premiumMealRules = ref({
  premiumMealDetail: [
    { required: true, message: "高档餐详情不能为空", trigger: "blur" }
  ],
  consumeTime: [
    { required: true, message: "食用时间不能为空", trigger: "blur" }
  ],
  type: [
    { required: true, message: "时间段不能为空", trigger: "change" }
  ],
  quantity: [
    { required: true, message: "份数不能为空", trigger: "blur" }
  ]
});
let currentMemberRecord = {};

// 忌口信息相关变量
const allergyDialogVisible = ref(false);
const allergyDialogTitle = ref('');
const allergyForm = ref({
  id: undefined,
  memberCheckinId: undefined,
  userName: '',
  userPhone: '',
  roomNumber: '',
  allergyDetail: ''
});
const allergyRules = ref({
  allergyDetail: [
    { required: true, message: "用餐忌口不能为空", trigger: "blur" }
  ]
});

function handleMaterialSelection(selection) {
  selectedMaterials.value = selection;
}

function addSelectedMaterials() {
  // 将选中的物料添加到套餐列表
  packageList.value = [...packageList.value, ...selectedMaterials.value.map(m => ({
    ...m,
    packageQuantity: 1,
    memberHaveUseNum: 0,
    memberNeedPay: 0
  }))];
  materialSelectVisible.value = false;
}

/** 查询入住会员列表 */
function getList() {
  loading.value = true;
  listMemberCheckin(queryParams).then(response => {
    console.log('API响应:', response);
    console.log('会员列表数据结构:', response.rows?.[0]); // 查看第一个会员的数据结构
    memberCheckinList.value = response.rows || [];
    total.value = Number(response.total || 0);
    loading.value = false;
  }).catch(error => {
    console.error('查询失败:', error);
    loading.value = false;
    proxy.$modal.msgError("查询失败");
  });
}

// 在script部分添加editPackage方法
function editPackage() {
  //取消
  if(editPackageVisible.value){
    showPackageList({
      id: record.id,
    });
  }
  // 实现修改逻辑
  editPackageVisible.value = !editPackageVisible.value
}

// 入住状态文本映射
function getStatusText(status) {
  const statusMap = {
    0: '已入住',
    1: '已退住',
    3: '待入住'
  };
  return statusMap[status] || '未知';
}
// 显示默认套餐列表
function showDefaultPackageList() {
  defaultPackageDialogVisible.value = true;
  defaultPackageLoading.value = true;
  defaultPackageList.value = [];

  // 调用 API 获取默认套餐信息
  getPackageDefaultList().then(response => {
    defaultPackageList.value = response || [];
    defaultPackageLoading.value = false;
  }).catch(error => {
    console.error('获取默认套餐信息失败:', error);
    proxy.$modal.msgError("获取默认套餐信息失败");
    defaultPackageLoading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  data.form = {
    id: undefined,
    memberName: undefined,
    phoneNumber: undefined,
    checkinDate: undefined,
    checkoutDate: undefined,
    roomNumber: undefined,
    status: 0,
    remarks: undefined,
    emergencyContactName: undefined,
    emergencyContactPhone: undefined,
    expectedDeliveryDate: undefined,
    hospitalAdmissionDate: undefined,
    dischargeDate: undefined,
    havePremiumMealNum: 0,
    weight: undefined,
    prenatalHospital: undefined,
    referralCode: undefined,
    prizeContent: undefined
  };
  proxy.resetForm("memberCheckinRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.status = undefined
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加入住会员";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getMemberCheckin(id).then(response => {
    data.form = response.data;
    open.value = true;
    title.value = "修改入住会员";
  });
}

/** 变更房号按钮操作 */
function handleUpdateRoomNumber(row) {
  roomNumberForm.value = {
    id: row.id,
    memberName: row.memberName,
    phoneNumber: row.phoneNumber,
    checkinDate: row.checkinDate,
    checkoutDate: row.checkoutDate,
    roomNumber: '', // 清空房号，让用户输入新房号
    status: row.status,
    remarks: row.remarks,
    emergencyContactName: row.emergencyContactName,
    emergencyContactPhone: row.emergencyContactPhone,
    oldRoomNumber: row.roomNumber // 保存原房号用于显示
  };
  roomNumberDialogVisible.value = true;
}

/** 提交房号变更 */
function submitRoomNumberChange() {
  proxy.$refs["roomNumberRef"].validate(valid => {
    if (valid) {
      const data = {
        id: roomNumberForm.value.id,
        roomNumber: roomNumberForm.value.roomNumber.trim()
      };
      console.log('提交房号变更数据:', data);
      console.log('原房号:', roomNumberForm.value.oldRoomNumber);
      console.log('新房号:', roomNumberForm.value.roomNumber);

      updateRoomNumber(data).then(response => {
        if (response === true || response.data === true) {
          proxy.$modal.msgSuccess("房号变更成功");
          roomNumberDialogVisible.value = false;
          getList();
        } else {
          proxy.$modal.msgError("房号变更失败");
        }
      }).catch(error => {
        console.error('房号变更失败:', error);
        proxy.$modal.msgError(error.response?.data?.msg || error.message || "房号变更失败");
      });
    }
  });
}

/** 取消房号变更 */
function cancelRoomNumberChange() {
  roomNumberDialogVisible.value = false;
  roomNumberForm.value = {
    id: undefined,
    memberName: '',
    phoneNumber: '',
    checkinDate: undefined,
    checkoutDate: undefined,
    roomNumber: '',
    oldRoomNumber: '',
    status: undefined,
    remarks: '',
    emergencyContactName: '',
    emergencyContactPhone: ''
  };
  proxy.resetForm("roomNumberRef");
}

/** 显示换房记录 */
function showRoomChangeRecords(row) {
  currentMemberInfo.value = {
    memberName: row.memberName,
    phoneNumber: row.phoneNumber
  };
  roomChangeRecordsVisible.value = true;
  roomChangeLoading.value = true;
  roomChangeRecords.value = [];

  // 调用API获取换房记录
  getRoomChangeRecords(row.id).then(response => {
    roomChangeRecords.value = response || [];
    roomChangeLoading.value = false;
  }).catch(error => {
    console.error('获取换房记录失败:', error);
    proxy.$modal.msgError("获取换房记录失败");
    roomChangeLoading.value = false;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["memberCheckinRef"].validate(valid => {
    if (valid) {
      if (data.form.id != undefined) {
        updateMemberCheckin(data.form).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMemberCheckin(data.form).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const checkinIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除入住会员编号为"' + checkinIds + '"的数据项?').then(function() {
    return delMemberCheckin(checkinIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('admin/memberCheckin/export', {
    ...queryParams
  }, `入住会员_${new Date().getTime()}.xlsx`);
}

/** 显示车辆详情 */
function showCarDetails(row) {
  carDialogVisible.value = true;
  carLoading.value = true;
  carList.value = [];

  // 调用API获取车辆信息
  request({
    url: '/admin/memberCheckin/cars',
    method: 'post',
    data: {
      phoneNumber: row.phoneNumber,
      openId: row.openId
    }
  }).then(response => {
    if (response.code === 200) {
      carList.value = response.data || [];
    } else {
      proxy.$modal.msgError(response.msg || "获取车辆信息失败");
    }
    carLoading.value = false;
  }).catch(error => {
    console.error('获取车辆信息失败:', error);
    proxy.$modal.msgError("获取车辆信息失败");
    carLoading.value = false;
  });
}


// 提交新套餐
function submitNewPackages() {
  const memberId = record.id// 从当前选中行获取会员ID
  const newPackages = defaultPackageList.value
  console.log(newPackages)
  console.log(memberId)

  updateMemberPackages({
    memberId: memberId,
    packages: newPackages
  }).then(response => {
    proxy.$modal.msgSuccess('套餐添加成功');
    defaultPackageDialogVisible.value = false;
    // 重新获取会员套餐列表
    showPackageList({
      id: record.id,
    });
  }).catch(error => {
    console.error('提交新套餐失败:', error);
    proxy.$modal.msgError('提交新套餐失败');
  });
}

// 提交新套餐
function submitUpdateNewPackages() {
  const memberId = record.id// 从当前选中行获取会员ID
  const newPackages = packageList.value
  console.log(newPackages)
  console.log(memberId)

  updateMemberPackages({
    memberId: memberId,
    packages: newPackages
  }).then(response => {
    proxy.$modal.msgSuccess('套餐修改成功');
    defaultPackageDialogVisible.value = false;
    editPackageVisible.value = false;
    // 重新获取会员套餐列表
    showPackageList({
      id: record.id,
    });
  }).catch(error => {
    console.error('套餐修改成功失败:', error);
    proxy.$modal.msgError('套餐修改成功失败');
  });
}

// 新增查看套餐列表方法（如果之前未添加）
function showPackageList(row) {
  record = row
  packageDialogVisible.value = true;
  packageLoading.value = true;
  packageList.value = [];

  // 调用API获取套餐信息
  getMemberPackage(
      {
        memberId: row.id,
      }).then(response => {

    packageList.value = response || [];
    packageLoading.value = false;
  }).catch(error => {
    console.error('获取套餐信息失败:', error);
    proxy.$modal.msgError("获取套餐信息失败");
    packageLoading.value = false;
  });
}
// 新增查看套餐列表方法（如果之前未添加）
function showMaterialUse(row) {
  materialUseVisible.value = true;
  materialUseLoading.value = true;
  materialUseList.value = [];

  // 调用API获取套餐信息
  listRecord(
      {
        materialId: row.materialId ?row.materialId :row.id,
        memberCheckinId: record.id
      }).then(response => {

    materialUseList.value = response || [];
    materialUseLoading.value = false;
  }).catch(error => {
    console.error('获取使用详情失败:', error);
    proxy.$modal.msgError("获取使用详情失败");
    materialUseLoading.value = false;
  });
}

// 高档餐相关方法
function showPremiumMealList(row) {
  currentMemberRecord = row;
  premiumMealDialogVisible.value = true;
  premiumMealLoading.value = true;
  premiumMealList.value = [];

  // 调用API获取高档餐信息
  listPremiumMeal({
    memberCheckinId: row.id
  }).then(response => {
    premiumMealList.value = response || [];
    premiumMealLoading.value = false;
  }).catch(error => {
    console.error('获取高档餐信息失败:', error);
    proxy.$modal.msgError("获取高档餐信息失败");
    premiumMealLoading.value = false;
  });
}

function showAddPremiumMealDialog() {
  resetPremiumMealForm();
  premiumMealFormTitle.value = '新增高档餐';
  premiumMealForm.value.memberCheckinId = currentMemberRecord.id;
  premiumMealFormVisible.value = true;
}

function handleEditPremiumMeal(row) {
  resetPremiumMealForm();
  premiumMealFormTitle.value = '修改高档餐';
  premiumMealForm.value = {
    id: row.id,
    memberCheckinId: row.memberCheckinId,
    premiumMealDetail: row.premiumMealDetail,
    consumeTime: row.consumeTime,
    type: row.type,
    quantity: row.quantity,
    remark: row.remark
  };
  premiumMealFormVisible.value = true;
}

function handleDeletePremiumMeal(row) {
  proxy.$modal.confirm('是否确认删除该高档餐记录?').then(function() {
    return removePremiumMeal(row.id);
  }).then(() => {
    showPremiumMealList(currentMemberRecord);
    getList(); // 刷新外面的会员列表
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function submitPremiumMealForm() {
  proxy.$refs["premiumMealFormRef"].validate(valid => {
    if (valid) {
      if (premiumMealForm.value.id != undefined) {
        updatePremiumMeal(premiumMealForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          premiumMealFormVisible.value = false;
          showPremiumMealList(currentMemberRecord);
          getList(); // 刷新外面的会员列表
        });
      } else {
        addPremiumMeal(premiumMealForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          premiumMealFormVisible.value = false;
          showPremiumMealList(currentMemberRecord);
          getList(); // 刷新外面的会员列表
        });
      }
    }
  });
}

function cancelPremiumMealForm() {
  premiumMealFormVisible.value = false;
  resetPremiumMealForm();
}

function resetPremiumMealForm() {
  premiumMealForm.value = {
    id: undefined,
    memberCheckinId: undefined,
    premiumMealDetail: '',
    consumeTime: undefined,
    type: '',
    quantity: 1,
    remark: ''
  };
  proxy.resetForm("premiumMealFormRef");
}

// 忌口信息相关方法
function handleAllergyInfo(row) {
  console.log('点击忌口信息按钮，行数据:', row); // 调试信息

  allergyDialogTitle.value = '忌口信息';

  // 直接从会员数据中获取忌口信息，如果存在则为编辑模式，否则为新增模式
  const existingAllergyDetail = row.allergyDetail || '';

  allergyForm.value = {
    id: existingAllergyDetail ? row.allergyId : undefined, // 如果有忌口信息，使用allergyId
    memberCheckinId: row.id,
    userName: row.memberName,
    userPhone: row.phoneNumber,
    roomNumber: row.roomNumber,
    allergyDetail: existingAllergyDetail
  };

  console.log('填充后的表单数据:', allergyForm.value); // 调试信息
  console.log('忌口详情:', existingAllergyDetail); // 调试信息

  // 总是通过API查询最新的忌口信息
  console.log('通过API查询忌口信息'); // 调试信息

  getAllergy({ memberCheckinId: row.id }).then(response => {
    console.log('getAllergy API 响应:', response); // 调试信息
    console.log('API响应类型:', typeof response); // 调试信息
    console.log('API响应是否为数组:', Array.isArray(response)); // 调试信息

    // 处理不同的响应格式
    let allergyData = null;

    if (response) {
      if (Array.isArray(response) && response.length > 0) {
        // 如果返回的是数组，取第一个
        allergyData = response[0];
      } else if (response.rows && response.rows.length > 0) {
        // 如果返回的是分页格式
        allergyData = response.rows[0];
      } else if (response.id) {
        // 如果返回的是单个对象
        allergyData = response;
      }
    }

    if (allergyData && allergyData.id) {
      // 如果找到现有记录，填充数据
      allergyForm.value.id = allergyData.id;
      allergyForm.value.allergyDetail = allergyData.allergyDetail || '';
      console.log('API查询到忌口记录，已填充数据:', allergyData); // 调试信息
    } else {
      console.log('API未查询到忌口记录，新增模式'); // 调试信息
    }

    allergyDialogVisible.value = true;
  }).catch(error => {
    console.log('API查询忌口信息失败:', error); // 调试信息
    allergyDialogVisible.value = true;
  });
}

function submitAllergyForm() {
  proxy.$refs.allergyFormRef.validate(valid => {
    if (valid) {
      const formData = { ...allergyForm.value };

      if (formData.id) {
        // 更新忌口信息
        updateAllergy(formData).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          allergyDialogVisible.value = false;
          getList();
        });
      } else {
        // 新增忌口信息
        addAllergy(formData).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          allergyDialogVisible.value = false;
          getList();
        });
      }
    }
  });
}

function cancelAllergyForm() {
  allergyDialogVisible.value = false;
  resetAllergyForm();
}

function resetAllergyForm() {
  allergyForm.value = {
    id: undefined,
    memberCheckinId: undefined,
    userName: '',
    userPhone: '',
    roomNumber: '',
    allergyDetail: ''
  };
  proxy.$refs.allergyFormRef?.resetFields();
}

// 提取HTML中的纯文本内容
const getPlainText = (htmlString) => {
  if (!htmlString) return ''
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlString
  return tempDiv.textContent || tempDiv.innerText || ''
}

onMounted(() => {
  getList();
});

</script>

<style scoped>
.allergy-text {
  /* color: #e6a23c; */
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
}

.ellipsis-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.no-allergy {
  color: #909399;
  font-style: italic;
}

.car-count-btn {
  font-weight: bold;
  font-size: 14px;
}

.no-cars {
  color: #909399;
  font-style: italic;
}

.premium-meal-count-btn {
  font-weight: bold;
  font-size: 14px;
  color: #409eff;
}

.no-premium-meals {
  color: #909399;
  font-style: italic;
}

.room-change-header {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.room-change-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}


</style>


