<template>
	<view class="handicraft-container">
		<custom-nav-bar title="手工课安排"></custom-nav-bar>
		
		<view class="handicraft-content">
			<!-- 手工课介绍 -->
			<view class="intro-section">
				<view class="intro-title">手工课程</view>
				<view class="intro-desc">
					我们为住院期间的妈妈们精心安排了丰富的手工课程，包括手工编织、手工制作、绘画课程等，
					让您在休养期间也能享受创作的乐趣，培养兴趣爱好。
				</view>
			</view>

			<!-- 筛选区域 -->
			<view class="filter-section">
				<view class="filter-item">
					<picker 
						:range="typeOptions" 
						:value="filterData.typeIndex" 
						@change="onTypeChange"
					>
						<view class="filter-picker">
							{{ typeOptions[filterData.typeIndex] }}
							<text class="filter-arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<view class="filter-item">
					<picker 
						:range="statusOptions" 
						:value="filterData.statusIndex" 
						@change="onStatusChange"
					>
						<view class="filter-picker">
							{{ statusOptions[filterData.statusIndex] }}
							<text class="filter-arrow">▼</text>
						</view>
					</picker>
				</view>
			</view>

			<!-- 手工课列表 -->
			<view class="class-list" v-if="classList.length > 0">
				<view 
					class="class-item" 
					v-for="(item, index) in classList" 
					:key="item.id"
					@click="viewClassDetail(item)"
				>
					<view class="class-header">
						<view class="class-name">{{ item.className }}</view>
						<view class="class-status" :class="getStatusClass(item.status)">
							{{ getStatusText(item.status) }}
						</view>
					</view>
					
					<view class="class-info">
						<view class="info-row">
							<text class="info-label">课程类型：</text>
							<text class="info-value">{{ getTypeText(item.classType) }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">上课时间：</text>
							<text class="info-value">{{ formatDateTime(item.classTime) }}</text>
						</view>
						<view class="info-row" v-if="item.duration">
							<text class="info-label">课程时长：</text>
							<text class="info-value">{{ item.duration }}分钟</text>
						</view>
						<view class="info-row" v-if="item.location">
							<text class="info-label">上课地点：</text>
							<text class="info-value">{{ item.location }}</text>
						</view>
						<view class="info-row" v-if="item.teacher">
							<text class="info-label">授课老师：</text>
							<text class="info-value">{{ item.teacher }}</text>
						</view>
						<view class="info-row" v-if="item.maxParticipants">
							<text class="info-label">限制人数：</text>
							<text class="info-value">{{ item.currentParticipants || 0 }}/{{ item.maxParticipants }}人</text>
						</view>
					</view>

					<view class="class-description" v-if="item.description">
						{{ item.description }}
					</view>

					<view class="class-materials" v-if="item.materials">
						<text class="materials-label">所需材料：</text>
						<text class="materials-text">{{ item.materials }}</text>
					</view>

					<view class="class-actions" v-if="item.status === 0">
						<button 
							class="join-btn" 
							@click.stop="joinClass(item)"
							:disabled="isClassFull(item)"
						>
							{{ isClassFull(item) ? '人数已满' : '报名参加' }}
						</button>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-else-if="!loading">
				<image class="empty-image" src="/static/icon/empty.png" mode="aspectFit"></image>
				<view class="empty-text">暂无手工课安排</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getHandicraftClassList, joinHandicraftClass } from '@/config/api/handicraft.js';
import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

// 数据状态
const classList = ref([]);
const loading = ref(false);

// 筛选数据
const filterData = ref({
	typeIndex: 0,
	classType: null,
	statusIndex: 0,
	status: null
});

// 筛选选项
const typeOptions = ref(['全部类型', '手工编织', '手工制作', '绘画课程', '其他手工']);
const statusOptions = ref(['全部状态', '待开始', '进行中', '已完成', '已取消']);

// 课程类型映射
const typeMap = {
	1: '手工编织',
	2: '手工制作', 
	3: '绘画课程',
	4: '其他手工'
};

// 状态映射
const statusMap = {
	0: '待开始',
	1: '进行中',
	2: '已完成',
	3: '已取消'
};

// 获取类型文本
const getTypeText = (type) => {
	return typeMap[type] || '未知类型';
};

// 获取状态文本
const getStatusText = (status) => {
	return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		0: 'status-pending',
		1: 'status-ongoing',
		2: 'status-completed',
		3: 'status-cancelled'
	};
	return classMap[status] || '';
};

// 判断课程是否已满
const isClassFull = (classItem) => {
	if (!classItem.maxParticipants) return false;
	return (classItem.currentParticipants || 0) >= classItem.maxParticipants;
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
	if (!dateTimeStr) return '';
	const date = new Date(dateTimeStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');
	return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 类型筛选
const onTypeChange = (e) => {
	filterData.value.typeIndex = e.detail.value;
	filterData.value.classType = e.detail.value === 0 ? null : e.detail.value;
	loadClassList();
};

// 状态筛选
const onStatusChange = (e) => {
	filterData.value.statusIndex = e.detail.value;
	filterData.value.status = e.detail.value === 0 ? null : (e.detail.value - 1);
	loadClassList();
};

// 加载手工课列表
const loadClassList = async () => {
	loading.value = true;
	
	try {
		const params = {
			pageNum: 1,
			pageSize: 50
		};
		
		// 添加筛选条件
		if (filterData.value.classType !== null) {
			params.classType = filterData.value.classType;
		}
		if (filterData.value.status !== null) {
			params.status = filterData.value.status;
		}

		console.log('加载手工课列表，参数:', params);

		const response = await getHandicraftClassList(params);
		
		if (response && response.list) {
			classList.value = response.list;
		} else if (response && Array.isArray(response)) {
			classList.value = response;
		} else {
			classList.value = [];
		}

		console.log('手工课列表加载成功:', classList.value);

	} catch (error) {
		console.error('加载手工课列表失败:', error);
		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none'
		});
		classList.value = [];
	} finally {
		loading.value = false;
	}
};

// 查看课程详情
const viewClassDetail = (classItem) => {
	// 显示课程详情弹窗或跳转到详情页
	uni.showModal({
		title: classItem.className,
		content: `课程类型：${getTypeText(classItem.classType)}\n上课时间：${formatDateTime(classItem.classTime)}\n授课老师：${classItem.teacher || '待安排'}\n课程描述：${classItem.description || '暂无描述'}`,
		showCancel: false,
		confirmText: '知道了'
	});
};

// 报名参加手工课
const joinClass = async (classItem) => {
	try {
		uni.showLoading({
			title: '报名中...'
		});

		const params = {
			id: classItem.id
		};

		console.log('报名参加手工课:', params);

		await joinHandicraftClass(params);

		uni.showToast({
			title: '报名成功',
			icon: 'success'
		});

		// 重新加载列表
		loadClassList();

	} catch (error) {
		console.error('报名失败:', error);
		uni.showToast({
			title: error.message || '报名失败，请重试',
			icon: 'none'
		});
	} finally {
		uni.hideLoading();
	}
};

// 分享给朋友
const onShareAppMessage = () => {
	return getShareAppMessageConfig({
		title: '东方爱堡月子会所 - 手工课安排',
		path: '/pages_business/pages/handicraft/list',
	});
}

// 分享到朋友圈
const onShareTimeline = () => {
	return getShareTimelineConfig({
		title: '东方爱堡月子会所手工课程，丰富您的休养时光',
	});
}

onMounted(() => {
	console.log('手工课列表页面加载完成');
	loadClassList();
});
</script>

<style scoped>
.handicraft-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.handicraft-content {
	padding: 20rpx;
}

.intro-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.intro-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.filter-section {
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.filter-item {
	flex: 1;
}

.filter-picker {
	background: white;
	height: 70rpx;
	border-radius: 12rpx;
	padding: 0 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 28rpx;
	color: #333;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-arrow {
	color: #999;
	font-size: 24rpx;
}

.class-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.class-item {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.class-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.class-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.class-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: white;
}

.status-pending {
	background: #FF9800;
}

.status-ongoing {
	background: #4CAF50;
}

.status-completed {
	background: #9E9E9E;
}

.status-cancelled {
	background: #F44336;
}

.class-info {
	margin-bottom: 20rpx;
}

.info-row {
	display: flex;
	margin-bottom: 10rpx;
	font-size: 26rpx;
}

.info-label {
	color: #666;
	width: 160rpx;
}

.info-value {
	color: #333;
	flex: 1;
}

.class-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	padding: 20rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
}

.class-materials {
	font-size: 26rpx;
	margin-bottom: 20rpx;
}

.materials-label {
	color: #666;
}

.materials-text {
	color: #333;
}

.class-actions {
	display: flex;
	justify-content: flex-end;
}

.join-btn {
	background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
}

.join-btn:disabled {
	background: #ccc;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 20rpx;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.loading-state {
	display: flex;
	justify-content: center;
	padding: 50rpx;
	font-size: 28rpx;
	color: #999;
}
</style>
