package com.dfab.appUser.service;

/*import com.dfab.appUser.dto.LoginRequest;
import com.dfab.appUser.dto.LoginResponse;*/
import com.dfab.appUser.entity.AppUser;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AppUserService extends IService<AppUser> {
    /**
     * 根据openId获取用户
     * @param openId 用户openId
     * @return 用户信息
     */
    AppUser getByOpenId(String openId);

    /**
     * 创建或更新用户
     * @param appUser 用户信息
     * @return 用户信息
     */
    AppUser createOrUpdateAppUser(AppUser appUser);

    /**
     * 根据手机号获取用户
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    AppUser getByPhoneNumber(String phoneNumber);

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
//    LoginResponse login(LoginRequest loginRequest);

    /**
     * 根据token获取用户信息
     * @param token 用户token
     * @return 用户信息
     */
//    AppUser getUserInfoByToken(String token);
}
