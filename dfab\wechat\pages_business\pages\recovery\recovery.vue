<template>
	<view class="recovery-container">
		<custom-nav-bar title="产康护理"></custom-nav-bar>
		<view class="recovery-content">
			<!-- 产康护理介绍 -->
			<view class="recovery-intro">
				<view class="intro-text">
					<view class="intro-title">专业产康护理服务</view>
					<view class="intro-desc">
						我们提供专业的产后康复护理服务，帮助产妇恢复身体机能，缓解产后不适，促进身心健康。专业团队为您量身定制康复方案。
					</view>
				</view>
			</view>

			<!-- 产康护理图片展示 -->
			<view class="recovery-section">
				<view class="recovery-grid">
					<view
						class="recovery-item"
						v-for="(item, index) in recoveryList"
						:key="index"
						@click="previewImage(item.mediumImage, item.largeImage)"
					>
						<image
							class="recovery-image"
							:src="item.mediumImage"
							mode="aspectFill"
							:lazy-load="true"
						></image>
						<view class="recovery-name">{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import imageApi from '@/config/api/image.js';
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 产康护理页面
	 * 展示月子中心的产康护理服务图片，从后端读取
	 */

	// 后端图片ID列表
	const imageIds = [
		'1927002930842554370',
		'1927002988845584386',
		'1927003123159781377',
		'1927003169917882369',
		'1927003272011436034',
		'1927003220375359489',
		'1927003320585670657'
	];

	// 产康护理服务名称列表
	const serviceNames = [
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' '
	];

	// 产康护理图片列表 - 使用封装的API生成
	const recoveryList = ref(imageApi.generateImageList(imageIds, serviceNames));

	// 图片预览功能 - 使用封装的API
	const previewImage = (currentImage, largeImage) => {
		imageApi.previewImage(currentImage, largeImage);
	};

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 专业产康护理',
			path: '/pages_business/pages/recovery/recovery',
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所专业产康护理，助力妈妈快速恢复',
		});
	}

	onMounted(() => {
		console.log('产康护理页面加载完成');
		console.log('产康护理图片列表:', recoveryList.value);
	});
</script>

<style>
	.recovery-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.recovery-content {
		padding: 20rpx;
	}

	.recovery-intro {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.intro-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.intro-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		text-align: center;
	}

	.recovery-section {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.recovery-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.recovery-item {
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		cursor: pointer;
	}

	.recovery-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	}

	.recovery-image {
		width: 100%;
		height: 400rpx;
		object-fit: cover;
	}

	.recovery-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #8b5a2b;
		text-align: center;
		padding: 20rpx 15rpx 10rpx;
	}
</style>