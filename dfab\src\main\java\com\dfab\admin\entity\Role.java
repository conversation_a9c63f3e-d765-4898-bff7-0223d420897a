package com.dfab.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 角色管理实体类，用于存储系统中的角色信息，包含角色拥有的权限列表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("role")
@Schema(description = "角色管理实体类")
public class Role extends BaseEntity {
    // 角色 ID
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "角色 ID", example = "1")
    private Long id;

    // 角色名称
    @Schema(description = "角色名称", example = "管理员")
    private String name;

    // 角色描述
    @Schema(description = "角色描述", example = "拥有所有管理权限")
    private String description;

    // 角色拥有的权限列表
    @Schema(description = "角色拥有的权限列表")
    @TableField(exist = false)
    private List<Permission> permissions;

    // 角色拥有的权限id
    @Schema(description = "角色拥有的权限id")
    private String permissionIds;
}