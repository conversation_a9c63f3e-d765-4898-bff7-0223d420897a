package com.dfab.carInfo.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.carInfo.entity.CarInfo;
import com.dfab.carInfo.mapper.CarMapper;
import com.dfab.carInfo.service.CarService;
import com.dfab.taskCenter.service.TaskCenterService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 车辆信息Service实现类
 */
@Slf4j
@Service
public class CarServiceImpl extends ServiceImpl<CarMapper, CarInfo> implements CarService {

    @Resource
    private AppUserService appUserService;

    @Autowired
    private TaskCenterService taskCenterService;

    @Resource
    private MemberCheckinService memberCheckinService;

    @Override
    public CarInfo saveCar(CarInfo car) {
        // 检查车牌号是否已存在
        if (isPlateNumberExists(car.getPlateNumber())) {
            throw new RuntimeException("车牌号已存在");
        }

        // createTime 由 MyBatis-Plus 自动填充，不需要手动设置
        // 保存车辆信息
        save(car);

        // 创建车牌新增任务
        try {
            // 尝试获取会员信息
            if (car.getMemberCheckinId() != null) {
                MemberCheckin memberCheckin = memberCheckinService.getById(car.getMemberCheckinId());
                if (memberCheckin != null) {
                    taskCenterService.createCarAddTaskWithMember(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId(),
                        car.getMemberCheckinId(),
                        memberCheckin.getMemberName()
                    );
                } else {
                    taskCenterService.createCarAddTask(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId()
                    );
                }
            } else {
                taskCenterService.createCarAddTask(
                    car.getId(),
                    car.getPlateNumber(),
                    car.getOwnerName(),
                    car.getPhoneNumber(),
                    car.getOpenId(),
                    car.getUserId()
                );
            }
        } catch (Exception e) {
            log.error("创建车牌新增任务失败: {}", e.getMessage(), e);
        }

        return car;
    }

    @Override
    public CarInfo createOrUpdate(CarInfo car) {
        // 根据openId获取用户信息
        AppUser appUser = appUserService.getByOpenId(car.getOpenId());

        // 查找是否已存在相同车牌号的记录
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getOpenId, car.getOpenId())
                         .eq(CarInfo::getPlateNumber, car.getPlateNumber());
        CarInfo existingCar = baseMapper.selectOne(lambdaQueryWrapper);

        try {
            List<MemberCheckin> allMemberCheckinsByOpenId = memberCheckinService.getAllMemberCheckinsByOpenId(car.getOpenId());
            if(CollUtil.isNotEmpty(allMemberCheckinsByOpenId)){
                //会员id
                car.setMemberCheckinId(allMemberCheckinsByOpenId.get(0).getId());
            }
        }catch (Exception e){

        }

        if (existingCar != null) {
            // 更新现有记录
            car.setId(existingCar.getId());
            car.setCreateTime(existingCar.getCreateTime());
            // modifyTime 由 MyBatis-Plus 自动填充，不需要手动设置
            if (appUser != null) {
                car.setUserId(appUser.getId());
            }
            baseMapper.updateById(car);
            return getById(existingCar.getId());
        } else {
            // 检查车牌号是否被其他用户使用
            if (isPlateNumberExists(car.getPlateNumber())) {
                throw new RuntimeException("车牌号已存在");
            }

            // 新增记录
            if (appUser != null) {
                car.setUserId(appUser.getId());
            }
            // createTime 由 MyBatis-Plus 自动填充，不需要手动设置
            baseMapper.insert(car);

            // 创建车牌新增任务
            try {
                // 尝试获取会员信息
                if (car.getMemberCheckinId() != null) {
                    MemberCheckin memberCheckin = memberCheckinService.getById(car.getMemberCheckinId());
                    if (memberCheckin != null) {
                        taskCenterService.createCarAddTaskWithMember(
                            car.getId(),
                            car.getPlateNumber(),
                            car.getOwnerName(),
                            car.getPhoneNumber(),
                            car.getOpenId(),
                            car.getUserId(),
                            car.getMemberCheckinId(),
                            memberCheckin.getMemberName()
                        );
                    } else {
                        taskCenterService.createCarAddTask(
                            car.getId(),
                            car.getPlateNumber(),
                            car.getOwnerName(),
                            car.getPhoneNumber(),
                            car.getOpenId(),
                            car.getUserId()
                        );
                    }
                } else {
                    taskCenterService.createCarAddTask(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId()
                    );
                }
            } catch (Exception e) {
                log.error("创建车牌新增任务失败: {}", e.getMessage(), e);
            }

            return car;
        }
    }

    @Override
    public List<CarInfo> getCarsByOpenId(String openId) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getOpenId, openId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public CarInfo getCarByIdAndOpenId(Long id, String openId) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getId, id)
                         .eq(CarInfo::getOpenId, openId);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<CarInfo> getAllCars() {
        return list();
    }

    @Override
    public List<CarInfo> getCarsByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return list(); // 如果没有提供用户ID，返回所有车辆（管理员权限）
        }

        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return list(queryWrapper);
    }

    @Override
    public boolean isPlateNumberExists(String plateNumber) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getPlateNumber, plateNumber);
        return count(lambdaQueryWrapper) > 0;
    }

    @Override
    public boolean isPlateNumberExists(String plateNumber, Long excludeId) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getPlateNumber, plateNumber);
        if (excludeId != null) {
            lambdaQueryWrapper.ne(CarInfo::getId, excludeId);
        }
        return count(lambdaQueryWrapper) > 0;
    }

    @Override
    public void deleteCarByIdAndOpenId(Long id, String openId) {
        // 验证车辆是否属于该用户
        CarInfo car = getCarByIdAndOpenId(id, openId);
        if (car == null) {
            throw new RuntimeException("无权删除此车辆信息或车辆不存在");
        }

        // 创建车牌删除任务
        try {
            // 用户主动删除车辆
            if (car.getMemberCheckinId() != null) {
                MemberCheckin memberCheckin = memberCheckinService.getById(car.getMemberCheckinId());
                if (memberCheckin != null) {
                    taskCenterService.createCarDeleteTaskByUser(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId(),
                        car.getMemberCheckinId(),
                        memberCheckin.getMemberName()
                    );
                } else {
                    taskCenterService.createCarDeleteTask(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId()
                    );
                }
            } else {
                taskCenterService.createCarDeleteTask(
                    car.getId(),
                    car.getPlateNumber(),
                    car.getOwnerName(),
                    car.getPhoneNumber(),
                    car.getOpenId(),
                    car.getUserId()
                );
            }
        } catch (Exception e) {
            log.error("创建车牌删除任务失败: {}", e.getMessage(), e);
        }

        removeById(id);
    }

    /**
     * 根据ID删除车辆信息（带用户权限验证）
     * @param id 车辆ID
     * @param userId 用户ID
     */
    @Override
    public void deleteCarById(Long id, String userId) {
        if (!StringUtils.hasText(userId)) {
            removeById(id); // 如果没有提供用户ID，直接删除（管理员权限）
            return;
        }

        // 验证车辆是否属于该用户
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("user_id", userId);

        CarInfo car = getOne(queryWrapper);
        if (car == null) {
            throw new RuntimeException("无权删除此车辆信息或车辆不存在");
        }

        // 创建车牌删除任务
        try {
            // 用户主动删除车辆
            if (car.getMemberCheckinId() != null) {
                MemberCheckin memberCheckin = memberCheckinService.getById(car.getMemberCheckinId());
                if (memberCheckin != null) {
                    taskCenterService.createCarDeleteTaskByUser(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId(),
                        car.getMemberCheckinId(),
                        memberCheckin.getMemberName()
                    );
                } else {
                    taskCenterService.createCarDeleteTask(
                        car.getId(),
                        car.getPlateNumber(),
                        car.getOwnerName(),
                        car.getPhoneNumber(),
                        car.getOpenId(),
                        car.getUserId()
                    );
                }
            } else {
                taskCenterService.createCarDeleteTask(
                    car.getId(),
                    car.getPlateNumber(),
                    car.getOwnerName(),
                    car.getPhoneNumber(),
                    car.getOpenId(),
                    car.getUserId()
                );
            }
        } catch (Exception e) {
            log.error("创建车牌删除任务失败: {}", e.getMessage(), e);
        }

        removeById(id);
    }

    /**
     * 根据ID删除车辆信息（不验证用户权限）
     * @param id 车辆ID
     */
    @Override
    public void deleteCarById(Long id) {
        removeById(id);
    }

    @Override
    public int deleteCarsByOpenId(String openId) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getOpenId, openId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public List<CarInfo> getCarsByPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return List.of();
        }
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getPhoneNumber, phoneNumber);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<CarInfo> getCarsByPhoneNumberOrOpenId(String phoneNumber, String openId) {
        // 优先通过openId查询
        if (openId != null && !openId.trim().isEmpty()) {
            List<CarInfo> cars = getCarsByOpenId(openId);
            if (!cars.isEmpty()) {
                return cars;
            }
        }

        // 如果通过openId没有找到，再通过手机号查询
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            return getCarsByPhoneNumber(phoneNumber);
        }

        return List.of();
    }

    @Override
    public List<CarInfo> getCarsByMemberCheckinId(Long memberCheckinId) {
        if (memberCheckinId == null) {
            return List.of();
        }
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarInfo::getMemberCheckinId, memberCheckinId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<CarInfo> getCarsByOpenIdAndMemberCheckinId(String openId, Long memberCheckinId) {
        LambdaQueryWrapper<CarInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        // 使用OR逻辑：查询会员ID匹配的车辆 OR openId匹配的车辆
        if (memberCheckinId != null && openId != null && !openId.trim().isEmpty()) {
            lambdaQueryWrapper.and(wrapper ->
                wrapper.eq(CarInfo::getMemberCheckinId, memberCheckinId)
                       .or()
                       .eq(CarInfo::getOpenId, openId)
            );
        } else if (memberCheckinId != null) {
            // 只有会员ID
            lambdaQueryWrapper.eq(CarInfo::getMemberCheckinId, memberCheckinId);
        } else if (openId != null && !openId.trim().isEmpty()) {
            // 只有openId
            lambdaQueryWrapper.eq(CarInfo::getOpenId, openId);
        } else {
            return List.of();
        }

        return list(lambdaQueryWrapper);
    }
}