package com.dfab.carInfo.controller;

import com.dfab.carInfo.entity.CarInfo;
import com.dfab.carInfo.service.CarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序车辆信息 Controller 类，提供车辆信息的增删改查接口
 */
@RestController
@RequestMapping("/api/cars")
@Tag(name = "CarMiniController", description = "小程序车辆信息接口，提供对车辆信息的增删改查功能")
public class CarMiniController {

    @Autowired
    private CarService carService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 根据 ID 获取车辆信息
     * @return 对应的车辆信息
     */
    @Operation(summary = "根据 ID 获取车辆信息", description = "通过指定的车辆记录 ID 查询对应的车辆信息")
    @PostMapping("/get")
    public ResponseEntity<?> getCarById(@Parameter(description = "车辆记录的 ID", required = true) @RequestBody CarInfo car) {
        try {
            String openId = (String)request.getAttribute("openId");
            car.setOpenId(openId);
            CarInfo result = carService.getCarByIdAndOpenId(car.getId(), openId);
            if (result == null) {
                return ResponseEntity.badRequest().body("未找到对应的车辆信息");
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 创建新的车辆信息
     * @param car 车辆信息实体
     * @return 创建成功的车辆信息
     */
    @Operation(summary = "创建新的车辆信息", description = "将新的车辆信息保存到数据库中")
    @PostMapping("/save")
    public ResponseEntity<?> createCar(@Parameter(description = "车辆信息实体", required = true) @RequestBody CarInfo car) {
        try {
            String openId = (String)request.getAttribute("openId");
            car.setOpenId(openId);
            CarInfo result = carService.createOrUpdate(car);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 删除车辆信息
     */
    @Operation(summary = "删除车辆信息", description = "根据指定的 ID 删除对应的车辆信息")
    @PostMapping("/remove")
    public ResponseEntity<?> deleteCar(@RequestBody CarInfo car) {
        try {
            String openId = (String)request.getAttribute("openId");
            car.setOpenId(openId);
            carService.deleteCarByIdAndOpenId(car.getId(), openId);
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 根据 openId 获取车辆信息
     * @return 对应 openId 的车辆信息列表
     */
    @Operation(summary = "根据 openId 获取车辆信息", description = "通过用户的微信 openId 查询对应的车辆信息")
    @PostMapping("/list")
    public ResponseEntity<?> getAllCarsByOpenId() {
        try {
            String openId = (String)request.getAttribute("openId");
            List<CarInfo> cars = carService.getCarsByOpenId(openId);
            return ResponseEntity.ok(cars);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
} 