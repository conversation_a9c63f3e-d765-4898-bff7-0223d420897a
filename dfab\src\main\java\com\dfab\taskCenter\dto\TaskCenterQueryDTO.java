package com.dfab.taskCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务中心查询参数DTO
 */
@Data
@Schema(description = "任务中心查询参数")
public class TaskCenterQueryDTO {

    /**
     * 任务类型：1-车牌新增，2-车牌删除，3-离所处理，4-会员注册，5-预约处理，6-投诉处理等
     */
    @Schema(description = "任务类型", example = "1")
    private Integer taskType;

    /**
     * 任务状态：0-待处理，1-已处理
     */
    @Schema(description = "任务状态", example = "0")
    private Integer status;

    /**
     * 关键信息（核心业务数据，如车牌号、会员号、订单号等）
     */
    @Schema(description = "关键信息", example = "粤A12345")
    private String keyInfo;

    /**
     * 业务类型（car-车辆，member-会员，order-订单，complaint-投诉等）
     */
    @Schema(description = "业务类型", example = "car")
    private String businessType;

    /**
     * 关联业务ID（可以是车辆ID、会员ID、订单ID等）
     */
    @Schema(description = "关联业务ID", example = "1")
    private Long businessId;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话", example = "***********")
    private String customerPhone;

    /**
     * 微信 openId
     */
    @Schema(description = "微信 openId", example = "wx1234567890")
    private String openId;

    /**
     * 用户ID（用于数据隔离）
     */
    @Schema(description = "用户 ID", example = "1")
    private Long userId;

    /**
     * 处理人员
     */
    @Schema(description = "处理人员", example = "前台小王")
    private String processor;

    /**
     * 优先级：1-低，2-中，3-高
     */
    @Schema(description = "优先级", example = "2")
    private Integer priority;

    /**
     * 是否已推送：0-未推送，1-已推送
     */
    @Schema(description = "是否已推送", example = "0")
    private Integer isPushed;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 处理时间开始
     */
    @Schema(description = "处理时间开始")
    private LocalDateTime processTimeStart;

    /**
     * 处理时间结束
     */
    @Schema(description = "处理时间结束")
    private LocalDateTime processTimeEnd;
}
