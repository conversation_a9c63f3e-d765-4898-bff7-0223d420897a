package com.dfab.memberCheckin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dfab.carInfo.entity.CarInfo;
import com.dfab.carInfo.service.CarService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 入住会员管理后台管理 Controller 类
 */
@RestController
@RequestMapping("/admin/memberCheckin")
@Tag(name = "MemberCheckinAdminController", description = "入住会员管理后台管理接口")
public class MemberCheckinAdminController {

    @Autowired
    private MemberCheckinService memberCheckinService;

    @Autowired
    private CarService carService;

    /**
     * 分页查询入住会员列表
     * @param memberCheckin 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询入住会员列表", description = "根据条件分页查询入住会员列表数据")
    @PostMapping("/list")
    public Map<String, Object> list(@RequestBody MemberCheckin memberCheckin) {
        IPage<MemberCheckin> page = memberCheckinService.page(memberCheckin);
        Map<String, Object> result = new HashMap<>();
        result.put("rows", page.getRecords());
        result.put("total", page.getTotal());
        return result;
    }

    /**
     * 根据 ID 获取入住会员信息
     * @param id 入住会员记录的 ID
     * @return 对应的入住会员信息
     */
    @Operation(summary = "根据 ID 获取入住会员信息", description = "通过指定的入住会员记录 ID 查询对应的入住信息")
    @PostMapping("/{id}")
    public Map<String, Object> getMemberCheckinById(@Parameter(description = "入住会员记录的 ID", required = true) @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", memberCheckinService.getById(id));
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }
    
    /**
     * 添加入住会员信息
     */
    @Operation(summary = "添加入住会员信息", description = "添加新的入住会员记录")
    @PostMapping("/add")
    public Map<String, Object> add(@Valid @RequestBody MemberCheckin memberCheckin) {
        memberCheckinService.add(memberCheckin);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }
    
    /**
     * 根据 ID 修改入住会员信息
     */
    @Operation(summary = "根据 ID 修改入住会员信息", description = "通过指定的入住会员记录 ID 修改对应的入住信息")
    @PostMapping("/update")
    public Map<String, Object> updateMemberCheckinById(@Parameter(description = "入住会员对象", required = true) @RequestBody MemberCheckin memberCheckin) {
        Boolean result = memberCheckinService.updateByAdmin(memberCheckin);
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "操作成功");
        return response;
    }

    /**
     * 根据 ID 修改入住会员信息
     */
    @Operation(summary = "根据 ID 修改入住房间", description = "根据 ID 修改入住房间")
    @PostMapping("/updateRoomNumber")
    public Boolean updateRoomNumber(@Parameter(description = "入住会员对象", required = true) @RequestBody MemberCheckin memberCheckin) {
        return memberCheckinService.updateRoomNumber(memberCheckin);
    }

    /**
     * 删除入住会员信息
     */
    @Operation(summary = "删除入住会员信息", description = "根据ID删除入住会员信息，支持批量删除")
    @PostMapping("/delete")
    public Map<String, Object> delete(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 打印请求参数，便于调试
            System.out.println("删除请求参数: " + params);
            
            Object idObj = params.get("id");
            
            if (idObj == null) {
                throw new IllegalArgumentException("id参数不能为空");
            }
            
            if (idObj instanceof List) {
                // 批量删除 - 需要处理数值类型的转换
                List<Long> ids = new ArrayList<>();
                List<?> rawIds = (List<?>) idObj;
                
                for (Object item : rawIds) {
                    if (item instanceof Integer) {
                        ids.add(((Integer) item).longValue());
                    } else if (item instanceof Long) {
                        ids.add((Long) item);
                    } else if (item instanceof String) {
                        try {
                            ids.add(Long.parseLong((String) item));
                        } catch (NumberFormatException e) {
                            System.out.println("无法解析ID: " + item);
                        }
                    } else if (item instanceof Number) {
                        ids.add(((Number) item).longValue());
                    }
                }
                
                if (!ids.isEmpty()) {
                    // 批量删除前先为每个会员的关联车辆创建删除任务
                    List<MemberCheckin> membersToDelete = memberCheckinService.listByIds(ids);
                    for (MemberCheckin member : membersToDelete) {
                        memberCheckinService.createCarDeleteTasksForDeletedMember(member);
                    }

                    memberCheckinService.removeByIds(ids);
                }
            } else {
                // 单个删除
                Long id;
                if (idObj instanceof Integer) {
                    id = ((Integer) idObj).longValue();
                } else if (idObj instanceof Long) {
                    id = (Long) idObj;
                } else if (idObj instanceof String) {
                    try {
                        id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的ID格式: " + idObj);
                    }
                } else if (idObj instanceof Number) {
                    id = ((Number) idObj).longValue();
                } else {
                    throw new IllegalArgumentException("无效的ID类型");
                }
                
                // 删除前先获取会员信息，为关联车辆创建删除任务
                MemberCheckin memberToDelete = memberCheckinService.getById(id);
                if (memberToDelete != null) {
                    memberCheckinService.createCarDeleteTasksForDeletedMember(memberToDelete);
                }

                memberCheckinService.removeById(id);
            }

            result.put("code", 200);
            result.put("msg", "操作成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "删除失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 导出入住会员信息
     */
    @Operation(summary = "导出入住会员信息", description = "导出所有入住会员信息")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ModelAttribute MemberCheckin memberCheckin) {
        ExcelUtil<MemberCheckin> util = new ExcelUtil<>(MemberCheckin.class);
        util.exportExcel(response, memberCheckinService.list(memberCheckin), "会员信息");

    }

    /**
     * 获取所有入住会员列表（用于下拉选择）
     */
    @Operation(summary = "获取所有入住会员列表", description = "获取所有入住会员列表，用于下拉选择")
    @PostMapping("/all")
    public Map<String, Object> getAllMemberCheckins() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 只查询已入住状态的会员
            List<MemberCheckin> memberCheckins = memberCheckinService.getByStatus(0);

            result.put("code", 200);
            result.put("msg", "查询成功");
            result.put("data", memberCheckins);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取会员关联的车辆信息
     */
    @Operation(summary = "获取会员关联的车辆信息", description = "根据会员的手机号或openId查询关联的车辆信息")
    @PostMapping("/cars")
    public Map<String, Object> getMemberCars(@RequestBody Map<String, String> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            String phoneNumber = params.get("phoneNumber");
            String openId = params.get("openId");

            List<CarInfo> cars = carService.getCarsByPhoneNumberOrOpenId(phoneNumber, openId);

            result.put("code", 200);
            result.put("msg", "查询成功");
            result.put("data", cars);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }
}
