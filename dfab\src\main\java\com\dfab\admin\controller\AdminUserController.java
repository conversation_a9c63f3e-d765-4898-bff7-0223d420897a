/*
package com.dfab.admin.controller;

import com.dfab.admin.entity.AdminUser;
import com.dfab.admin.service.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/users")
@Tag(name = "AdminUserController", description = "后台用户管理接口")
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    @Operation(summary = "获取所有后台用户")
    @GetMapping
    public List<AdminUser> getAllUsers() {
        return adminUserService.list();
    }

    @Operation(summary = "根据 ID 获取后台用户")
    @GetMapping("/{id}")
    public AdminUser getUserById(@PathVariable Long id) {
        return adminUserService.getById(id);
    }

    @Operation(summary = "创建新的后台用户")
    @PostMapping
    public AdminUser createUser(@RequestBody AdminUser user) {
        adminUserService.save(user);
        return user;
    }

    @Operation(summary = "更新后台用户信息")
    @PutMapping("/{id}")
    public AdminUser updateUser(@PathVariable Long id, @RequestBody AdminUser user) {
        user.setId(id);
        adminUserService.updateById(user);
        return user;
    }

    @Operation(summary = "删除后台用户")
    @DeleteMapping("/{id}")
    public void deleteUser(@PathVariable Long id) {
        adminUserService.removeById(id);
    }
}*/
