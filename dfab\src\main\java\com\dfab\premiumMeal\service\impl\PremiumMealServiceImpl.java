package com.dfab.premiumMeal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.premiumMeal.entity.PremiumMeal;
import com.dfab.premiumMeal.mapper.PremiumMealMapper;
import com.dfab.premiumMeal.service.PremiumMealService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 高档餐 Service 实现类
 */
@Service
public class PremiumMealServiceImpl extends ServiceImpl<PremiumMealMapper, PremiumMeal> implements PremiumMealService {

    @Override
    public List<PremiumMeal> getByMemberCheckinId(Long memberCheckinId) {
        LambdaQueryWrapper<PremiumMeal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PremiumMeal::getMemberCheckinId, memberCheckinId);
        queryWrapper.orderByDesc(PremiumMeal::getConsumeTime);
        queryWrapper.orderByDesc(PremiumMeal::getType);
        return this.list(queryWrapper);
    }

    @Override
    public IPage<PremiumMeal> page(PremiumMeal premiumMeal) {
        Page<PremiumMeal> page = new Page<>(premiumMeal.getPageNum(), premiumMeal.getPageSize());
        LambdaQueryWrapper<PremiumMeal> queryWrapper = new LambdaQueryWrapper<>();
        
        if (premiumMeal.getMemberCheckinId() != null) {
            queryWrapper.eq(PremiumMeal::getMemberCheckinId, premiumMeal.getMemberCheckinId());
        }
        if (premiumMeal.getPremiumMealDetail() != null && !premiumMeal.getPremiumMealDetail().isEmpty()) {
            queryWrapper.like(PremiumMeal::getPremiumMealDetail, premiumMeal.getPremiumMealDetail());
        }
        
        queryWrapper.orderByDesc(PremiumMeal::getCreateTime);
        return this.page(page, queryWrapper);
    }

    @Override
    public PremiumMeal addByAdmin(PremiumMeal premiumMeal) {
        this.save(premiumMeal);
        return premiumMeal;
    }

    @Override
    public Boolean updateByAdmin(PremiumMeal premiumMeal) {
        if (premiumMeal.getId() == null) {
            throw new RuntimeException("id不能为空");
        }
        return this.updateById(premiumMeal);
    }

    @Override
    public Boolean removeByAdmin(Long id) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        return this.removeById(id);
    }
}
