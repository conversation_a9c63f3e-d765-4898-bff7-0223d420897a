package com.dfab.material.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dfab.material.dto.MemberPackagePayResult;
import com.dfab.material.dto.RecordQueryDto;
import com.dfab.material.dto.MemberPackageDto;
import com.dfab.material.entity.Inventory;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.entity.MaterialInfo;
import com.dfab.material.service.InventoryRecordService;
import com.dfab.material.service.InventoryService;
import com.dfab.material.service.MaterialInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物料管理后台Controller
 */
@RestController
@RequestMapping("/admin/material")
@Tag(name = "MaterialAdminController", description = "后台物料管理接口，提供对物料信息的增删改查功能")
@Slf4j
public class MaterialAdminController {

    @Autowired
    private MaterialInfoService materialInfoService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private InventoryRecordService inventoryRecordService;

    /**
     * 查询物料信息列表
     */
    @PostMapping("/list")
    @Operation(summary = "查询物料信息列表", description = "查询所有物料信息列表")
    public ResponseEntity<List<MaterialInfo>> list(@RequestBody(required = false) MaterialInfo materialInfo) {
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (null == materialInfo ||  null == materialInfo.getMemberId()) {
            queryWrapper.isNull(MaterialInfo::getMemberId);
        }else {
            queryWrapper.eq(MaterialInfo::getMemberId,materialInfo.getMemberId());
        }
        List<MaterialInfo> list = materialInfoService.list(queryWrapper);
        return ResponseEntity.ok(list);
    }

    /**
     * 查询物料信息列表
     */
    @PostMapping("/memberList")
    @Operation(summary = "查询会员套餐物料信息列表", description = "查询会员套餐物料信息列表")
    public ResponseEntity<List<MaterialInfo>> memberList(@RequestBody MaterialInfo materialInfo) {
        List<MaterialInfo> list = materialInfoService.memberList(materialInfo);
        return ResponseEntity.ok(list);
    }

     /**
     * 查询物料信息列表
     */
    @PostMapping("/memberListPay")
    @Operation(summary = "查询会员结算列表", description = "查询会员结算列表")
    public ResponseEntity<MemberPackagePayResult> memberListPay(@RequestBody MaterialInfo materialInfo) {
        MemberPackagePayResult result = materialInfoService.memberListPay(materialInfo);
        return ResponseEntity.ok(result);
    }

    /**
     * 查询套餐默认物料信息列表
     */
    @PostMapping("/packageDefaultList")
    @Operation(summary = "查询套餐默认物料信息列表", description = "查询套餐默认物料信息列表")
    public ResponseEntity<List<MaterialInfo>> packageDefaultList() {
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(MaterialInfo::getIsPackageMaterial,1);
        queryWrapper.isNull(MaterialInfo::getMemberId);

        List<MaterialInfo> list = materialInfoService.list(queryWrapper);
        return ResponseEntity.ok(list);
    }

    /**
     * 查询套餐默认物料信息列表
     */
    @PostMapping("/warnList")
    @Operation(summary = "查询库存预警物料信息列表", description = "查询库存预警物料信息列表")
    public ResponseEntity<List<MaterialInfo>> warnList() {

        List<MaterialInfo> list = inventoryService.warnList();
        return ResponseEntity.ok(list);
    }

    /**
     * 分页查询物料信息
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询物料信息", description = "分页查询物料信息列表")
    public ResponseEntity<Page<MaterialInfo>> page(@RequestBody MaterialInfo materialInfo) {
        LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();


        if (null == materialInfo ||  null == materialInfo.getMemberId()) {
            queryWrapper.isNull(MaterialInfo::getMemberId);
        }else {
            queryWrapper.eq(MaterialInfo::getMemberId,materialInfo);
        }

        // 如果有物料名称，按物料名称模糊查询
        if (materialInfo.getMaterialName() != null && !materialInfo.getMaterialName().isEmpty()) {
            queryWrapper.like(MaterialInfo::getMaterialName, materialInfo.getMaterialName());
        }

        // 如果有规格型号，按规格型号模糊查询
        if (materialInfo.getSpecification() != null && !materialInfo.getSpecification().isEmpty()) {
            queryWrapper.like(MaterialInfo::getSpecification, materialInfo.getSpecification());
        }

        // 如果有是否套餐物料条件，按是否套餐物料查询
        if (materialInfo.getIsPackageMaterial() != null) {
            queryWrapper.eq(MaterialInfo::getIsPackageMaterial, materialInfo.getIsPackageMaterial());
        }
        
        // 创建分页对象
        Page<MaterialInfo> page = new Page<>(
                materialInfo.getPageNum() != null ? materialInfo.getPageNum() : 1,
                materialInfo.getPageSize() != null ? materialInfo.getPageSize() : 10
        );
        
        // 执行分页查询
        Page<MaterialInfo> result = materialInfoService.page(page, queryWrapper);
        
        // 添加日志
        log.info("分页查询结果: total={}, records.size={}", result.getTotal(), result.getRecords().size());
        
        return ResponseEntity.ok(result);
    }

    /**
     * 根据ID查询物料信息
     */
    @PostMapping("/getById")
//    @Operation(summary = "根据ID查询物料信息", description = "通过指定的物料信息ID查询对应的物料信息")
    public ResponseEntity<MaterialInfo> getById(@RequestBody MaterialInfo materialInfo) {
        if (materialInfo.getId() == null) {
            throw new RuntimeException("物料ID不能为空");
        }
        MaterialInfo info = materialInfoService.getById(materialInfo.getId());
        Inventory byMaterialId = inventoryService.getByMaterialId(info.getMaterialId());
        if(byMaterialId != null){
            info.setQuantity(byMaterialId.getQuantity());
        }
        return ResponseEntity.ok(info);
    }

    /**
     * 根据ID查询物料信息
     */
    @PostMapping("/getInventoryById")
//    @Operation(summary = "根据ID查询物料信息", description = "通过指定的物料信息ID查询对应的物料信息")
    public ResponseEntity<MaterialInfo> getById(@RequestBody Inventory inventory) {
        if (inventory.getId() == null) {
            throw new RuntimeException("物料ID不能为空");
        }
        Inventory inventory1 = inventoryService.getById(inventory.getId());
        MaterialInfo info = materialInfoService.getByMaterialId(inventory1.getMaterialId());

        if(info != null && inventory1 != null){
            info.setQuantity(inventory1.getQuantity());
        }
        return ResponseEntity.ok(info);
    }

    /**
     * 新增物料信息
     */
    @PostMapping("/add")
    @Operation(summary = "新增物料信息", description = "新增物料信息")
    public ResponseEntity<Boolean> add(@Valid @RequestBody MaterialInfo materialInfo) {
        boolean result = materialInfoService.save(materialInfo);
        if(result){
            Inventory inventory = new Inventory();
            inventory.setMaterialId(materialInfo.getId());
            inventory.setQuantity(0);
            inventoryService.save(inventory);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 更新物料信息
     */
    @PostMapping("/update")
    @Operation(summary = "更新物料信息", description = "更新物料信息")
    public ResponseEntity<Boolean> update(@Valid @RequestBody MaterialInfo materialInfo) {
        if (materialInfo.getId() == null) {
            throw new RuntimeException("物料ID不能为空");
        }
        boolean result = materialInfoService.updateById(materialInfo);
        return ResponseEntity.ok(result);
    }

    /**
     * 删除物料信息
     */
    @PostMapping("/delete")
    @Operation(summary = "删除物料信息", description = "根据ID删除物料信息，支持批量删除")
    public ResponseEntity<Map<String, Object>> delete(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("删除物料请求参数: {}", params);

            Object idObj = params.get("id");

            if (idObj == null) {
                throw new IllegalArgumentException("id参数不能为空");
            }

            if (idObj instanceof List) {
                // 批量删除
                List<Long> ids = new ArrayList<>();
                List<?> rawIds = (List<?>) idObj;

                for (Object item : rawIds) {
                    if (item instanceof Integer) {
                        ids.add(((Integer) item).longValue());
                    } else if (item instanceof Long) {
                        ids.add((Long) item);
                    } else if (item instanceof String) {
                        try {
                            ids.add(Long.parseLong((String) item));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析ID: {}", item);
                        }
                    } else if (item instanceof Number) {
                        ids.add(((Number) item).longValue());
                    }
                }

                if (!ids.isEmpty()) {
                    materialInfoService.removeByIds(ids);
                    log.info("批量删除物料成功，删除数量: {}", ids.size());
                }
            } else {
                // 单个删除
                Long id;
                if (idObj instanceof Integer) {
                    id = ((Integer) idObj).longValue();
                } else if (idObj instanceof Long) {
                    id = (Long) idObj;
                } else if (idObj instanceof String) {
                    try {
                        id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的ID格式: " + idObj);
                    }
                } else if (idObj instanceof Number) {
                    id = ((Number) idObj).longValue();
                } else {
                    throw new IllegalArgumentException("无效的ID类型");
                }

                materialInfoService.removeById(id);
                log.info("删除物料成功，ID: {}", id);
            }

            result.put("code", 200);
            result.put("msg", "操作成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "删除失败：" + e.getMessage());
            log.error("删除物料失败", e);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 查询库存信息列表
     */
    @PostMapping("/inventory/list")
    @Operation(summary = "查询库存信息列表", description = "查询所有库存信息列表（包含物料信息）")
    public ResponseEntity<List<Map<String, Object>>> inventoryList() {
        List<Map<String, Object>> list = inventoryService.getInventoryList();
        return ResponseEntity.ok(list);
    }

    /**
     * 分页查询库存信息
     */
    @PostMapping("/inventory/page")
    @Operation(summary = "分页查询库存信息", description = "分页查询库存信息列表（包含物料信息）")
    public ResponseEntity<Page<Map<String, Object>>> inventoryPage(@RequestBody MaterialInfo materialInfo) {
        log.info("库存分页查询请求参数: pageNum={}, pageSize={}, materialName={}, specification={}",
                materialInfo.getPageNum(), materialInfo.getPageSize(),
                materialInfo.getMaterialName(), materialInfo.getSpecification());

        Page<Map<String, Object>> result = inventoryService.getInventoryPage(materialInfo);

        log.info("库存分页查询结果: total={}, records.size={}",
                result.getTotal(), result.getRecords().size());

        return ResponseEntity.ok(result);
    }

    /**
     * 根据物料ID查询库存信息
     */
    @PostMapping("/inventory/getByMaterialId")
    @Operation(summary = "根据物料ID查询库存信息", description = "通过物料ID查询对应的库存信息")
    public ResponseEntity<Inventory> getInventoryByMaterialId(@RequestBody Map<String, Object> params) {
        Object materialIdObj = params.get("materialId");
        if (materialIdObj == null) {
            throw new RuntimeException("物料ID不能为空");
        }

        Long materialId;
        if (materialIdObj instanceof Integer) {
            materialId = ((Integer) materialIdObj).longValue();
        } else if (materialIdObj instanceof Long) {
            materialId = (Long) materialIdObj;
        } else if (materialIdObj instanceof String) {
            try {
                materialId = Long.parseLong((String) materialIdObj);
            } catch (NumberFormatException e) {
                throw new RuntimeException("无效的物料ID格式: " + materialIdObj);
            }
        } else if (materialIdObj instanceof Number) {
            materialId = ((Number) materialIdObj).longValue();
        } else {
            throw new RuntimeException("无效的物料ID类型");
        }

        Inventory inventory = inventoryService.getByMaterialId(materialId);
        return ResponseEntity.ok(inventory);
    }

    /**
     * 入库操作
     */
    @PostMapping("/inventory/inbound")
    @Operation(summary = "入库操作", description = "物料入库操作")
    public ResponseEntity<Boolean> inbound(@Valid @RequestBody InventoryRecord record) {
        boolean result = inventoryService.inbound(record);
        return ResponseEntity.ok(result);
    }

    /**
     * 出库操作
     */
    @PostMapping("/inventory/outbound")
    @Operation(summary = "出库操作", description = "物料出库操作")
    public ResponseEntity<Boolean> outbound(@Valid @RequestBody InventoryRecord record) {
        boolean result = inventoryService.outbound(record);
        return ResponseEntity.ok(result);
    }

    /**
     * 删除库存信息
     */
    @PostMapping("/inventory/delete")
    @Operation(summary = "删除库存信息", description = "根据ID删除库存信息，支持批量删除")
    public ResponseEntity<Map<String, Object>> deleteInventory(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("删除库存请求参数: {}", params);

            Object idObj = params.get("id");

            if (idObj == null) {
                throw new IllegalArgumentException("id参数不能为空");
            }

            if (idObj instanceof List) {
                // 批量删除
                List<Long> ids = new ArrayList<>();
                List<?> rawIds = (List<?>) idObj;

                for (Object item : rawIds) {
                    if (item instanceof Integer) {
                        ids.add(((Integer) item).longValue());
                    } else if (item instanceof Long) {
                        ids.add((Long) item);
                    } else if (item instanceof String) {
                        try {
                            ids.add(Long.parseLong((String) item));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析ID: {}", item);
                        }
                    } else if (item instanceof Number) {
                        ids.add(((Number) item).longValue());
                    }
                }

                if (!ids.isEmpty()) {
                    inventoryService.removeByIds(ids);
                    log.info("批量删除库存成功，删除数量: {}", ids.size());
                }
            } else {
                // 单个删除
                Long id;
                if (idObj instanceof Integer) {
                    id = ((Integer) idObj).longValue();
                } else if (idObj instanceof Long) {
                    id = (Long) idObj;
                } else if (idObj instanceof String) {
                    try {
                        id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的ID格式: " + idObj);
                    }
                } else if (idObj instanceof Number) {
                    id = ((Number) idObj).longValue();
                } else {
                    throw new IllegalArgumentException("无效的ID类型");
                }

                inventoryService.removeById(id);
                log.info("删除库存成功，ID: {}", id);
            }

            result.put("code", 200);
            result.put("msg", "操作成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "删除失败：" + e.getMessage());
            log.error("删除库存失败", e);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 查询出入库记录列表
     */
    @PostMapping("/record/list")
    @Operation(summary = "查询出入库记录列表", description = "查询所有出入库记录列表（包含物料信息）")
    public ResponseEntity<List<Map<String, Object>>> recordList(@RequestBody(required = false) InventoryRecord queryDto) {
        List<Map<String, Object>> list = inventoryRecordService.getRecordList(queryDto);
        return ResponseEntity.ok(list);
    }

    /**
     * 分页查询出入库记录
     */
    @PostMapping("/record/page")
    @Operation(summary = "分页查询出入库记录", description = "分页查询出入库记录列表（包含物料信息）")
    public ResponseEntity<Page<Map<String, Object>>> recordPage(@RequestBody RecordQueryDto queryDto) {
        log.info("出入库记录分页查询请求参数: pageNum={}, pageSize={}, materialName={}, specification={}, recordType={}, quantity={}, useUnit={}, remark={}, creator={}, dateRange={}",
                queryDto.getPageNum(), queryDto.getPageSize(), queryDto.getMaterialName(),
                queryDto.getSpecification(), queryDto.getRecordType(), queryDto.getQuantity(),
                queryDto.getUseUnit(), queryDto.getRemark(), queryDto.getCreator(), queryDto.getDateRange());

        Page<Map<String, Object>> result = inventoryRecordService.getRecordPage(queryDto);

        log.info("出入库记录分页查询结果: total={}, records.size={}",
                result.getTotal(), result.getRecords().size());

        return ResponseEntity.ok(result);
    }

    /**
     * 删除出入库记录
     */
    @PostMapping("/record/delete")
    @Operation(summary = "删除出入库记录", description = "根据ID删除出入库记录，支持批量删除")
    public ResponseEntity<Map<String, Object>> deleteRecord(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("删除出入库记录请求参数: {}", params);

            Object idObj = params.get("id");

            if (idObj == null) {
                throw new IllegalArgumentException("id参数不能为空");
            }

            if (idObj instanceof List) {
                // 批量删除
                List<Long> ids = new ArrayList<>();
                List<?> rawIds = (List<?>) idObj;

                for (Object item : rawIds) {
                    if (item instanceof Integer) {
                        ids.add(((Integer) item).longValue());
                    } else if (item instanceof Long) {
                        ids.add((Long) item);
                    } else if (item instanceof String) {
                        try {
                            ids.add(Long.parseLong((String) item));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析ID: {}", item);
                        }
                    } else if (item instanceof Number) {
                        ids.add(((Number) item).longValue());
                    }
                }

                if (!ids.isEmpty()) {
                    inventoryRecordService.removeByIds(ids);
                    log.info("批量删除出入库记录成功，删除数量: {}", ids.size());
                }
            } else {
                // 单个删除
                Long id;
                if (idObj instanceof Integer) {
                    id = ((Integer) idObj).longValue();
                } else if (idObj instanceof Long) {
                    id = (Long) idObj;
                } else if (idObj instanceof String) {
                    try {
                        id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的ID格式: " + idObj);
                    }
                } else if (idObj instanceof Number) {
                    id = ((Number) idObj).longValue();
                } else {
                    throw new IllegalArgumentException("无效的ID类型");
                }

                inventoryRecordService.removeById(id);
                log.info("删除出入库记录成功，ID: {}", id);
            }

            result.put("code", 200);
            result.put("msg", "操作成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "删除失败：" + e.getMessage());
            log.error("删除出入库记录失败", e);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 更新会员套餐信息
     */
    @PostMapping("/updateMemberPackages")
    @Operation(summary = "更新会员套餐信息", description = "更新会员套餐信息")
    public ResponseEntity<Boolean> updateMemberPackages(@RequestBody MemberPackageDto memberPackageDto) {
        // 实现更新逻辑
        boolean result = materialInfoService.updateMemberPackages(memberPackageDto);
        return ResponseEntity.ok(result);
    }
}

