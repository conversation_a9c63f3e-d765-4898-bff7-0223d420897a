<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="预约类型" prop="reservationType">
        <el-select v-model="queryParams.reservationType" placeholder="请选择预约类型" clearable style="width: 200px">
          <el-option
            v-for="(name, code) in reservationTypes"
            :key="code"
            :label="name"
            :value="parseInt(code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预约状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择预约状态" clearable style="width: 200px">
          <el-option label="待确认" value="0" />
          <el-option label="已确认" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预约时间" prop="reservationTimeRange">
        <el-date-picker
          v-model="queryParams.reservationTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:universalReservation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:universalReservation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:universalReservation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:universalReservation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">今日预约</div>
            <div class="statistic-value">{{ statistics.todayCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">待处理</div>
            <div class="statistic-value">{{ statistics.pendingCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="universalReservationList" @selection-change="handleSelectionChange" height="600">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column label="预约类型" align="center" prop="reservationType" width="100">
        <template #default="scope">
          <el-tag :type="getReservationTypeTag(scope.row.reservationType).type">
            {{ getReservationTypeTag(scope.row.reservationType).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预约标题" align="center" prop="title" width="150" show-overflow-tooltip />
      <el-table-column label="用户信息" align="center" width="120">
        <template #default="scope">
          <div>{{ scope.row.userName }}</div>
          <div style="font-size: 12px; color: #999;">{{ scope.row.userPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="预约时间" align="center" prop="reservationTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reservationTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预约地点" align="center" prop="location" width="120" show-overflow-tooltip />
      <el-table-column label="房间号" align="center" prop="roomNumber" width="80" />
      <el-table-column label="预约状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status).type">
            {{ getStatusTag(scope.row.status).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="处理人员" align="center" width="120">
        <template #default="scope">
          <div v-if="scope.row.handlerName">{{ scope.row.handlerName }}</div>
          <div v-if="scope.row.handlerPhone" style="font-size: 12px; color: #999;">{{ scope.row.handlerPhone }}</div>
          <span v-if="!scope.row.handlerName" style="color: #999;">未分配</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['business:universalReservation:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['business:universalReservation:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['business:universalReservation:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="预约详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="预约类型">
          <el-tag :type="getReservationTypeTag(detailForm.reservationType).type">
            {{ getReservationTypeTag(detailForm.reservationType).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预约状态">
          <el-tag :type="getStatusTag(detailForm.status).type">
            {{ getStatusTag(detailForm.status).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.userName || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="预约时间">{{ parseTime(detailForm.reservationTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        <el-descriptions-item label="预约标题" :span="2">{{ detailForm.title || '无' }}</el-descriptions-item>
        <el-descriptions-item label="预约描述" :span="2">{{ detailForm.description || '无' }}</el-descriptions-item>
<!--        <el-descriptions-item label="扩展属性" :span="2">
          <pre v-if="detailForm.extendedProperties">{{ formatExtendedProperties(detailForm.extendedProperties) }}</pre>
          <span v-else>无</span>
        </el-descriptions-item>-->
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/编辑弹窗 -->
    <UniversalReservationForm
      v-model:visible="formVisible"
      :reservation-id="currentReservationId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup name="UniversalReservation">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import {
  listUniversalReservation,
  getUniversalReservation,
  delUniversalReservation,
  addUniversalReservation,
  updateUniversalReservation,
  getReservationTypes,
  getReservationStatistics
} from "@/api/business/universalReservation";
import UniversalReservationForm from './form.vue';

const { proxy } = getCurrentInstance();
import { parseTime } from '@/utils/ruoyi';

const universalReservationList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const reservationTypes = ref({});
const statistics = ref({});
const formVisible = ref(false);
const currentReservationId = ref(null);
const detailOpen = ref(false);
const detailForm = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    reservationType: undefined,
    status: undefined,
    userName: undefined,
    contactPhone: undefined,
    reservationTimeRange: undefined,
    reservationTimeStart: undefined,
    reservationTimeEnd: undefined
  },
  rules: {
    reservationType: [
      { required: true, message: "预约类型不能为空", trigger: "change" }
    ],
    title: [
      { required: true, message: "预约标题不能为空", trigger: "blur" }
    ],
    reservationTime: [
      { required: true, message: "预约时间不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询通用预约列表 */
function getList() {
  loading.value = true;
  
  // 处理时间范围
  if (queryParams.value.reservationTimeRange && queryParams.value.reservationTimeRange.length === 2) {
    queryParams.value.reservationTimeStart = queryParams.value.reservationTimeRange[0];
    queryParams.value.reservationTimeEnd = queryParams.value.reservationTimeRange[1];
  } else {
    queryParams.value.reservationTimeStart = undefined;
    queryParams.value.reservationTimeEnd = undefined;
  }
  
  // 创建查询参数副本，移除reservationTimeRange字段
  const queryData = { ...queryParams.value };
  delete queryData.reservationTimeRange;
  
  listUniversalReservation(queryData).then(response => {
    if (response && response.records) {
      universalReservationList.value = response.records;
      total.value = response.total;
    } else {
      universalReservationList.value = [];
      total.value = 0;
    }
    loading.value = false;
  });
}

/** 获取预约类型 */
function getReservationTypesList() {
  getReservationTypes().then(response => {
    reservationTypes.value = response.types || {};
  });
}

/** 获取统计信息 */
function getStatistics() {
  getReservationStatistics({}).then(response => {
    statistics.value = response;
  });
}

/** 预约类型标签 */
function getReservationTypeTag(type) {
  const typeMap = {
    1: { text: '用车预约', type: 'warning' },
    2: { text: '试餐预约', type: 'primary' },
    3: { text: '看房预约', type: 'info' },
    4: { text: '其他预约', type: '' }
  };
  return typeMap[type] || { text: '未知', type: 'danger' };
}

/** 状态标签 */
function getStatusTag(status) {
  const statusMap = {
    0: { text: '待确认', type: 'warning' },
    1: { text: '已确认', type: 'primary' },
    2: { text: '已完成', type: 'success' },
    3: { text: '已取消', type: 'danger' }
  };
  return statusMap[status] || { text: '未知', type: 'info' };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  currentReservationId.value = null;
  formVisible.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row.id || ids.value[0];
  currentReservationId.value = id;
  formVisible.value = true;
}

/** 详情按钮操作 */
function handleDetail(row) {
  detailForm.value = row;
  detailOpen.value = true;
}

/** 格式化扩展属性 */
function formatExtendedProperties(extendedProperties) {
  try {
    if (typeof extendedProperties === 'string') {
      return JSON.stringify(JSON.parse(extendedProperties), null, 2);
    }
    return JSON.stringify(extendedProperties, null, 2);
  } catch (e) {
    return extendedProperties;
  }
}

/** 表单提交成功回调 */
function handleFormSuccess() {
  getList();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const deleteIds = row.id ? [row.id] : ids.value;
  proxy.$modal.confirm('是否确认删除通用预约编号为"' + deleteIds + '"的数据项？').then(function() {
    const promises = deleteIds.map(id => delUniversalReservation({ id: id }));
    return Promise.all(promises);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/universalReservation/export', {
    ...queryParams.value
  }, `universalReservation_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  getReservationTypesList();
  getStatistics();
});
</script>

<style scoped>
.statistic-item {
  text-align: center;
  padding: 20px;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.box-card {
  margin-bottom: 20px;
}
</style>
