package com.dfab.taskCenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dfab.taskCenter.entity.TaskCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务中心Mapper接口
 */
@Mapper
public interface TaskCenterMapper extends BaseMapper<TaskCenter> {

    /**
     * 根据任务类型查询任务列表
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<TaskCenter> selectByTaskType(@Param("taskType") Integer taskType);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<TaskCenter> selectByStatus(@Param("status") Integer status);

    /**
     * 根据用户ID查询任务列表
     * @param userId 用户ID
     * @return 任务列表
     */
    List<TaskCenter> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据关键信息查询任务列表
     * @param keyInfo 关键信息
     * @return 任务列表
     */
    List<TaskCenter> selectByKeyInfo(@Param("keyInfo") String keyInfo);

    /**
     * 根据业务类型查询任务列表
     * @param businessType 业务类型
     * @return 任务列表
     */
    List<TaskCenter> selectByBusinessType(@Param("businessType") String businessType);

    /**
     * 根据业务ID查询任务列表
     * @param businessId 业务ID
     * @return 任务列表
     */
    List<TaskCenter> selectByBusinessId(@Param("businessId") Long businessId);

    /**
     * 查询待处理任务数量
     * @return 待处理任务数量
     */
    int countPendingTasks();

    /**
     * 查询今日新增任务数量
     * @return 今日新增任务数量
     */
    int countTodayTasks();

    /**
     * 根据优先级查询任务列表
     * @param priority 优先级
     * @return 任务列表
     */
    List<TaskCenter> selectByPriority(@Param("priority") Integer priority);

    /**
     * 查询未推送的任务列表
     * @return 未推送的任务列表
     */
    List<TaskCenter> selectUnpushedTasks();

    /**
     * 更新任务推送状态
     * @param taskId 任务ID
     * @param isPushed 是否已推送
     * @param pushCount 推送次数
     * @return 更新结果
     */
    int updatePushStatus(@Param("taskId") Long taskId, 
                        @Param("isPushed") Integer isPushed, 
                        @Param("pushCount") Integer pushCount);
}
