package com.dfab.businessLog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.dfab.businessLog.entity.BusinessLog;
import com.dfab.businessLog.service.BusinessLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务关键日志后台管理 Controller 类，提供业务日志信息的增删改查接口。
 */
@RestController
@RequestMapping("/admin/businessLog")
@Tag(name = "BusinessLogAdminController", description = "业务日志后台管理接口，提供对业务日志信息的增删改查功能")
public class BusinessLogAdminController {

    @Autowired
    private BusinessLogService businessLogService;

    /**
     * 根据 ID 获取业务日志信息
     * @param businessLog 包含 ID 的业务日志对象
     * @return 业务日志信息
     */
    @Operation(summary = "根据 ID 获取业务日志信息", description = "根据 ID 获取业务日志信息")
    @PostMapping("/get")
    @Log(title = "业务日志后台管理-根据 ID 获取业务日志信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public BusinessLog getById(@Parameter(description = "包含 ID 的业务日志对象", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.getById(businessLog.getId());
    }

    /**
     * 创建业务日志信息
     * @param businessLog 业务日志信息
     * @return 创建的业务日志
     */
    @Operation(summary = "创建业务日志信息", description = "创建业务日志信息")
    @PostMapping("/save")
    @Log(title = "业务日志后台管理-创建业务日志信息", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public BusinessLog save(@Parameter(description = "业务日志信息实体", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.createByAdmin(businessLog);
    }

    /**
     * 更新业务日志信息
     * @param businessLog 业务日志信息
     * @return 更新后的业务日志
     */
    @Operation(summary = "更新业务日志信息", description = "更新业务日志信息")
    @PostMapping("/update")
    @Log(title = "业务日志后台管理-更新业务日志信息", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public BusinessLog update(@Parameter(description = "业务日志信息实体", required = true) @RequestBody BusinessLog businessLog) {
        if (businessLog.getId() == null) {
            throw new RuntimeException("id不能为空");
        }
        boolean success = businessLogService.updateById(businessLog);
        if (success) {
            return businessLogService.getById(businessLog.getId());
        } else {
            throw new RuntimeException("更新业务日志失败");
        }
    }

    /**
     * 根据 ID 删除业务日志信息
     * @param businessLog 包含 ID 的业务日志对象
     * @return 是否删除成功
     */
    @Operation(summary = "根据 ID 删除业务日志信息", description = "根据 ID 删除业务日志信息")
    @PostMapping("/delete")
    @Log(title = "业务日志后台管理-删除业务日志信息", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public Boolean deleteBusinessLog(@Parameter(description = "包含 ID 的业务日志对象", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.removeById(businessLog.getId());
    }

    /**
     * 查询业务日志列表
     * @param businessLog 查询条件
     * @return 业务日志列表
     */
    @Operation(summary = "查询业务日志列表", description = "根据条件查询业务日志列表")
    @PostMapping("/list")
    @Log(title = "业务日志后台管理-查询业务日志列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public List<BusinessLog> list(@Parameter(description = "查询条件", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.list(businessLog);
    }

    /**
     * 分页查询业务日志列表
     * @param businessLog 查询条件
     * @return 分页业务日志列表
     */
    @Operation(summary = "分页查询业务日志列表", description = "根据条件分页查询业务日志列表")
    @PostMapping("/page")
    @Log(title = "业务日志后台管理-分页查询业务日志列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public IPage<BusinessLog> page(@Parameter(description = "查询条件", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.page(businessLog);
    }

    /**
     * 根据会员入住ID和类型查询业务日志列表
     * @param businessLog 查询条件，包含memberCheckinId和type
     * @return 业务日志列表
     */
    @Operation(summary = "根据会员入住ID和类型查询业务日志列表", description = "根据会员入住ID和类型查询业务日志列表，主要用于查询换房记录")
    @PostMapping("/listByMemberCheckinIdAndType")
    @Log(title = "业务日志后台管理-根据会员入住ID和类型查询业务日志列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public List<BusinessLog> listByMemberCheckinIdAndType(@Parameter(description = "查询条件", required = true) @RequestBody BusinessLog businessLog) {
        return businessLogService.getBusinessLogListByMemberCheckinIdAndType(businessLog.getMemberCheckinId(), businessLog.getType());
    }
}
