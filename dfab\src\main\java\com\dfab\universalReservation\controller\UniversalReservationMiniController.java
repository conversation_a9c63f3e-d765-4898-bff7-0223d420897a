package com.dfab.universalReservation.controller;

import com.dfab.universalReservation.entity.UniversalReservation;
import com.dfab.universalReservation.enums.ReservationTypeEnum;
import com.dfab.universalReservation.service.UniversalReservationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序通用预约控制器
 */
@RestController
@RequestMapping("/api/universalReservation")
@Tag(name = "UniversalReservationMiniController", description = "小程序通用预约接口，支持试餐、看房、用车等多种预约类型")
public class UniversalReservationMiniController {

    @Autowired
    private UniversalReservationService universalReservationService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 根据 ID 获取预约信息
     * @param reservation 包含预约ID的对象
     * @return 对应的预约信息
     */
    @Operation(summary = "根据 ID 获取预约信息", description = "通过指定的预约记录 ID 查询对应的预约信息")
    @PostMapping("/get")
    @Log(title = "小程序通用预约-根据ID获取预约信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public UniversalReservation getReservationById(@Parameter(description = "预约记录的 ID", required = true) @RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        return universalReservationService.getReservationByIdAndOpenId(reservation.getId(), openId);
    }

    /**
     * 创建新的预约信息
     * @param reservation 预约信息
     * @return 创建成功的预约信息
     */
    @Operation(summary = "创建新的预约信息", description = "将新的预约信息保存到数据库中")
    @PostMapping("/save")
    @Log(title = "小程序通用预约-创建新的预约信息", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public UniversalReservation createReservation(@Parameter(description = "预约信息", required = true) @RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        universalReservationService.createOrUpdateReservation(reservation);
        return reservation;
    }

    /**
     * 更新预约信息
     * @param reservation 新的预约信息
     * @return 更新后的预约信息
     */
    @Operation(summary = "更新预约信息", description = "根据指定的 ID 更新对应的预约信息")
    @PostMapping("/update")
    @Log(title = "小程序通用预约-更新预约信息", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public UniversalReservation updateReservation(@Parameter(description = "预约信息", required = true) @RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        universalReservationService.createOrUpdateReservation(reservation);
        return reservation;
    }

    /**
     * 删除预约
     * @param reservation 包含预约ID的对象
     */
    @Operation(summary = "删除预约", description = "根据指定的 ID 删除对应的预约信息")
    @PostMapping("/remove")
    @Log(title = "小程序通用预约-删除预约", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
    public void deleteReservation(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        universalReservationService.deleteReservationByIdAndOpenId(reservation.getId(), openId);
    }

    /**
     * 根据 openId 获取预约列表
     * @return 对应 openId 的预约列表
     */
    @Operation(summary = "根据 openId 获取预约列表", description = "通过用户的微信 openId 查询对应的预约列表")
    @PostMapping("/list")
    @Log(title = "小程序通用预约-获取预约列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<UniversalReservation> getReservationsByOpenId() {
        String openId = (String) request.getAttribute("openId");
        return universalReservationService.getReservationsByOpenId(openId);
    }

    /**
     * 根据 openId 和预约类型获取预约列表
     * @param reservation 包含预约类型的对象
     * @return 对应的预约列表
     */
    @Operation(summary = "根据预约类型获取预约列表", description = "通过用户的微信 openId 和预约类型查询对应的预约列表")
    @PostMapping("/list-by-type")
    @Log(title = "小程序通用预约-根据类型获取预约列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<UniversalReservation> getReservationsByType(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        return universalReservationService.getReservationsByOpenIdAndType(openId, reservation.getReservationType());
    }

    /**
     * 根据会员入住记录ID获取预约列表
     * @param reservation 包含会员入住记录ID的对象
     * @return 对应会员的预约列表
     */
    @Operation(summary = "根据会员入住记录ID获取预约列表", description = "通过会员入住记录ID查询对应的预约列表")
    @PostMapping("/list-by-member")
    @Log(title = "小程序通用预约-根据会员获取预约列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<UniversalReservation> getReservationsByMemberCheckinId(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        // 验证该会员入住记录是否属于当前用户
        List<UniversalReservation> reservations = universalReservationService.getReservationsByMemberCheckinId(reservation.getMemberCheckinId());
        // 过滤出属于当前用户的预约
        return reservations.stream()
                .filter(r -> openId.equals(r.getOpenId()))
                .toList();
    }

    /**
     * 取消预约
     * @param reservation 包含预约ID和取消原因的对象
     * @return 取消结果
     */
    @Operation(summary = "取消预约", description = "取消指定的预约，并记录取消原因")
    @PostMapping("/cancel")
    @Log(title = "小程序通用预约-取消预约", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public UniversalReservation cancelReservation(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        // 验证预约是否属于当前用户
        UniversalReservation existing = universalReservationService.getReservationByIdAndOpenId(reservation.getId(), openId);
        if (existing != null) {
            // 更新预约状态为已取消，并记录取消原因
            existing.setStatus(3); // 3-已取消
            existing.setCancelReason(reservation.getCancelReason());
            universalReservationService.updateById(existing);
        }
        return existing;
    }

    /**
     * 评价预约
     * @param reservation 包含预约ID、评分和评价内容的对象
     * @return 评价结果
     */
    @Operation(summary = "评价预约", description = "对已完成的预约进行评价")
    @PostMapping("/rate")
    @Log(title = "小程序通用预约-评价预约", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public UniversalReservation rateReservation(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        // 验证预约是否属于当前用户
        UniversalReservation existing = universalReservationService.getReservationByIdAndOpenId(reservation.getId(), openId);
        if (existing != null && existing.getStatus() == 2) { // 只能对已完成的预约进行评价
            universalReservationService.rateReservation(reservation.getId(), reservation.getRating(), reservation.getReview());
            existing.setRating(reservation.getRating());
            existing.setReview(reservation.getReview());
        }
        return existing;
    }

    /**
     * 获取预约类型列表
     * @return 预约类型列表
     */
    @Operation(summary = "获取预约类型列表", description = "获取所有支持的预约类型")
    @PostMapping("/types")
    @Log(title = "小程序通用预约-获取预约类型列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public Map<String, Object> getReservationTypes() {
        Map<String, Object> result = new HashMap<>();
        Map<Integer, String> types = new HashMap<>();
        for (ReservationTypeEnum type : ReservationTypeEnum.values()) {
            types.put(type.getCode(), type.getName());
        }
        result.put("types", types);
        return result;
    }

    /**
     * 获取用户预约统计
     * @return 用户预约统计信息
     */
    @Operation(summary = "获取用户预约统计", description = "获取当前用户的预约统计信息")
    @PostMapping("/my-statistics")
    @Log(title = "小程序通用预约-获取用户预约统计", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public Map<String, Object> getMyReservationStatistics() {
        String openId = (String) request.getAttribute("openId");
        List<UniversalReservation> allReservations = universalReservationService.getReservationsByOpenId(openId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", allReservations.size());
        result.put("pendingCount", allReservations.stream().filter(r -> r.getStatus() == 0).count());
        result.put("confirmedCount", allReservations.stream().filter(r -> r.getStatus() == 1).count());
        result.put("completedCount", allReservations.stream().filter(r -> r.getStatus() == 2).count());
        result.put("cancelledCount", allReservations.stream().filter(r -> r.getStatus() == 3).count());
        
        // 按类型统计
        Map<Integer, Long> typeCount = new HashMap<>();
        for (ReservationTypeEnum type : ReservationTypeEnum.values()) {
            long count = allReservations.stream().filter(r -> type.getCode().equals(r.getReservationType())).count();
            typeCount.put(type.getCode(), count);
        }
        result.put("typeCount", typeCount);
        
        return result;
    }

    /**
     * 试餐预约快捷入口
     * @param reservation 试餐预约信息
     * @return 创建成功的预约信息
     */
    @Operation(summary = "试餐预约", description = "快捷创建试餐预约")
    @PostMapping("/tasting")
    @Log(title = "小程序通用预约-试餐预约", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public UniversalReservation createTastingReservation(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        reservation.setReservationType(ReservationTypeEnum.TASTING.getCode());
        reservation.setTitle("试餐预约");
        universalReservationService.createOrUpdateReservation(reservation);
        return reservation;
    }

    /**
     * 看房预约快捷入口
     * @param reservation 看房预约信息
     * @return 创建成功的预约信息
     */
    @Operation(summary = "看房预约", description = "快捷创建看房预约")
    @PostMapping("/room-viewing")
    @Log(title = "小程序通用预约-看房预约", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public UniversalReservation createRoomViewingReservation(@RequestBody UniversalReservation reservation) {
        String openId = (String) request.getAttribute("openId");
        reservation.setOpenId(openId);
        reservation.setReservationType(ReservationTypeEnum.ROOM_VIEWING.getCode());
        reservation.setTitle("看房预约");
        universalReservationService.createOrUpdateReservation(reservation);
        return reservation;
    }


}
