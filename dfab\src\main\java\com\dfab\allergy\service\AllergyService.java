package com.dfab.allergy.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.allergy.entity.Allergy;

import java.util.List;

/**
 * 用户忌口 Service 接口。
 */
public interface AllergyService extends IService<Allergy> {

    Allergy getAllergyByOpenId(String openId);

    List<Allergy> getAllergyListByOpenId(String openId);

    Allergy create(Allergy allergy);

    Allergy createByAdmin(Allergy allergy);

    Boolean removeByOpenId(String openId);

    /**
     * 根据手机号获取忌口信息
     * @param phoneNumber 手机号
     * @return 忌口信息
     */
    Allergy getAllergyByPhoneNumber(String phoneNumber);

    /**
     * 根据手机号或openId获取忌口信息
     * @param phoneNumber 手机号
     * @param openId 微信openId
     * @return 忌口信息
     */
    Allergy getAllergyByPhoneNumberOrOpenId(String phoneNumber, String openId);

    Allergy getAllergyByMemberId(Long memberId);

    IPage<Allergy> page(Allergy allergy);

    List<Allergy> list(Allergy allergy);

//    List<Allergy> boardList();

    /**
     * 添加投诉记录
     * @param allergyId 忌口ID
     * @param complaintContent 投诉内容
     * @return 是否成功
     */
    Boolean addComplaint(Long allergyId, String complaintContent);

    /**
     * 获取投诉记录列表
     * @param allergyId 忌口ID
     * @return 投诉记录列表
     */
    List<com.dfab.businessLog.entity.BusinessLog> getComplaintList(Long allergyId);
}