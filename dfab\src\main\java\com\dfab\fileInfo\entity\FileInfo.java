package com.dfab.fileInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import lombok.Data;

@Data
@TableName("file_info")
public class FileInfo extends BaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private String fileName;
    private String filePath;
    private Long fileSize;
    private String visualFileSize;
    private String fileType;
    private String bucket;
}