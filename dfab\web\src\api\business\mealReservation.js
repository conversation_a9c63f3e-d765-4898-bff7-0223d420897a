import request from '@/utils/request'

// 查询餐食预约列表
export function listMealReservation(query) {
  return request({
    url: '/admin/mealReservation/page',
    method: 'post',
    data: query
  })
}

// 查询餐食预约列表
export function allListMealReservation(query) {
  return request({
    url: '/admin/mealReservation/list',
    method: 'post',
    data: query
  })
}

// 查询餐食预约详细
export function getMealReservation(id) {
  return request({
    url: '/admin/mealReservation/' + id,
    method: 'post'
  })
}

// 添加餐食预约
export function addMealReservation(data) {
  return request({
    url: '/admin/mealReservation/add',
    method: 'post',
    data: data
  })
}

// 修改餐食预约
export function updateMealReservation(data) {
  return request({
    url: '/admin/mealReservation/update',
    method: 'post',
    data: data
  })
}

// 删除餐食预约
export function delMealReservation(id) {
  return request({
    url: '/admin/mealReservation/delete',
    method: 'post',
    data: { id }
  })
}

// 导出餐食预约
export function exportMealReservation(query) {
  return request({
    url: '/admin/mealReservation/export',
    method: 'get',
    params: query
  })
} 