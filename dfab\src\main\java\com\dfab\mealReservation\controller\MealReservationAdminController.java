package com.dfab.mealReservation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dfab.mealReservation.entity.MealReservation;
import com.dfab.mealReservation.service.MealReservationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 餐食预约后台管理 Controller 类。
 */
@RestController
@RequestMapping("/admin/mealReservation")
@Tag(name = "MealReservationAdminController", description = "餐食预约后台管理接口")
public class MealReservationAdminController {

    @Autowired
    private MealReservationService mealReservationService;

    /**
     * 分页查询餐食预约列表
     * @param reservation 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询餐食预约列表", description = "根据条件分页查询餐食预约列表数据")
//    @Log(title = "餐食预约后台管理-分页查询餐食预约列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/page")
    public Map<String, Object> page(@RequestBody MealReservation reservation) {
        IPage<MealReservation> page = mealReservationService.page(reservation);
        Map<String, Object> result = new HashMap<>();
        result.put("rows", page.getRecords());
        result.put("total", page.getTotal());

        mealReservationService.getSum(reservation).forEach((k, v) -> {
            result.put(k, v);
        });

        return result;
    }

    /**
     * 分页查询餐食预约列表
     * @param reservation 查询条件
     * @return 分页结果
     */
    @Operation(summary = "查询餐食预约列表", description = "根据条件查询餐食预约列表数据")
//    @Log(title = "餐食预约后台管理-查询餐食预约列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/list")
    public List<MealReservation> list(@RequestBody MealReservation reservation) {
        //过滤已退住数据
        return mealReservationService.listOnlyIn(reservation);
    }

    /**
     * 根据 ID 获取餐食预约信息
     * @param id 餐食预约记录的 ID
     * @return 对应的餐食预约信息
     */
    @Operation(summary = "根据 ID 获取餐食预约信息", description = "通过指定的餐食预约记录 ID 查询对应的预约信息")
    @Log(title = "餐食预约后台管理-根据 ID 获取餐食预约信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/{id}")
    public Map<String, Object> getReservationById(@Parameter(description = "餐食预约记录的 ID", required = true) @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", mealReservationService.getById(id));
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }
    
    /**
     * 添加餐食预约信息
     */
    @Operation(summary = "添加餐食预约信息", description = "添加新的餐食预约记录")
    @Log(title = "餐食预约后台管理-添加餐食预约信息", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody MealReservation reservation) {
        mealReservationService.addByAdmin(reservation);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "操作成功");
        return result;
    }
    
    /**
     * 根据 ID 修改餐食预约信息
     */
    @Operation(summary = "根据 ID 修改餐食预约信息", description = "通过指定的餐食预约记录 ID 修改对应的预约信息")
    @Log(title = "餐食预约后台管理-根据 ID 修改餐食预约信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping("/update")
    public Map<String, Object> updateReservationById(@Parameter(description = "餐食预约对象", required = true) @RequestBody MealReservation reservation) {
        Boolean result = mealReservationService.updateByAdmin(reservation);
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "操作成功");
        return response;
    }
    
    /**
     * 删除餐食预约信息
     */
    @Operation(summary = "删除餐食预约信息", description = "根据ID删除餐食预约信息，支持批量删除")
    @Log(title = "餐食预约后台管理-根据 ID 获取餐食预约信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @PostMapping("/delete")
    public Map<String, Object> delete(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 打印请求参数，便于调试
            System.out.println("删除请求参数: " + params);
            
            Object idObj = params.get("id");
            
            if (idObj == null) {
                throw new IllegalArgumentException("id参数不能为空");
            }
            
            if (idObj instanceof List) {
                // 批量删除 - 需要处理数值类型的转换
                List<Long> ids = new ArrayList<>();
                List<?> rawIds = (List<?>) idObj;
                
                for (Object item : rawIds) {
                    if (item instanceof Integer) {
                        ids.add(((Integer) item).longValue());
                    } else if (item instanceof Long) {
                        ids.add((Long) item);
                    } else if (item instanceof String) {
                        try {
                            ids.add(Long.parseLong((String) item));
                        } catch (NumberFormatException e) {
                            System.out.println("无法解析ID: " + item);
                        }
                    } else if (item instanceof Number) {
                        ids.add(((Number) item).longValue());
                    }
                }
                
                if (!ids.isEmpty()) {
                    mealReservationService.removeByIds(ids);
                }
            } else {
                // 单个删除
                Long id;
                if (idObj instanceof Integer) {
                    id = ((Integer) idObj).longValue();
                } else if (idObj instanceof Long) {
                    id = (Long) idObj;
                } else if (idObj instanceof String) {
                    try {
                        id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无效的ID格式: " + idObj);
                    }
                } else if (idObj instanceof Number) {
                    id = ((Number) idObj).longValue();
                } else {
                    throw new IllegalArgumentException("无效的ID类型");
                }
                
                mealReservationService.removeById(id);
            }
            
            result.put("code", 200);
            result.put("msg", "操作成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "删除失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 导出餐食预约信息
     */
    @Operation(summary = "导出餐食预约信息", description = "导出所有餐食预约信息")
    @Log(title = "餐食预约后台管理-导出餐食预约信息", businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)
    @GetMapping("/export")
    public List<MealReservation> export(MealReservation reservation) {
        return mealReservationService.list(reservation);
    }
}