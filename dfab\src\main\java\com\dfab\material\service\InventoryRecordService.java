package com.dfab.material.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.material.dto.RecordQueryDto;
import com.dfab.material.entity.InventoryRecord;

import java.util.List;
import java.util.Map;

/**
 * 出入库记录Service接口
 */
public interface InventoryRecordService extends IService<InventoryRecord> {
    
    /**
     * 查询出入库记录列表（包含物料信息）
     * @return 出入库记录列表
     */
    List<Map<String, Object>> getRecordList(InventoryRecord queryDto);

    /**
     * 分页查询出入库记录列表（包含物料信息）
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页出入库记录列表
     */
    Page<Map<String, Object>> getRecordPage(int pageNum, int pageSize);

    /**
     * 分页查询出入库记录列表（包含物料信息，支持查询条件）
     * @param queryDto 查询条件
     * @return 分页出入库记录列表
     */
    Page<Map<String, Object>> getRecordPage(RecordQueryDto queryDto);
} 