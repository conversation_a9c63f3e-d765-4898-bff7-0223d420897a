/*
package com.dfab.admin.controller;

import com.dfab.admin.entity.Permission;
import com.dfab.admin.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/permissions")
@Tag(name = "PermissionController", description = "权限管理接口")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    @Operation(summary = "获取所有权限")
    @GetMapping
    public List<Permission> getAllPermissions() {
        return permissionService.list();
    }

    @Operation(summary = "根据 ID 获取权限")
    @GetMapping("/{id}")
    public Permission getPermissionById(@PathVariable Long id) {
        return permissionService.getById(id);
    }

    @Operation(summary = "创建新的权限")
    @PostMapping
    public Permission createPermission(@RequestBody Permission permission) {
        permissionService.save(permission);
        return permission;
    }

    @Operation(summary = "更新权限信息")
    @PutMapping("/{id}")
    public Permission updatePermission(@PathVariable Long id, @RequestBody Permission permission) {
        permission.setId(id);
        permissionService.updateById(permission);
        return permission;
    }

    @Operation(summary = "删除权限")
    @DeleteMapping("/{id}")
    public void deletePermission(@PathVariable Long id) {
        permissionService.removeById(id);
    }
}*/
