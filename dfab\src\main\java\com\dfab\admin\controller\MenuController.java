/*
package com.dfab.admin.controller;

import com.dfab.admin.entity.Menu;
import com.dfab.admin.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/menus")
@Tag(name = "MenuController", description = "菜单管理接口")
public class MenuController {

    @Autowired
    private MenuService menuService;

    @Operation(summary = "获取所有菜单")
    @GetMapping
    public List<Menu> getAllMenus() {
        return menuService.list();
    }

    @Operation(summary = "根据 ID 获取菜单")
    @GetMapping("/{id}")
    public Menu getMenuById(@PathVariable Long id) {
        return menuService.getById(id);
    }

    @Operation(summary = "创建新的菜单")
    @PostMapping
    public Menu createMenu(@RequestBody Menu menu) {
        menuService.save(menu);
        return menu;
    }

    @Operation(summary = "更新菜单信息")
    @PutMapping("/{id}")
    public Menu updateMenu(@PathVariable Long id, @RequestBody Menu menu) {
        menu.setId(id);
        menuService.updateById(menu);
        return menu;
    }

    @Operation(summary = "删除菜单")
    @DeleteMapping("/{id}")
    public void deleteMenu(@PathVariable Long id) {
        menuService.removeById(id);
    }
}*/
