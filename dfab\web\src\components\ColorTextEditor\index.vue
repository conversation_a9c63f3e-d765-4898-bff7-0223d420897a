<template>
  <div class="color-text-editor">
    <!-- 自定义工具栏 -->
    <div class="custom-toolbar">
      <div class="color-picker-wrapper">
        <span class="toolbar-label">字体颜色：</span>
        <div class="color-options">
          <div
            v-for="color in allowedColors"
            :key="color"
            class="color-option"
            :class="{ active: currentColor === color }"
            :style="{ backgroundColor: color }"
            :title="getColorName(color)"
            @click.stop="setTextColor(color)"
            @mousedown.prevent
          ></div>
        </div>
      </div>
      <!-- 功能描述备注 -->
      <div class="function-description">
        <span class="description-text">
          点击颜色可设置文字颜色，支持选中文字后改变颜色
        </span>
      </div>
    </div>

    <!-- 简单的可编辑div -->
    <div
      ref="editorRef"
      class="simple-editor"
      contenteditable="true"
      :style="styles"
      :data-placeholder="props.placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'

const editorRef = ref()

const props = defineProps({
  /* 编辑器的内容 */
  modelValue: {
    type: String,
    default: ''
  },
  /* 高度 */
  height: {
    type: Number,
    default: 120,
  },
  /* 最小高度 */
  minHeight: {
    type: Number,
    default: 80,
  },
  /* 只读 */
  readOnly: {
    type: Boolean,
    default: false,
  },
  /* 占位符 */
  placeholder: {
    type: String,
    default: '请输入内容'
  }
})

const emit = defineEmits(['update:modelValue'])

// 限制的颜色选项
const allowedColors = [
  '#ff0000', // 红色
  '#00aa00', // 绿色
  '#0066cc', // 蓝色
  '#333333'  // 黑色
]

// 当前选中的颜色
const currentColor = ref('#333333')

// 编辑器是否有焦点
const hasFocus = ref(false)

// 获取颜色名称
function getColorName(color) {
  const colorNames = {
    '#ff0000': '红色',
    '#00aa00': '绿色',
    '#0066cc': '蓝色',
    '#333333': '黑色'
  }
  return colorNames[color] || color
}

// 移除Quill配置，使用简单的contenteditable

const styles = computed(() => {
  let style = {}
  if (props.minHeight) {
    style.minHeight = `${props.minHeight}px`
  }
  if (props.height) {
    style.height = `${props.height}px`
  }
  return style
})

const content = ref("")

// 监听外部值变化
watch(() => props.modelValue, (v) => {
  const newValue = v || ""
  if (newValue !== content.value) {
    content.value = newValue
    // 同步到编辑器
    if (editorRef.value) {
      // 如果是纯文本，直接设置textContent
      if (!newValue.includes('<') && !newValue.includes('>')) {
        editorRef.value.textContent = newValue
      } else {
        editorRef.value.innerHTML = newValue
      }
    }
  }
}, { immediate: true })

// 处理输入变化
function handleInput(event) {
  // 使用延迟更新，避免输入过程中的乱码
  setTimeout(() => {
    if (editorRef.value) {
      const newContent = editorRef.value.innerHTML
      content.value = newContent
      emit('update:modelValue', newContent)
    }
  }, 0)
}

// 处理焦点获得
function handleFocus() {
  hasFocus.value = true
}

// 处理焦点失去
function handleBlur() {
  hasFocus.value = false
}

// 处理键盘事件
function handleKeydown(event) {
  // 处理回车键，确保换行正常
  if (event.key === 'Enter') {
    event.preventDefault()
    document.execCommand('insertHTML', false, '<br>')
    handleInput(event)
  }
}

// 清理HTML内容，避免乱码
function cleanContent(htmlContent) {
  if (!htmlContent) return ''

  // 创建临时div来处理HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent

  // 如果内容只是纯文本，直接返回文本内容
  if (tempDiv.textContent === htmlContent) {
    return htmlContent
  }

  return htmlContent
}

// 设置文字颜色
function setTextColor(color) {
  currentColor.value = color

  if (editorRef.value) {
    // 保存当前选择
    const selection = window.getSelection()
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null

    if (hasFocus.value && range && !range.collapsed) {
      // 有选中文本时，应用颜色到选中文本
      document.execCommand('styleWithCSS', false, true)
      document.execCommand('foreColor', false, color)
    } else {
      // 没有选中文本时，设置默认颜色（用于新输入的文本）
      // 先聚焦编辑器，然后设置颜色格式
      if (!hasFocus.value) {
        editorRef.value.focus()
      }
      document.execCommand('styleWithCSS', false, true)
      document.execCommand('foreColor', false, color)
    }

    // 更新内容
    content.value = editorRef.value.innerHTML
    emit('update:modelValue', content.value)
  }
}

// 组件挂载后的初始化
onMounted(() => {
  nextTick(() => {
    if (editorRef.value) {
      // 设置初始内容
      if (content.value) {
        editorRef.value.innerHTML = content.value
      }

      // 设置默认颜色
      editorRef.value.style.color = currentColor.value
    }
  })
})
</script>

<style scoped>
.color-text-editor {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

/* 自定义工具栏样式 */
.custom-toolbar {
  border-bottom: 1px solid #dcdfe6;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 4px 4px 0 0;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.toolbar-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.color-options {
  display: flex;
  gap: 6px;
}

.color-option {
  width: 24px;
  height: 24px;
  border: 2px solid #fff;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px #409eff;
}

.color-option.active {
  box-shadow: 0 0 0 2px #409eff;
  transform: scale(1.05);
}

/* 功能描述样式 */
.function-description {
  margin-top: 4px;
}

.description-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  font-style: italic;
}

/* 简单编辑器样式 */
.simple-editor {
  border: none;
  font-size: 14px;
  padding: 12px;
  min-height: 80px;
  line-height: 1.5;
  border-radius: 0 0 4px 4px;
  outline: none;
  background: #fff;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.simple-editor:empty::before {
  content: attr(data-placeholder);
  color: #c0c4cc;
  font-style: normal;
}

.simple-editor:focus {
  outline: none;
}

/* 确保编辑器聚焦时的样式 */
.color-text-editor:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 移除不需要的样式 */
</style>
