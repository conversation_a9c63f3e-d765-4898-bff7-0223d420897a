<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总任务数</span>
            <el-icon class="card-icon"><Document /></el-icon>
          </div>
          <div class="card-content">
            <div class="number">{{ statistics.totalTasks }}</div>
            <div class="desc">所有任务总数</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>待处理任务</span>
            <el-icon class="card-icon warning"><Clock /></el-icon>
          </div>
          <div class="card-content">
            <div class="number warning">{{ statistics.pendingTasks }}</div>
            <div class="desc">需要处理的任务</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>已处理任务</span>
            <el-icon class="card-icon success"><Check /></el-icon>
          </div>
          <div class="card-content">
            <div class="number success">{{ statistics.processedTasks }}</div>
            <div class="desc">已完成的任务</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>今日新增</span>
            <el-icon class="card-icon primary"><Plus /></el-icon>
          </div>
          <div class="card-content">
            <div class="number primary">{{ statistics.todayTasks }}</div>
            <div class="desc">今天新增的任务</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 任务类型分布 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div class="card-header">
            <span>任务类型分布</span>
          </div>
          <div class="chart-container">
            <div ref="taskTypeChart" style="width: 100%; height: 300px;"></div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 任务状态分布 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div class="card-header">
            <span>任务状态分布</span>
          </div>
          <div class="chart-container">
            <div ref="taskStatusChart" style="width: 100%; height: 300px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 任务详细统计表格 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div class="card-header">
            <span>任务详细统计</span>
            <el-button type="primary" size="small" @click="refreshStatistics">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <el-table :data="taskTypeData" border style="width: 100%">
            <el-table-column prop="typeName" label="任务类型" align="center" />
            <el-table-column prop="totalCount" label="总数" align="center" />
            <el-table-column prop="pendingCount" label="待处理" align="center">
              <template #default="scope">
                <el-tag type="warning">{{ scope.row.pendingCount }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="processedCount" label="已处理" align="center">
              <template #default="scope">
                <el-tag type="success">{{ scope.row.processedCount }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="completionRate" label="完成率" align="center">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.completionRate" 
                  :color="getProgressColor(scope.row.completionRate)"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="TaskCenterStatistics">
import { getTaskStatistics } from "@/api/business/taskCenter"
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance()

const statistics = ref({
  totalTasks: 0,
  pendingTasks: 0,
  processedTasks: 0,
  todayTasks: 0,
  carAddTasks: 0,
  carAddPending: 0,
  carAddProcessed: 0,
  carDeleteTasks: 0,
  carDeletePending: 0,
  carDeleteProcessed: 0,
  expectedDeliveryTasks: 0,
  expectedDeliveryPending: 0,
  expectedDeliveryProcessed: 0,
  hospitalAdmissionTasks: 0,
  hospitalAdmissionPending: 0,
  hospitalAdmissionProcessed: 0,
  dischargeTasks: 0,
  dischargePending: 0,
  dischargeProcessed: 0
})

const taskTypeData = ref([])
const taskTypeChart = ref(null)
const taskStatusChart = ref(null)

let typeChartInstance = null
let statusChartInstance = null

/** 获取统计数据 */
function getStatistics() {
  getTaskStatistics().then(response => {
    statistics.value = response
    updateTaskTypeData()
    initCharts()
  })
}

/** 更新任务类型数据 */
function updateTaskTypeData() {
  const data = statistics.value
  taskTypeData.value = [
    {
      typeName: '车牌新增',
      totalCount: data.carAddTasks,
      pendingCount: data.carAddPending,
      processedCount: data.carAddProcessed,
      completionRate: data.carAddTasks > 0 ? Math.round((data.carAddProcessed / data.carAddTasks) * 100) : 0
    },
    {
      typeName: '车牌删除',
      totalCount: data.carDeleteTasks,
      pendingCount: data.carDeletePending,
      processedCount: data.carDeleteProcessed,
      completionRate: data.carDeleteTasks > 0 ? Math.round((data.carDeleteProcessed / data.carDeleteTasks) * 100) : 0
    },
    {
      typeName: '预产期提醒',
      totalCount: data.expectedDeliveryTasks || 0,
      pendingCount: data.expectedDeliveryPending || 0,
      processedCount: data.expectedDeliveryProcessed || 0,
      completionRate: (data.expectedDeliveryTasks || 0) > 0 ? Math.round(((data.expectedDeliveryProcessed || 0) / (data.expectedDeliveryTasks || 0)) * 100) : 0
    },
    {
      typeName: '入院待产通知',
      totalCount: data.hospitalAdmissionTasks || 0,
      pendingCount: data.hospitalAdmissionPending || 0,
      processedCount: data.hospitalAdmissionProcessed || 0,
      completionRate: (data.hospitalAdmissionTasks || 0) > 0 ? Math.round(((data.hospitalAdmissionProcessed || 0) / (data.hospitalAdmissionTasks || 0)) * 100) : 0
    },
    {
      typeName: '出院手续通知',
      totalCount: data.dischargeTasks || 0,
      pendingCount: data.dischargePending || 0,
      processedCount: data.dischargeProcessed || 0,
      completionRate: (data.dischargeTasks || 0) > 0 ? Math.round(((data.dischargeProcessed || 0) / (data.dischargeTasks || 0)) * 100) : 0
    }
  ]
}

/** 初始化图表 */
function initCharts() {
  nextTick(() => {
    initTaskTypeChart()
    initTaskStatusChart()
  })
}

/** 初始化任务类型图表 */
function initTaskTypeChart() {
  if (typeChartInstance) {
    typeChartInstance.dispose()
  }
  
  typeChartInstance = echarts.init(taskTypeChart.value)
  
  const option = {
    title: {
      text: '任务类型分布',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '任务类型',
        type: 'pie',
        radius: '60%',
        data: [
          { value: statistics.value.carAddTasks, name: '车牌新增' },
          { value: statistics.value.carDeleteTasks, name: '车牌删除' },
          { value: statistics.value.expectedDeliveryTasks || 0, name: '预产期提醒' },
          { value: statistics.value.hospitalAdmissionTasks || 0, name: '入院待产通知' },
          { value: statistics.value.dischargeTasks || 0, name: '出院手续通知' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  typeChartInstance.setOption(option)
}

/** 初始化任务状态图表 */
function initTaskStatusChart() {
  if (statusChartInstance) {
    statusChartInstance.dispose()
  }
  
  statusChartInstance = echarts.init(taskStatusChart.value)
  
  const option = {
    title: {
      text: '任务状态分布',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '任务状态',
        type: 'pie',
        radius: '60%',
        data: [
          { value: statistics.value.pendingTasks, name: '待处理' },
          { value: statistics.value.processedTasks, name: '已处理' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChartInstance.setOption(option)
}

/** 获取进度条颜色 */
function getProgressColor(percentage) {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

/** 刷新统计数据 */
function refreshStatistics() {
  getStatistics()
  proxy.$modal.msgSuccess("统计数据已刷新")
}

/** 窗口大小改变时重新调整图表 */
function handleResize() {
  if (typeChartInstance) {
    typeChartInstance.resize()
  }
  if (statusChartInstance) {
    statusChartInstance.resize()
  }
}

onMounted(() => {
  getStatistics()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (typeChartInstance) {
    typeChartInstance.dispose()
  }
  if (statusChartInstance) {
    statusChartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 15px;
}

.card-icon {
  font-size: 20px;
}

.card-icon.warning {
  color: #e6a23c;
}

.card-icon.success {
  color: #67c23a;
}

.card-icon.primary {
  color: #409eff;
}

.card-content {
  text-align: center;
}

.number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.number.warning {
  color: #e6a23c;
}

.number.success {
  color: #67c23a;
}

.number.primary {
  color: #409eff;
}

.desc {
  color: #999;
  font-size: 14px;
}

.chart-container {
  padding: 10px;
}
</style>
