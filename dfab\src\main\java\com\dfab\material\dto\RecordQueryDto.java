package com.dfab.material.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 出入库记录查询条件DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "出入库记录查询条件")
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecordQueryDto {
    
    /**
     * 分页参数
     */
    @TableField(exist = false)
    @Schema(description = "页码", example = "1")
    private Integer pageNum;
    
    @TableField(exist = false)
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;
    
    /**
     * 物料名称
     */
    @Schema(description = "物料名称", example = "一次性无纺布内裤")
    private String materialName;

    /**
     * 物料id
     */
    @Schema(description = "物料id", example = "123")
    private Long materialId;
    
    /**
     * 规格型号
     */
    @Schema(description = "规格型号", example = "XXXL")
    private String specification;
    
    /**
     * 出入库类型
     */
    @Schema(description = "出入库类型，1-入库，2-出库", example = "1")
    private Integer recordType;
    
    /**
     * 数量
     */
    @Schema(description = "数量", example = "10")
    private String quantity;
    
    /**
     * 使用单位
     */
    @Schema(description = "使用单位", example = "产科")
    private String useUnit;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注信息")
    private String remark;
    
    /**
     * 操作人
     */
    @Schema(description = "操作人", example = "admin")
    private String creator;
    
    /**
     * 日期范围
     */
    @TableField(exist = false)
    @Schema(description = "日期范围", example = "[\"2025-06-01\", \"2025-06-30\"]")
    private List<String> dateRange;
}
