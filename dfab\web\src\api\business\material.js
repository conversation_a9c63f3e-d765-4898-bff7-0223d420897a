import request from '@/utils/request'

// 通用请求配置，禁用防重复提交检查
const commonConfig = {
  headers: {
    'repeatSubmit': false
  }
};

// 查询物料信息列表
export function listMaterial() {
  return request({
    url: '/admin/material/list',
    method: 'post',
    ...commonConfig
  })
}

// 分页查询物料信息
export function pageMaterial(data) {
  return request({
    url: '/admin/material/page',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 根据ID查询物料信息
export function getMaterial(data) {
  return request({
    url: '/admin/material/getById',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 新增物料信息
export function addMaterial(data) {
  return request({
    url: '/admin/material/add',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 修改物料信息
export function updateMaterial(data) {
  return request({
    url: '/admin/material/update',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 查询库存信息列表
export function listInventory() {
  return request({
    url: '/admin/material/inventory/list',
    method: 'post',
    ...commonConfig
  })
}

// 分页查询库存信息
export function pageInventory(data) {
  return request({
    url: '/admin/material/inventory/page',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 根据物料ID查询库存信息
export function getInventoryByMaterialId(materialId) {
  return request({
    url: '/admin/material/inventory/getByMaterialId',
    method: 'post',
    data: { materialId: materialId },
    ...commonConfig
  })
}

// 入库操作
export function inbound(data) {
  return request({
    url: '/admin/material/inventory/inbound',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 出库操作
export function outbound(data) {
  return request({
    url: '/admin/material/inventory/outbound',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 查询出入库记录列表
export function listRecord(data) {
  return request({
    url: '/admin/material/record/list',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 分页查询出入库记录
export function pageRecord(data) {
  return request({
    url: '/admin/material/record/page',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 删除物料信息
export function deleteMaterial(data) {
  return request({
    url: '/admin/material/delete',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 查询库存预警物料信息列表
export function warnList() {
  return request({
    url: '/admin/material/warnList',
    method: 'post',
    ...commonConfig
  })
}

// 删除库存信息
export function deleteInventory(data) {
  return request({
    url: '/admin/material/inventory/delete',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 删除出入库记录
export function deleteRecord(data) {
  return request({
    url: '/admin/material/record/delete',
    method: 'post',
    data: data,
    ...commonConfig
  })
}