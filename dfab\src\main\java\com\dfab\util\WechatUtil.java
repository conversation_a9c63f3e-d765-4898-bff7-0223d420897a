package com.dfab.util;

import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WechatUtil {

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.secret}")
    private String appsecret;

    @Resource
    private  StringRedisTemplate stringRedisTemplate;

    @Resource
    private  RestTemplate restTemplate;

    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";
    private static final String PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s";



    /**
     * 获取 access_token，优先从 Redis 中获取
     * @return access_token
     */
    public String getAccessToken() {
        /*String accessToken = stringRedisTemplate.opsForValue().get("wechat:access_token");
        if (accessToken != null) {
            return accessToken;
        }*/

        String accessToken = null;
        String url = String.format(ACCESS_TOKEN_URL, appid, appsecret);
        String responseStr = restTemplate.getForObject(url, String.class);
        JSONObject response = JSONObject.parseObject(responseStr);
        if (response != null && response.containsKey("access_token")) {
            accessToken = response.getString("access_token");
//            int expiresIn = response.getIntValue("expires_in");
//            stringRedisTemplate.opsForValue().set("wechat:access_token", accessToken, expiresIn - 200, TimeUnit.SECONDS);
        }
        log.info("accessToken:{}", accessToken);
        return accessToken;
    }

    /**
     * 根据微信 code 获取 openid 和 session_key
     * @param code 微信登录 code
     * @return 包含 openid 和 session_key 的 Map
     */
    public Map<String, String> getSessionKeyAndOpenId(String code) {
        String url = String.format(JSCODE2SESSION_URL, appid, appsecret, code);
        String responseStr = restTemplate.getForObject(url, String.class);
        JSONObject response = JSONObject.parseObject(responseStr);
        log.info("responseStr:{}", responseStr);
        Map<String, String> result = new HashMap<>();
        if (response != null) {
            if (response.containsKey("openid")) {
                result.put("openid", response.getString("openid"));
            }
            if (response.containsKey("session_key")) {
                result.put("session_key", response.getString("session_key"));
            }
        }
        return result;
    }

    /**
     * 根据加密数据和 iv 获取用户手机号
     * @param encryptedData 加密数据
     * @param iv 加密向量
     * @param code 微信登录 code
     * @return 用户手机号
     */
    /**
     * 获取用户手机号（微信新接口）
     * @param code 微信登录code
     * @return 用户手机号或null
     */
    public String getPhoneNumber(String code) {
        String accessToken = getAccessToken();
        String url = String.format(PHONE_NUMBER_URL, accessToken);
    
        JSONObject requestBody = new JSONObject();
        requestBody.put("code", code);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(requestBody.toString(), headers);
        
        try {
            String response = restTemplate.postForObject(url, request, String.class);
            JSONObject json = JSONObject.parseObject(response);
            
            if (json != null && json.getInteger("errcode") == 0) {
                return json.getJSONObject("phone_info")
                         .getString("phoneNumber");
            }
            log.error("获取手机号失败: {}", json);
        } catch (Exception e) {
            log.error("调用微信API异常", e);
        }
        return null;
    }
}