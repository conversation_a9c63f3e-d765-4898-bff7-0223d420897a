package com.dfab.config;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.util.FieldCommentReader;
import jakarta.annotation.PostConstruct;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;


import java.lang.reflect.Field;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Configuration
public class DatabaseInitConfig {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @PostConstruct
    public void initDatabase() {
        try (SqlSession sqlSession = sqlSessionFactory.openSession();
             Connection connection = sqlSession.getConnection();
             Statement statement = connection.createStatement()) {

            Reflections reflections = new Reflections("com");
            Set<Class<?>> entityClasses = reflections.getTypesAnnotatedWith(TableName.class);

            for (Class<?> entityClass : entityClasses) {
                TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
                String tableName;
                if (tableNameAnnotation != null && !tableNameAnnotation.value().isEmpty()) {
                    tableName = tableNameAnnotation.value();
                } else {
                    // 默认驼峰转下划线
                    tableName = convertToUnderline(entityClass.getSimpleName());
                }

                // 构建源码路径并读取字段注释
                String basePath = "src/main/java"; // 可以通过配置读取
                String packagePath = entityClass.getPackage().getName().replace(".", "/");
                String className = entityClass.getSimpleName() + ".java";
                String classFilePath = Paths.get(basePath, packagePath, className).toString();

                Map<String, String> fieldComments = new HashMap<>();
                try {
                    fieldComments = FieldCommentReader.getFieldComments(classFilePath);
                } catch (Exception e) {
                    System.err.println("无法读取字段注释：" + classFilePath);
                    e.printStackTrace();
                }

                if (!isTableExists(connection, tableName)) {
                    createTable(statement, entityClass, tableName, fieldComments);
                } else {
                    checkAndAddColumns(statement, entityClass, tableName, fieldComments);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isTableExists(Connection connection, String tableName) throws Exception {
        ResultSet resultSet = connection.getMetaData().getTables(null, null, tableName, null);
        return resultSet.next();
    }

    private void appendComment(StringBuilder sql, Field field, Map<String, String> fieldComments) {
        String comment = fieldComments.getOrDefault(field.getName(), "");
        if (!comment.isEmpty()) {
            sql.append(" COMMENT '").append(comment).append("'");
        }
    }

    private void createTable(Statement statement, Class<?> entityClass, String tableName, Map<String, String> fieldComments) throws Exception {
        StringBuilder sql = new StringBuilder("CREATE TABLE IF NOT EXISTS `" + tableName + "` (");
        Field[] fields = getAllFields(entityClass);
        boolean hasPrimaryKey = false;

        for (Field field : fields) {
            String columnName;
            TableId tableId = field.getAnnotation(TableId.class);
            TableField tableField = field.getAnnotation(TableField.class);

            // 跳过 @TableField(exist = false) 的字段
            if (tableField != null && !tableField.exist()) {
                continue;
            }

            if (tableId != null) {
                columnName = tableId.value().isEmpty() ? convertToUnderline(field.getName()) : tableId.value();
                sql.append("`").append(columnName).append("` BIGINT PRIMARY KEY ");
                appendComment(sql, field, fieldComments);
                sql.append(", ");
                hasPrimaryKey = true;
            } else {
                columnName = tableField != null && !tableField.value().isEmpty() ? tableField.value() : convertToUnderline(field.getName());
                String fieldType = getFieldType(field.getType());
                sql.append("`").append(columnName).append("` ").append(fieldType);
                appendComment(sql, field, fieldComments);
                sql.append(", ");
            }
        }

        if (sql.length() > 0) {
            sql.delete(sql.length() - 2, sql.length());
        }

        if (!hasPrimaryKey) {
            System.err.println("表 " + tableName + " 没有主键，请检查实体类！");
        }

        sql.append(")");
        statement.executeUpdate(sql.toString());
    }

    private void checkAndAddColumns(Statement statement, Class<?> entityClass, String tableName, Map<String, String> fieldComments) throws Exception {
        Field[] fields = getAllFields(entityClass);

        for (Field field : fields) {
            String columnName;
            TableId tableId = field.getAnnotation(TableId.class);
            TableField tableField = field.getAnnotation(TableField.class);

            // 跳过 @TableField(exist = false) 的字段
            if (tableField != null && !tableField.exist()) {
                continue;
            }

            if (tableId != null) {
                columnName = tableId.value().isEmpty() ? convertToUnderline(field.getName()) : tableId.value();
            } else {
                columnName = tableField != null && !tableField.value().isEmpty() ? tableField.value() : convertToUnderline(field.getName());
            }

            if (!isColumnExists(statement, tableName, columnName)) {
                String fieldType = getFieldType(field.getType());
                StringBuilder sql = new StringBuilder("ALTER TABLE `" + tableName + "` ADD COLUMN `" + columnName + "` " + fieldType);

                String comment = fieldComments.getOrDefault(field.getName(), "");
                if (!comment.isEmpty()) {
                    sql.append(" COMMENT '").append(comment).append("'");
                }

                sql.append(";");
                statement.executeUpdate(sql.toString());
            }
        }
    }

    private boolean isColumnExists(Statement statement, String tableName, String columnName) throws Exception {
        ResultSet resultSet = statement.executeQuery("SHOW COLUMNS FROM `" + tableName + "` LIKE '" + columnName + "'");
        return resultSet.next();
    }

    private String getFieldType(Class<?> fieldType) {
        if (fieldType == String.class) {
            return "VARCHAR(255)";
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return "INT";
        } else if (fieldType == Long.class || fieldType == long.class) {
            return "BIGINT";
        } else if (fieldType == Double.class || fieldType == double.class) {
            return "DOUBLE";
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            return "TINYINT(1)";
        } else if (fieldType == Date.class) {
            return "DATETIME";
        }else if (fieldType == LocalDate.class) {
            return "DATE";
        } else {
            return "VARCHAR(255)";
        }
    }

    // 工具方法：驼峰命名转下划线命名
    private String convertToUnderline(String name) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (Character.isUpperCase(c)) {
                result.append("_").append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString().toLowerCase();
    }

    // 新增方法，用于获取所有字段，包括父类的字段
    private Field[] getAllFields(Class<?> clazz) {
        java.util.List<Field> fieldList = new java.util.ArrayList<>();
        while (clazz != null) {
            java.util.Collections.addAll(fieldList, clazz.getDeclaredFields());
            clazz = clazz.getSuperclass();
        }
        return fieldList.toArray(new Field[0]);
    }
}