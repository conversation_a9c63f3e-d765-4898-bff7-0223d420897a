<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dfab.taskCenter.mapper.PushRecordMapper">

    <resultMap type="com.dfab.taskCenter.entity.PushRecord" id="PushRecordResult">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="pushType" column="push_type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="receiverOpenId" column="receiver_open_id"/>
        <result property="status" column="status"/>
        <result property="pushTime" column="push_time"/>
        <result property="response" column="response"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="retryCount" column="retry_count"/>
        <result property="templateId" column="template_id"/>
        <result property="pagePath" column="page_path"/>
        <result property="pushData" column="push_data"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteStatus" column="delete_status"/>
    </resultMap>

    <sql id="selectPushRecordVo">
        select id, task_id, push_type, title, content, receiver_open_id, status, 
               push_time, response, error_msg, retry_count, template_id, page_path, 
               push_data, create_time, update_time, delete_status
        from push_record
    </sql>

    <select id="selectByTaskId" parameterType="Long" resultMap="PushRecordResult">
        <include refid="selectPushRecordVo"/>
        where delete_status = 0 and task_id = #{taskId}
        order by push_time desc
    </select>

    <select id="selectByStatus" parameterType="Integer" resultMap="PushRecordResult">
        <include refid="selectPushRecordVo"/>
        where delete_status = 0 and status = #{status}
        order by push_time desc
    </select>

    <select id="selectByReceiverOpenId" parameterType="String" resultMap="PushRecordResult">
        <include refid="selectPushRecordVo"/>
        where delete_status = 0 and receiver_open_id = #{receiverOpenId}
        order by push_time desc
    </select>

    <select id="selectFailedRecords" resultMap="PushRecordResult">
        <include refid="selectPushRecordVo"/>
        where delete_status = 0 and status = 0
        order by push_time desc
    </select>

    <select id="selectRetryRecords" parameterType="Integer" resultMap="PushRecordResult">
        <include refid="selectPushRecordVo"/>
        where delete_status = 0 and status = 0 and retry_count &lt; #{maxRetryCount}
        order by push_time asc
    </select>

    <select id="countTodayPushes" resultType="int">
        select count(*) from push_record 
        where delete_status = 0 and DATE(push_time) = CURDATE()
    </select>

    <select id="calculateSuccessRate" resultType="Double">
        select 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END as success_rate
        from push_record 
        where delete_status = 0
    </select>

    <select id="countByPushType" parameterType="Integer" resultType="int">
        select count(*) from push_record 
        where delete_status = 0 and push_type = #{pushType}
    </select>

</mapper>
