package com.dfab.taskCenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.taskCenter.entity.TaskCenter;
import com.dfab.taskCenter.mapper.TaskCenterMapper;
import com.dfab.taskCenter.service.TaskCenterService;
import com.dfab.taskCenter.service.WechatPushService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 任务中心Service实现类
 */
@Slf4j
@Service
public class TaskCenterServiceImpl extends ServiceImpl<TaskCenterMapper, TaskCenter> implements TaskCenterService {

    @Autowired
    private WechatPushService wechatPushService;

    @Override
    @Transactional
    public TaskCenter createTask(Integer taskType, String title, String description, String keyInfo,
                                Long businessId, String businessType, String customerName, String customerPhone,
                                String openId, Long userId, String extendInfo, Long memberCheckinId) {
        TaskCenter task = new TaskCenter();
        task.setTitle(title);
        task.setTaskType(taskType);
        task.setStatus(0); // 待处理
        task.setDescription(description);
        task.setKeyInfo(keyInfo);
        task.setBusinessId(businessId);
        task.setBusinessType(businessType);
        task.setCustomerName(customerName);
        task.setCustomerPhone(customerPhone);
        task.setOpenId(openId);
        task.setUserId(userId);
        task.setExtendInfo(extendInfo);
        task.setMemberCheckinId(memberCheckinId);
        task.setPriority(2); // 中等优先级

        save(task);

        // 异步推送通知
        try {
            wechatPushService.pushTaskNotification(task, 1); // 任务创建通知
        } catch (Exception e) {
            log.error("推送任务通知失败: {}", e.getMessage(), e);
        }

        return task;
    }

    @Override
    @Transactional
    public TaskCenter createCarAddTask(Long carInfoId, String plateNumber, String ownerName,
                                      String ownerPhone, String openId, Long userId) {
        String title = "车牌新增处理";
        String description = String.format("会员%s新增车牌%s", ownerName, plateNumber);

        return createTask(1, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, null);
    }

    @Override
    @Transactional
    public TaskCenter createCarAddTaskWithMember(Long carInfoId, String plateNumber, String ownerName,
                                                String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName) {
        String title = "车牌新增处理";
        String description = String.format("会员%s新增车牌%s，请处理", memberName != null ? memberName : ownerName, plateNumber);

        return createTask(1, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, memberCheckinId);
    }

    @Override
    @Transactional
    public TaskCenter createCarDeleteTask(Long carInfoId, String plateNumber, String ownerName,
                                         String ownerPhone, String openId, Long userId) {
        String title = "车牌删除处理";
        String description = String.format("会员%s删除车牌%s", ownerName, plateNumber);

        return createTask(2, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, null);
    }

    @Override
    @Transactional
    public TaskCenter createCarDeleteTaskWithMember(Long carInfoId, String plateNumber, String ownerName,
                                                   String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName) {
        String title = "车牌删除处理";
        String description = String.format("会员%s已退住删除车牌%s", memberName != null ? memberName : ownerName, plateNumber);

        return createTask(2, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, memberCheckinId);
    }

    @Override
    @Transactional
    public TaskCenter createCarDeleteTaskForDeletedMember(Long carInfoId, String plateNumber, String ownerName,
                                                         String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName) {
        String title = "车牌删除处理";
        String description = String.format("会员%s已删除删除车牌%s", memberName != null ? memberName : ownerName, plateNumber);

        return createTask(2, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, memberCheckinId);
    }

    @Override
    @Transactional
    public TaskCenter createCarDeleteTaskByUser(Long carInfoId, String plateNumber, String ownerName,
                                               String ownerPhone, String openId, Long userId, Long memberCheckinId, String memberName) {
        String title = "车牌删除处理";
        String description = String.format("会员%s删除车牌%s", memberName != null ? memberName : ownerName, plateNumber);

        return createTask(2, title, description, plateNumber, carInfoId, "car",
                         ownerName, ownerPhone, openId, userId, null, memberCheckinId);
    }

    @Override
    @Transactional
    public TaskCenter createLeaveTask(Long carInfoId, String plateNumber, String ownerName,
                                     String ownerPhone, String openId, Long userId) {
        String title = "离所处理";
        String description = String.format("会员%s办理离所手续，车牌%s需要处理", ownerName, plateNumber);

        TaskCenter task = createTask(3, title, description, plateNumber, carInfoId, "car",
                                   ownerName, ownerPhone, openId, userId, null, null);

        // 离所任务设置为高优先级
        task.setPriority(3);
        updateById(task);

        return task;
    }

    @Override
    @Transactional
    public TaskCenter createExpectedDeliveryReminderTask(Long memberCheckinId, String memberName, String memberPhone,
                                                        String openId, Long userId, String expectedDeliveryDate) {
        String title = "客户临近预产期提醒";
        String description = String.format("会员%s预产期为%s，即将临近，请及时关注", memberName, expectedDeliveryDate);

        TaskCenter task = createTask(7, title, description, expectedDeliveryDate, memberCheckinId, "member",
                                   memberName, memberPhone, openId, userId, null, memberCheckinId);

        // 预产期提醒设置为高优先级
        task.setPriority(3);
        updateById(task);

        return task;
    }

    @Override
    @Transactional
    public TaskCenter createHospitalAdmissionNotificationTask(Long memberCheckinId, String memberName, String memberPhone,
                                                             String openId, Long userId, String hospitalAdmissionDate) {
        String title = "入院待产通知";
        String description = String.format("会员%s入院待产日期为%s，即将到来，请做好相关准备", memberName, hospitalAdmissionDate);

        TaskCenter task = createTask(8, title, description, hospitalAdmissionDate, memberCheckinId, "member",
                                   memberName, memberPhone, openId, userId, null, memberCheckinId);

        // 入院待产通知设置为高优先级
        task.setPriority(3);
        updateById(task);

        return task;
    }

    @Override
    @Transactional
    public TaskCenter createDischargeNotificationTask(Long memberCheckinId, String memberName, String memberPhone,
                                                     String openId, Long userId, String dischargeDate) {
        String title = "出院手续办理通知";
        String description = String.format("会员%s出院日期为%s，即将到来，请协助办理出院手续", memberName, dischargeDate);

        TaskCenter task = createTask(9, title, description, dischargeDate, memberCheckinId, "member",
                                   memberName, memberPhone, openId, userId, null, memberCheckinId);

        // 出院手续办理通知设置为高优先级
        task.setPriority(3);
        updateById(task);

        return task;
    }

    @Override
    @Transactional
    public boolean processTask(Long taskId, String processRemark) {
        TaskCenter task = getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        if (task.getStatus() == 1) {
            throw new RuntimeException("任务已处理");
        }

        // 获取当前登录用户作为处理人员
        String processor = getCurrentLoginUser();

        // 只更新处理相关的字段，避免误更新其他业务字段
        TaskCenter updateTask = new TaskCenter();
        updateTask.setId(taskId);
        updateTask.setStatus(1); // 已处理
        updateTask.setProcessor(processor);
        updateTask.setProcessRemark(processRemark);
        updateTask.setProcessTime(new Date());

        boolean result = updateById(updateTask);

        if (result) {
            // 重新获取完整的任务信息用于推送
            task = getById(taskId);
            // 异步推送完成通知给用户
            try {
//                wechatPushService.pushTaskCompletionNotification(task);
            } catch (Exception e) {
                log.error("推送任务完成通知失败: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public List<TaskCenter> getTasksByType(Integer taskType) {
        return baseMapper.selectByTaskType(taskType);
    }

    @Override
    public List<TaskCenter> getTasksByStatus(Integer status) {
        return baseMapper.selectByStatus(status);
    }

    @Override
    public List<TaskCenter> getTasksByUserId(Long userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    public List<TaskCenter> getTasksByOpenId(String openId) {
        LambdaQueryWrapper<TaskCenter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskCenter::getOpenId, openId);
        wrapper.orderByDesc(TaskCenter::getCreateTime);
        return list(wrapper);
    }

    @Override
    public PageInfo<TaskCenter> getTasksPage(int pageNum, int pageSize, TaskCenter queryParams) {
        // 使用MyBatis-Plus的分页插件
        Page<TaskCenter> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<TaskCenter> wrapper = new LambdaQueryWrapper<>();

        if (queryParams != null) {
            // 任务类型
            if (queryParams.getTaskType() != null) {
                wrapper.eq(TaskCenter::getTaskType, queryParams.getTaskType());
            }
            // 任务状态
            if (queryParams.getStatus() != null) {
                wrapper.eq(TaskCenter::getStatus, queryParams.getStatus());
            }
            // 关键信息
            if (StringUtils.hasText(queryParams.getKeyInfo())) {
                wrapper.like(TaskCenter::getKeyInfo, queryParams.getKeyInfo());
            }
            // 业务类型
            if (StringUtils.hasText(queryParams.getBusinessType())) {
                wrapper.eq(TaskCenter::getBusinessType, queryParams.getBusinessType());
            }
            // 客户姓名
            if (StringUtils.hasText(queryParams.getCustomerName())) {
                wrapper.like(TaskCenter::getCustomerName, queryParams.getCustomerName());
            }
            // 客户电话
            if (StringUtils.hasText(queryParams.getCustomerPhone())) {
                wrapper.like(TaskCenter::getCustomerPhone, queryParams.getCustomerPhone());
            }
            // 处理人员
            if (StringUtils.hasText(queryParams.getProcessor())) {
                wrapper.like(TaskCenter::getProcessor, queryParams.getProcessor());
            }
            // 优先级
            if (queryParams.getPriority() != null) {
                wrapper.eq(TaskCenter::getPriority, queryParams.getPriority());
            }
            // 用户ID
            if (queryParams.getUserId() != null) {
                wrapper.eq(TaskCenter::getUserId, queryParams.getUserId());
            }
            // 微信openId
            if (StringUtils.hasText(queryParams.getOpenId())) {
                wrapper.eq(TaskCenter::getOpenId, queryParams.getOpenId());
            }
        }

        wrapper.orderByDesc(TaskCenter::getCreateTime);

        // 使用MyBatis-Plus的分页查询
        Page<TaskCenter> resultPage = page(page, wrapper);

        // 转换为PageInfo格式以保持接口兼容性
        PageInfo<TaskCenter> pageInfo = new PageInfo<>();
        pageInfo.setList(resultPage.getRecords());
        pageInfo.setTotal(resultPage.getTotal());
        pageInfo.setPageNum((int) resultPage.getCurrent());
        pageInfo.setPageSize((int) resultPage.getSize());
        pageInfo.setPages((int) resultPage.getPages());
        pageInfo.setHasNextPage(resultPage.hasNext());
        pageInfo.setHasPreviousPage(resultPage.hasPrevious());

        return pageInfo;
    }

    @Override
    public int getPendingTaskCount() {
        return baseMapper.countPendingTasks();
    }

    @Override
    public int getTodayTaskCount() {
        return baseMapper.countTodayTasks();
    }

    @Override
    public List<TaskCenter> getTasksByKeyInfo(String keyInfo) {
        LambdaQueryWrapper<TaskCenter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskCenter::getKeyInfo, keyInfo);
        wrapper.orderByDesc(TaskCenter::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<TaskCenter> getTasksByBusinessType(String businessType) {
        LambdaQueryWrapper<TaskCenter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskCenter::getBusinessType, businessType);
        wrapper.orderByDesc(TaskCenter::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<TaskCenter> getTasksByBusinessId(Long businessId) {
        LambdaQueryWrapper<TaskCenter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskCenter::getBusinessId, businessId);
        wrapper.orderByDesc(TaskCenter::getCreateTime);
        return list(wrapper);
    }

    @Override
    public boolean updateTaskPriority(Long taskId, Integer priority) {
        // 只更新优先级字段，避免误更新其他业务字段
        TaskCenter updateTask = new TaskCenter();
        updateTask.setId(taskId);
        updateTask.setPriority(priority);
        return updateById(updateTask);
    }

    @Override
    @Transactional
    public boolean batchProcessTasks(List<Long> taskIds, String processRemark) {
        boolean allSuccess = true;
        for (Long taskId : taskIds) {
            try {
                processTask(taskId, processRemark);
            } catch (Exception e) {
                log.error("批量处理任务失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    @Override
    public boolean updateTaskBasicInfo(Long taskId, String title, String description, String keyInfo, Integer priority) {
        // 只更新基本信息字段，避免误更新其他业务字段
        TaskCenter updateTask = new TaskCenter();
        updateTask.setId(taskId);
        if (StringUtils.hasText(title)) {
            updateTask.setTitle(title);
        }
        if (StringUtils.hasText(description)) {
            updateTask.setDescription(description);
        }
        if (StringUtils.hasText(keyInfo)) {
            updateTask.setKeyInfo(keyInfo);
        }
        if (priority != null) {
            updateTask.setPriority(priority);
        }
        return updateById(updateTask);
    }

    @Override
    public boolean updateTaskCustomerInfo(Long taskId, String customerName, String customerPhone) {
        // 只更新客户信息字段，避免误更新其他业务字段
        TaskCenter updateTask = new TaskCenter();
        updateTask.setId(taskId);
        if (StringUtils.hasText(customerName)) {
            updateTask.setCustomerName(customerName);
        }
        if (StringUtils.hasText(customerPhone)) {
            updateTask.setCustomerPhone(customerPhone);
        }
        return updateById(updateTask);
    }

    @Override
    public boolean updateTaskExtendInfo(Long taskId, String extendInfo) {
        // 只更新扩展信息字段，避免误更新其他业务字段
        TaskCenter updateTask = new TaskCenter();
        updateTask.setId(taskId);
        updateTask.setExtendInfo(extendInfo);
        return updateById(updateTask);
    }

    /**
     * 获取当前登录用户
     * @return 当前登录用户名
     */
    private String getCurrentLoginUser() {
        try {
            return SecurityUtils.getLoginUser().getUser().getNickName();

        } catch (Exception e) {
            log.warn("获取当前登录用户失败: {}", e.getMessage());
        }
        return "系统";
    }
}
