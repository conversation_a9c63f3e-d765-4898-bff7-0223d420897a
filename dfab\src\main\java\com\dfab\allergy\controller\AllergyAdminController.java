package com.dfab.allergy.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dfab.allergy.entity.Allergy;
import com.dfab.allergy.service.AllergyService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户忌口 Controller 类，提供用户忌口信息的增删改查接口。
 */
@RestController
@RequestMapping("/admin/allergy")
@Tag(name = "AllergyController", description = "用户忌口管理接口，提供后台对用户忌口信息的增删改查功能")
public class AllergyAdminController {

    @Autowired
    private AllergyService allergyService;



    @Operation(summary = "根据 id 获取用户忌口信息", description = "根据 id 获取用户忌口信息")
    @PostMapping("/get")
    @Log(title = "用户忌口-根据 id 获取用户忌口信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public Allergy getById(@RequestBody Allergy allergy) {
        return allergyService.getById(allergy.getId());
    }

    @Operation(summary = "创建用户忌口信息", description = "创建用户忌口信息")
    @Log(title = "用户忌口-创建用户忌口信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping("/save")
    public Allergy save(@Parameter(description = "用户忌口信息实体", required = true) @RequestBody Allergy allergy) {
        return allergyService.createByAdmin(allergy);
    }


    @Operation(summary = "根据 id 删除用户忌口信息", description = "根据 id 删除用户忌口信息")
    @Log(title = "用户忌口-删除用户忌口信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @PostMapping("/delete")
    public Boolean deleteAllergy(@RequestBody Allergy allergy) {
        return allergyService.removeById(allergy.getId());
    }

    @Operation(summary = "根据 id修改用户忌口信息", description = "id修改用户忌口信息")
    @Log(title = "用户忌口-id修改用户忌口信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping("/update")
    public Boolean updateAllergy(@RequestBody Allergy allergy) {
        return allergyService.updateById(Allergy.builder().id(allergy.getId()).allergyDetail(allergy.getAllergyDetail()).build());
    }

    @Operation(summary = "用户忌口-分页查询用户忌口列表", description = "分页查询用户忌口列表")
//    @Log(title = "用户忌口-分页查询用户忌口列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/page")
    public Map<String, Object> page(@RequestBody Allergy reservation) {
        IPage<Allergy> page = allergyService.page(reservation);
        Map<String, Object> result = new HashMap<>();
        result.put("rows", page.getRecords());
        result.put("total", page.getTotal());
        return result;
    }

  /*  @Operation(summary = "用户忌口-厨房看板查询忌口列表", description = "厨房看板查询忌口列表")
//    @Log(title = "用户忌口-分页查询用户忌口列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/boardList")
    public List<Allergy> boardList() {
        List<Allergy> list = allergyService.boardList();
        return list;
    }*/

    @Operation(summary = "用户忌口-查询忌口列表", description = "查询忌口列表")
//    @Log(title = "用户忌口-分页查询用户忌口列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    @PostMapping("/list")
    public List<Allergy> list(@RequestBody Allergy allergy) {
        List<Allergy> list = allergyService.list(allergy);
        return list;
    }

    /**
     * 添加投诉记录
     * @param requestData 包含allergyId和complaintContent的请求数据
     * @return 是否成功
     */
    @Operation(summary = "添加投诉记录", description = "为指定的忌口记录添加投诉")
    @Log(title = "用户忌口-添加投诉记录", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    @PostMapping("/addComplaint")
    public Boolean addComplaint(@Parameter(description = "投诉信息", required = true) @RequestBody Map<String, Object> requestData) {
        Long allergyId = Long.valueOf(requestData.get("allergyId").toString());
        String complaintContent = requestData.get("complaintContent").toString();
        return allergyService.addComplaint(allergyId, complaintContent);
    }

    /**
     * 获取投诉记录列表
     * @param requestData 包含allergyId的请求数据
     * @return 投诉记录列表
     */
    @Operation(summary = "获取投诉记录列表", description = "获取指定忌口的投诉记录列表")
//    @Log(title = "用户忌口-获取投诉记录列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    @PostMapping("/getComplaintList")
    public List<com.dfab.businessLog.entity.BusinessLog> getComplaintList(@Parameter(description = "查询条件", required = true) @RequestBody Map<String, Object> requestData) {
        Long allergyId = Long.valueOf(requestData.get("allergyId").toString());
        return allergyService.getComplaintList(allergyId);
    }
}