package com.dfab.carInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 车辆信息实体类
 * 用于存储车主姓名、车牌号码和联系电话等信息
 */
@Data
@TableName("car_info")
@Schema(description = "车辆信息实体类")
public class CarInfo extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "车辆信息 ID", example = "1")
    private Long id;

    /**
     * 微信 openId
     */
    @Schema(description = "微信 openId", example = "wx1234567890", required = true)
    private String openId;

    /**
     * 用户ID（用于数据隔离）
     */
    @Schema(description = "用户 ID", example = "1")
    private Long userId;

    /**
     * 车主姓名（必填，2-10字符）
     */
    @NotBlank(message = "车主姓名不能为空")
    @Size(min = 2, max = 10, message = "车主姓名长度必须在2-10个字符之间")
    @Schema(description = "车主姓名", example = "张三", required = true)
    private String ownerName;

    /**
     * 车牌号码（必填）
     */
    @NotBlank(message = "车牌号码不能为空")
    @Schema(description = "车牌号码", example = "京A12345", required = true)
    private String plateNumber;

    /**
     * 联系电话（必填，11位手机号格式）
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "联系电话", example = "13800138000", required = true)
    private String phoneNumber;

    @Schema(description = "入住会员ID", example = "1")
    private Long memberCheckinId;

}