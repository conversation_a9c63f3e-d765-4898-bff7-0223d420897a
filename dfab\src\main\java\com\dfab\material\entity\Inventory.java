package com.dfab.material.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 库存信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("inventory")
@Schema(description = "库存信息实体类")
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Inventory extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "库存信息ID", example = "1")
    private Long id;
    
    /**
     * 物料ID
     */
    @Schema(description = "物料ID", example = "1", required = true)
    private Long materialId;
    
    /**
     * 库存数量
     */
    @Schema(description = "库存数量", example = "100", required = true)
    private Integer quantity;
} 