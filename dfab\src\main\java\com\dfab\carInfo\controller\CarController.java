package com.dfab.carInfo.controller;

import com.dfab.carInfo.entity.CarInfo;
import com.dfab.carInfo.service.CarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车辆信息Controller
 */
@RestController
@RequestMapping("/api/cars")
@RequiredArgsConstructor
@Tag(name = "CarController", description = "车辆信息管理接口，提供车辆信息的增删改查功能")
public class CarController {

    private final CarService carService;

    /**
     * 根据 openId 获取车辆信息列表
     * @param openId 用户的微信 openId
     * @return 车辆信息列表
     */
    @Operation(summary = "根据 openId 获取车辆信息列表", description = "通过用户的微信 openId 查询对应的车辆信息列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取车辆信息列表",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "404", description = "未找到对应的车辆信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @GetMapping("/openId/{openId}")
    public ResponseEntity<List<CarInfo>> getCarsByOpenId(
            @Parameter(description = "用户的微信 openId", required = true) @PathVariable String openId) {
        try {
            List<CarInfo> cars = carService.getCarsByOpenId(openId);
            return ResponseEntity.ok(cars);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建或更新车辆信息
     * @param car 车辆信息实体
     * @return 创建或更新后的车辆信息
     */
    @Operation(summary = "创建或更新车辆信息", description = "如果传入的车辆信息不存在则创建，存在则更新")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功创建或更新车辆信息",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "400", description = "传入的车辆信息无效", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PostMapping("/createOrUpdate")
    public ResponseEntity<?> createOrUpdateCar(
            @Parameter(description = "车辆信息实体", required = true) @Valid @RequestBody CarInfo car) {
        try {
            CarInfo savedCar = carService.createOrUpdate(car);
            return ResponseEntity.ok(savedCar);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 添加车辆信息
     * @param car 车辆信息实体
     * @return 添加结果
     */
    @Operation(summary = "添加车辆信息", description = "添加新的车辆信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功添加车辆信息",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "400", description = "传入的车辆信息无效", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PostMapping
    public ResponseEntity<?> addCar(
            @Parameter(description = "车辆信息实体", required = true) @Valid @RequestBody CarInfo car) {
        try {
            // 保存车辆信息
            CarInfo savedCar = carService.saveCar(car);
            return ResponseEntity.ok(savedCar);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 根据ID和openId获取车辆信息
     * @param id 车辆ID
     * @param openId 用户的微信 openId
     * @return 车辆信息
     */
    @Operation(summary = "根据ID和openId获取车辆信息", description = "通过车辆ID和用户openId获取单个车辆信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取车辆信息",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "404", description = "未找到对应的车辆信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @GetMapping("/{id}/openId/{openId}")
    public ResponseEntity<CarInfo> getCarByIdAndOpenId(
            @Parameter(description = "车辆ID", required = true) @PathVariable Long id,
            @Parameter(description = "用户的微信 openId", required = true) @PathVariable String openId) {
        try {
            CarInfo car = carService.getCarByIdAndOpenId(id, openId);
            if (car != null) {
                return ResponseEntity.ok(car);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取车辆信息列表
     * @param userId 用户ID（可选，用于数据隔离）
     * @return 车辆信息列表
     */
    @Operation(summary = "获取车辆信息列表", description = "获取车辆信息列表，可根据用户ID进行数据隔离")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取车辆信息列表",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @GetMapping
    public ResponseEntity<List<CarInfo>> getCars(
            @Parameter(description = "用户ID", required = false)
            @RequestParam(required = false) String userId) {
        try {
            List<CarInfo> cars;
            if (userId != null && !userId.isEmpty()) {
                // 根据用户ID获取车辆列表
                cars = carService.getCarsByUserId(userId);
            } else {
                // 获取所有车辆列表（管理员权限）
                cars = carService.getAllCars();
            }
            return ResponseEntity.ok(cars);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新车辆信息
     * @param id 车辆ID
     * @param car 车辆信息实体
     * @return 更新结果
     */
    @Operation(summary = "更新车辆信息", description = "根据ID更新车辆信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功更新车辆信息",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CarInfo.class))),
            @ApiResponse(responseCode = "400", description = "传入的车辆信息无效", content = @Content),
            @ApiResponse(responseCode = "404", description = "未找到对应的车辆信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @PutMapping("/{id}")
    public ResponseEntity<?> updateCar(
            @Parameter(description = "车辆ID", required = true) @PathVariable Long id,
            @Parameter(description = "车辆信息实体", required = true) @Valid @RequestBody CarInfo car) {
        try {
            car.setId(id);
            CarInfo updatedCar = carService.createOrUpdate(car);
            return ResponseEntity.ok(updatedCar);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 根据ID和openId删除车辆信息
     * @param id 车辆ID
     * @param openId 用户的微信 openId
     * @return 删除结果
     */
    @Operation(summary = "根据ID和openId删除车辆信息", description = "通过车辆ID和用户openId删除车辆信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功删除车辆信息"),
            @ApiResponse(responseCode = "404", description = "未找到对应的车辆信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @DeleteMapping("/{id}/openId/{openId}")
    public ResponseEntity<?> deleteCarByIdAndOpenId(
            @Parameter(description = "车辆ID", required = true) @PathVariable Long id,
            @Parameter(description = "用户的微信 openId", required = true) @PathVariable String openId) {
        try {
            carService.deleteCarByIdAndOpenId(id, openId);
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 删除车辆信息
     * @param id 车辆ID
     * @param userId 用户ID（可选，用于权限验证）
     * @return 删除结果
     */
    @Operation(summary = "删除车辆信息", description = "根据车辆ID删除车辆信息，可进行用户权限验证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功删除车辆信息"),
            @ApiResponse(responseCode = "404", description = "未找到对应的车辆信息", content = @Content),
            @ApiResponse(responseCode = "500", description = "服务器内部错误", content = @Content)
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCar(
            @Parameter(description = "车辆ID", required = true) @PathVariable Long id,
            @Parameter(description = "用户ID", required = false)
            @RequestParam(required = false) String userId) {
        try {
            if (userId != null && !userId.isEmpty()) {
                // 带权限验证的删除
                carService.deleteCarById(id, userId);
            } else {
                // 不验证权限的删除（管理员权限）
                carService.deleteCarById(id);
            }
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}
