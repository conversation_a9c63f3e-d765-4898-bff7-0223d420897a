spring:
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 1000MB # 单个文件最大限制
      max-request-size: 1000MB # 一次请求的总文件大小限制

  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    url: **********************************************************************
    username: root
    password: dfab_250509
    driver-class-name: com.mysql.cj.jdbc.Driver
  # 移除 JPA 配置
  # jpa:
  #   hibernate:
  #     ddl-auto: update
  #   show-sql: true
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 启用驼峰命名规则
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delete_status  # 全局逻辑删除字段名
      logic-delete-value: 1        # 逻辑删除值（该值意味着已被删除）
      logic-not-delete-value: 0
  mapper-locations: classpath:**/*Mapper.xml
  type-aliases-package: com.ruoyi.**.domain
logging:
  level:
    root: INFO
#  file:
#    name: logs/application.log
server:
  port: 8089

# 微信小程序配置
wechat:
#  appid: wx1d3d7d9e8b819179
#  secret: f5a575eff8cb919522d8a6bcf0125d12
  #夏雨馨竹
  appid: wx39659ab5ea805811
  secret: ba1b7c90762f522f6262a7d18637ba0d

springdoc:
  api-docs:
    enabled: true  # 开启OpenApi接口
  swagger-ui:
    enabled: true  #开启swagger界面，依赖OpenApi，需要OpenApi同时开启



  redis:
    host: localhost  # Redis 服务器地址
    port: 6379       # Redis 服务器端口
    password:        # Redis 服务器密码，如果没有则留空
    database: 0      # 使用的 Redis 数据库编号

jwt:
  secret: dfab05251924793924668592130192479392466859213019247939246685921301924793924668592130 # 请替换为实际的密钥
  expiration: 8640000 # token过期时间(秒)


xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: dfab05251924793924668592130192479392466859213019247939246685921301924793924668592130479392466859213019247939246685921304793924668592130192479392466859213047939246685921301924793924668592130
  # 令牌有效期（默认30分钟）
  expireTime: 30

user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
