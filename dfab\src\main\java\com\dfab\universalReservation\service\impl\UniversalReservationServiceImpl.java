package com.dfab.universalReservation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.dfab.universalReservation.entity.UniversalReservation;
import com.dfab.universalReservation.mapper.UniversalReservationMapper;
import com.dfab.universalReservation.service.UniversalReservationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 通用预约服务实现类
 */
@Service
@Slf4j
public class UniversalReservationServiceImpl extends ServiceImpl<UniversalReservationMapper, UniversalReservation> implements UniversalReservationService {

    @Autowired
    private AppUserService appUserService;

    @Autowired
    @Lazy
    private MemberCheckinService memberCheckinService;

    @Override
    public List<UniversalReservation> getReservationsByOpenId(String openId) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getOpenId, openId);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByOpenIdAndType(String openId, Integer reservationType) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getOpenId, openId);
        wrapper.eq(UniversalReservation::getReservationType, reservationType);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByMemberCheckinId(Long memberCheckinId) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getMemberCheckinId, memberCheckinId);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByType(Integer reservationType) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getReservationType, reservationType);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByStatus(Integer status) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getStatus, status);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByTypeAndStatus(Integer reservationType, Integer status) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getReservationType, reservationType);
        wrapper.eq(UniversalReservation::getStatus, status);
        wrapper.orderByDesc(UniversalReservation::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UniversalReservation> getReservationsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(UniversalReservation::getReservationTime, startTime, endTime);
        wrapper.orderByAsc(UniversalReservation::getReservationTime);
        return list(wrapper);
    }

    @Override
    public UniversalReservation createOrUpdateReservation(UniversalReservation reservation) {
        if (reservation.getId() != null) {
            // 更新操作，需要验证openId
            LambdaQueryWrapper<UniversalReservation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UniversalReservation::getOpenId, reservation.getOpenId());
            queryWrapper.eq(UniversalReservation::getId, reservation.getId());
            UniversalReservation existing = getOne(queryWrapper);
            if (existing == null) {
                log.error("不能更新别人的数据，openId: {}, id: {}", reservation.getOpenId(), reservation.getId());
                throw new RuntimeException("不能更新别人的数据");
            }
            updateById(reservation);
        } else {
            // 新增操作 - 小程序创建预约时自动填充用户信息和会员ID
            if (StrUtil.isNotBlank(reservation.getOpenId())) {
                fillUserInfoFromOpenId(reservation);
            }
            save(reservation);
        }
        return reservation;
    }

    /**
     * 根据openId填充用户信息和会员ID
     * @param reservation 预约信息
     */
    private void fillUserInfoFromOpenId(UniversalReservation reservation) {
        AppUser appUser = appUserService.getByOpenId(reservation.getOpenId());
        if (appUser != null) {
            // 设置用户ID
            reservation.setUserId(appUser.getId());

            // 自动填充用户姓名（如果没有传入）
            if (appUser.getName() != null && !appUser.getName().isEmpty() &&
                (reservation.getUserName() == null || reservation.getUserName().isEmpty())) {
                reservation.setUserName(appUser.getName());
            }

            // 自动填充用户电话（如果没有传入）
            if (appUser.getPhoneNumber() != null && !appUser.getPhoneNumber().isEmpty() &&
                (reservation.getUserPhone() == null || reservation.getUserPhone().isEmpty())) {
                reservation.setUserPhone(appUser.getPhoneNumber());
            }

            // 查询会员入住信息
            List<MemberCheckin> allMemberCheckinsByOpenId = memberCheckinService.getAllMemberCheckinsByOpenId(appUser.getOpenId());
            if (CollUtil.isNotEmpty(allMemberCheckinsByOpenId)) {
                MemberCheckin memberCheckin = allMemberCheckinsByOpenId.get(0); // 取最新的入住记录

                // 自动填充房间号（如果没有传入）
                if (StrUtil.isBlank(reservation.getRoomNumber())) {
                    reservation.setRoomNumber(memberCheckin.getRoomNumber());
                }

                // 自动填充会员入住记录ID（如果没有传入）
                if (reservation.getMemberCheckinId() == null) {
                    reservation.setMemberCheckinId(memberCheckin.getId());
                }
            }
        }
    }

    @Override
    public UniversalReservation createReservationByAdmin(UniversalReservation reservation) {
        // 后台管理员创建预约，直接保存，不需要openId验证和自动填充
        save(reservation);
        return reservation;
    }

    @Override
    public UniversalReservation getReservationByIdAndOpenId(Long id, String openId) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getId, id);
        wrapper.eq(UniversalReservation::getOpenId, openId);
        return getOne(wrapper);
    }

    @Override
    public boolean deleteReservationByIdAndOpenId(Long id, String openId) {
        LambdaQueryWrapper<UniversalReservation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UniversalReservation::getOpenId, openId);
        queryWrapper.eq(UniversalReservation::getId, id);
        UniversalReservation existing = getOne(queryWrapper);
        if (existing != null) {
            return removeById(existing.getId());
        } else {
            log.error("不能删除别人的数据，openId: {}, id: {}", openId, id);
            return false;
        }
    }

    @Override
    public Page<UniversalReservation> getReservationsPageByMybatisPlus(int pageNum, int pageSize, UniversalReservation queryParams) {
        // 使用MyBatis-Plus的分页插件
        Page<UniversalReservation> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数构建查询条件
        if (queryParams != null) {
            if (StringUtils.hasText(queryParams.getOpenId())) {
                wrapper.eq(UniversalReservation::getOpenId, queryParams.getOpenId());
            }
            if (queryParams.getMemberCheckinId() != null) {
                wrapper.eq(UniversalReservation::getMemberCheckinId, queryParams.getMemberCheckinId());
            }
            if (queryParams.getReservationType() != null) {
                wrapper.eq(UniversalReservation::getReservationType, queryParams.getReservationType());
            }
            if (queryParams.getStatus() != null) {
                wrapper.eq(UniversalReservation::getStatus, queryParams.getStatus());
            }
            if (StringUtils.hasText(queryParams.getUserName())) {
                wrapper.like(UniversalReservation::getUserName, queryParams.getUserName());
            }
            if (StringUtils.hasText(queryParams.getContactPhone())) {
                wrapper.like(UniversalReservation::getContactPhone, queryParams.getContactPhone());
            }
            if (StringUtils.hasText(queryParams.getHandlerName())) {
                wrapper.like(UniversalReservation::getHandlerName, queryParams.getHandlerName());
            }
            if (StringUtils.hasText(queryParams.getTitle())) {
                wrapper.like(UniversalReservation::getTitle, queryParams.getTitle());
            }
            if (StringUtils.hasText(queryParams.getLocation())) {
                wrapper.like(UniversalReservation::getLocation, queryParams.getLocation());
            }
            if (queryParams.getReservationTimeStart() != null) {
                wrapper.ge(UniversalReservation::getReservationTime, queryParams.getReservationTimeStart());
            }
            if (queryParams.getReservationTimeEnd() != null) {
                wrapper.le(UniversalReservation::getReservationTime, queryParams.getReservationTimeEnd());
            }
        }

        wrapper.orderByDesc(UniversalReservation::getCreateTime);

        // 使用MyBatis-Plus的分页查询
        return page(page, wrapper);
    }

    @Override
    public boolean updateReservationStatus(Long id, Integer status) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<UniversalReservation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UniversalReservation::getId, id);
        updateWrapper.set(UniversalReservation::getStatus, status);
        
        return update(updateWrapper);
    }

    @Override
    public boolean assignHandler(Long id, String handlerName, String handlerPhone) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<UniversalReservation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UniversalReservation::getId, id);
        updateWrapper.set(UniversalReservation::getHandlerName, handlerName);
        updateWrapper.set(UniversalReservation::getHandlerPhone, handlerPhone);
        
        return update(updateWrapper);
    }

    @Override
    public boolean completeReservation(Long id, LocalDateTime actualStartTime, LocalDateTime actualEndTime) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<UniversalReservation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UniversalReservation::getId, id);
        updateWrapper.set(UniversalReservation::getStatus, 2); // 2-已完成
        updateWrapper.set(UniversalReservation::getActualStartTime, actualStartTime);
        updateWrapper.set(UniversalReservation::getActualEndTime, actualEndTime);
        
        return update(updateWrapper);
    }

    @Override
    public boolean cancelReservation(Long id, String cancelReason) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<UniversalReservation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UniversalReservation::getId, id);
        updateWrapper.set(UniversalReservation::getStatus, 3); // 3-已取消
        updateWrapper.set(UniversalReservation::getCancelReason, cancelReason);
        
        return update(updateWrapper);
    }

    @Override
    public boolean rateReservation(Long id, Integer rating, String review) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<UniversalReservation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UniversalReservation::getId, id);
        updateWrapper.set(UniversalReservation::getRating, rating);
        updateWrapper.set(UniversalReservation::getReview, review);
        
        return update(updateWrapper);
    }

    @Override
    public long getTodayReservationCount(Integer reservationType) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);
        
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(UniversalReservation::getReservationTime, startOfDay, endOfDay);
        if (reservationType != null) {
            wrapper.eq(UniversalReservation::getReservationType, reservationType);
        }
        
        return count(wrapper);
    }

    @Override
    public long getPendingReservationCount(Integer reservationType) {
        LambdaQueryWrapper<UniversalReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UniversalReservation::getStatus, 0); // 0-待确认
        if (reservationType != null) {
            wrapper.eq(UniversalReservation::getReservationType, reservationType);
        }
        
        return count(wrapper);
    }
}
