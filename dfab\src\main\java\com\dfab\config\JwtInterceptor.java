package com.dfab.config;

import com.dfab.appUser.entity.AppUser;
import com.dfab.util.JwtUtil;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 从请求头获取token
        String token = request.getHeader("Authorization");
        System.out.println("JWT拦截器 - 请求路径: " + request.getRequestURI());
        System.out.println("JWT拦截器 - Authorization头: " + token);

        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            System.out.println("JWT拦截器 - 提取的token: " + token);

            try {
                // 验证并解析token
                String subject = jwtUtil.getSubjectFromToken(token);
                System.out.println("JWT拦截器 - token subject: " + subject);

                if (jwtUtil.validateToken(token, subject)) {
                    Claims claims = jwtUtil.getClaimsFromToken(token);
                    String openId = claims.get("openid", String.class);
                    System.out.println("JWT拦截器 - 提取的openId: " + openId);
                    request.setAttribute("openId", openId);
                } else {
                    System.out.println("JWT拦截器 - token验证失败");
                }
            } catch (Exception e) {
                System.out.println("JWT拦截器 - token解析异常: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("JWT拦截器 - 没有找到有效的Authorization头");
        }
        return true;
    }
}