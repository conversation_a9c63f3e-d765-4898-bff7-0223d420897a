package com.dfab.memberCheckin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 入住会员管理 Service 接口
 */
public interface MemberCheckinService extends IService<MemberCheckin> {

    /**
     * 分页查询入住会员列表
     * @param memberCheckin 查询条件
     * @return 分页结果
     */
    IPage<MemberCheckin> page(MemberCheckin memberCheckin);

    /**
     * 查询入住会员列表
     * @param memberCheckin 查询条件
     * @return 入住会员列表
     */
    List<MemberCheckin> list(MemberCheckin memberCheckin);

    /**
     * 添加入住会员记录
     * @param memberCheckin 入住会员信息
     * @return 添加后的入住会员信息
     */
    MemberCheckin add(MemberCheckin memberCheckin);

    /**
     * 管理员更新入住会员记录
     * @param memberCheckin 入住会员信息
     * @return 更新结果
     */
    Boolean updateByAdmin(MemberCheckin memberCheckin);

    /**
     * 根据手机号查询入住会员
     * @param phoneNumber 手机号
     * @return 入住会员列表
     */
    List<MemberCheckin> getByPhoneNumber(String phoneNumber);

    /**
     * 根据房间号查询入住会员
     * @param roomNumber 房间号
     * @return 入住会员列表
     */
    List<MemberCheckin> getByRoomNumber(String roomNumber);

    List<MemberCheckin> getByUserId(Long userId);

    /**
     * 根据入住日期范围查询入住会员
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 入住会员列表
     */
    List<MemberCheckin> getByCheckinDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 根据状态查询入住会员
     * @param status 状态：0-已入住，1-已退住
     * @return 入住会员列表
     */
    List<MemberCheckin> getByStatus(Integer status);

    /**
     * 根据openId获取入住会员列表
     * @param openId 微信openId
     * @return 入住会员列表
     */
    List<MemberCheckin> getAllMemberCheckinsByOpenId(String openId);

    /**
     * 根据openId获取当前入住的会员信息
     * @param openId 微信openId
     * @return 当前入住会员列表
     */
    List<MemberCheckin> getCurrentMemberCheckinsByOpenId(String openId);

    /**
     * 获取入住会员信息（小程序用）
     * @param memberCheckin 查询条件
     * @return 入住会员信息
     */
    MemberCheckin get(MemberCheckin memberCheckin);

    /**
     * 删除入住会员信息（小程序用）
     * @param memberCheckin 入住会员信息
     * @return 删除结果
     */
    Boolean remove(MemberCheckin memberCheckin);

    /**
     * 更新入住会员信息（小程序用）
     * @param memberCheckin 入住会员信息
     * @return 更新结果
     */
    Boolean update(MemberCheckin memberCheckin);

    @Transactional
    Boolean updateRoomNumber(MemberCheckin memberCheckin);

    /**
     * 为删除的会员创建关联车辆的删除任务
     * @param memberCheckin 被删除的会员信息
     */
    void createCarDeleteTasksForDeletedMember(MemberCheckin memberCheckin);
}
