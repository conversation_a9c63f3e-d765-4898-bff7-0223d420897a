// base color
$blue: #324157;
$light-blue: #333c46;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// 默认主题变量
$menuText: #bfcbd9;
$menuActiveText: #409eff;
$menuBg: #304156;
$menuHover: #263445;

// 浅色主题theme-light
$menuLightBg: #ffffff;
$menuLightHover: #f0f1f5;
$menuLightText: #303133;
$menuLightActiveText: #409EFF;

// 基础变量
$base-sidebar-width: 200px;
$sideBarWidth: 200px;

// 菜单暗色变量
$base-menu-color: #bfcbd9;
$base-menu-color-active: #f4f4f5;
$base-menu-background: #304156;
$base-sub-menu-background: #1f2d3d;
$base-sub-menu-hover: #001528;

// 组件变量
$--color-primary: #409EFF;
$--color-success: #67C23A;
$--color-warning: #E6A23C;
$--color-danger: #F56C6C;
$--color-info: #909399;

:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuLightBg: $menuLightBg;
  menuLightHover: $menuLightHover;
  menuLightText: $menuLightText;
  menuLightActiveText: $menuLightActiveText;
  sideBarWidth: $sideBarWidth;
  // 导出基础颜色
  blue: $blue;
  lightBlue: $light-blue;
  red: $red;
  pink: $pink;
  green: $green;
  tiffany: $tiffany;
  yellow: $yellow;
  panGreen: $panGreen;
  // 导出组件颜色
  colorPrimary: $--color-primary;
  colorSuccess: $--color-success;
  colorWarning: $--color-warning;
  colorDanger: $--color-danger;
  colorInfo: $--color-info;
}

// CSS变量定义
:root {
  /* 亮色模式变量 */
  --sidebar-bg: #{$menuBg};
  --sidebar-text: #{$menuText};
  --menu-hover: #{$menuHover};
  
  --navbar-bg: #ffffff;
  --navbar-text: #303133;
  
  /* splitpanes default-theme 变量 */
  --splitpanes-default-bg: #ffffff;

}

// 暗黑模式变量
html.dark {
  /* 默认通用 */
  --el-bg-color: #141414;
  --el-bg-color-overlay: #1d1e1f;
  --el-text-color-primary: #ffffff;
  --el-text-color-regular: #d0d0d0;
  --el-border-color: #434343;
  --el-border-color-light: #434343;

  /* 侧边栏 */
  --sidebar-bg: #141414;
  --sidebar-text: #ffffff;
  --menu-hover: #2d2d2d;
  --menu-active-text: #{$menuActiveText};

  /* 顶部导航栏 */
  --navbar-bg: #141414;
  --navbar-text: #ffffff;
  --navbar-hover: #141414;

  /* 标签栏 */
  --tags-bg: #141414;
  --tags-item-bg: #1d1e1f;
  --tags-item-border: #303030;
  --tags-item-text: #d0d0d0;
  --tags-item-hover: #2d2d2d;
  --tags-close-hover: #64666a;

  /* splitpanes 组件暗黑模式变量 */
  --splitpanes-bg: #141414;
  --splitpanes-border: #303030;
  --splitpanes-splitter-bg: #1d1e1f;
  --splitpanes-splitter-hover-bg: #2d2d2d;

  /* blockquote 暗黑模式变量 */
  --blockquote-bg: #1d1e1f;
  --blockquote-border: #303030;
  --blockquote-text: #d0d0d0;
  
  /* Cron 时间表达式 模式变量 */
  --cron-border: #303030;

  /* splitpanes default-theme 暗黑模式变量 */
  --splitpanes-default-bg: #141414;

  /* 侧边栏菜单覆盖 */
   .sidebar-container {
    .el-menu-item, .menu-title {
      color: var(--el-text-color-regular);
    }
    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: var(--el-bg-color) !important;
    }
  }

  /* 顶部栏栏菜单覆盖 */
  .el-menu--horizontal {
    .el-menu-item {
      &:not(.is-disabled) {
        &:hover,
        &:focus {
          background-color: var(--navbar-hover) !important;
        }
      }
    }
  }

  /* 分割窗格覆盖 */
  .splitpanes {
    background-color: var(--splitpanes-bg);

    .splitpanes__pane {
      background-color: var(--splitpanes-bg);
      border-color: var(--splitpanes-border);
    }

    .splitpanes__splitter {
      background-color: var(--splitpanes-splitter-bg);
      border-color: var(--splitpanes-border);

      &:hover {
        background-color: var(--splitpanes-splitter-hover-bg);
      }

      &:before,
      &:after {
        background-color: var(--splitpanes-border);
      }
    }
  }

  /* 表格样式覆盖 */
  .el-table {
    --el-table-header-bg-color: var(--el-bg-color-overlay) !important;
    --el-table-header-text-color: var(--el-text-color-regular) !important;
    --el-table-border-color: var(--el-border-color-light) !important;
    --el-table-row-hover-bg-color: var(--el-bg-color-overlay) !important;

    .el-table__header-wrapper, .el-table__fixed-header-wrapper {
      th {
        background-color: var(--el-bg-color-overlay, #f8f8f9) !important;
        color: var(--el-text-color-regular, #515a6e);
      }
    }
  }

  /* 树组件高亮样式覆盖 */
  .el-tree {
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: var(--el-bg-color-overlay) !important;
      color: var(--el-color-primary);
    }

    .el-tree-node__content:hover {
      background-color: var(--el-bg-color-overlay);
    }
  }
  
  /* 下拉菜单样式覆盖 */
  .el-dropdown-menu__item:not(.is-disabled):focus, .el-dropdown-menu__item:not(.is-disabled):hover{
    background-color: var(--navbar-hover) !important;
  }

  /* blockquote样式覆盖 */
  blockquote {
    background-color: var(--blockquote-bg) !important;
    border-left-color: var(--blockquote-border) !important;
    color: var(--blockquote-text) !important;
  }
  
  /* 时间表达式标题样式覆盖 */
  .popup-result .title {
    background: var(--cron-border);
  }

}

