import request from '@/utils/request'

// 查询入住会员列表
export function listMemberCheckin(query) {
  return request({
    url: '/admin/memberCheckin/list',
    method: 'post',
    data: query
  })
}

// 查询入住会员详细
export function getMemberCheckin(id) {
  return request({
    url: '/admin/memberCheckin/' + id,
    method: 'post'
  })
}

// 添加入住会员
export function addMemberCheckin(data) {
  return request({
    url: '/admin/memberCheckin/add',
    method: 'post',
    data: data
  })
}

// 修改入住会员
export function updateMemberCheckin(data) {
  return request({
    url: '/admin/memberCheckin/update',
    method: 'post',
    data: data
  })
}

// 变更房号
export function updateRoomNumber(data) {
  return request({
    url: '/admin/memberCheckin/updateRoomNumber',
    method: 'post',
    data: data
  })
}

// 删除入住会员
export function delMemberCheckin(id) {
  return request({
    url: '/admin/memberCheckin/delete',
    method: 'post',
    data: { id }
  })
}

// 获取所有入住会员列表（用于下拉选择）
export function getAllMemberCheckins() {
  return request({
    url: '/admin/memberCheckin/all',
    method: 'post'
  })
}

// 导出入住会员
export function exportMemberCheckin(query) {
  return request({
    url: '/admin/memberCheckin/export',
    method: 'post',
    data: query
  })
}

export function getMemberPackage(query) {
  return request({
    url: '/admin/material/memberList',
    method: 'post',
    data: query
  })
}

export function memberListPay(query) {
  return request({
    url: '/admin/material/memberListPay',
    method: 'post',
    data: query
  })
}

// 查询默认套餐列表
 export function getPackageDefaultList(query) {
  return request({ 
    url: '/admin/material/packageDefaultList', 
    method: 'post', 
    data: query 
  }); 
}

// 提交新套餐
export function updateMemberPackages(data) { 
  return request({ 
    url: '/admin/material/updateMemberPackages', 
    method: 'post', 
    data: data 
  }); 
}
