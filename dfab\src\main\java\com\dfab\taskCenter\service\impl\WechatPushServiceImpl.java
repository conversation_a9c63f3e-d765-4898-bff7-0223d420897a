package com.dfab.taskCenter.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.dfab.taskCenter.entity.PushRecord;
import com.dfab.taskCenter.entity.TaskCenter;
import com.dfab.taskCenter.mapper.PushRecordMapper;
import com.dfab.taskCenter.mapper.TaskCenterMapper;
import com.dfab.taskCenter.service.WechatPushService;
import com.dfab.util.WechatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 微信推送Service实现类
 */
@Slf4j
@Service
public class WechatPushServiceImpl implements WechatPushService {

    @Autowired
    private PushRecordMapper pushRecordMapper;

    @Autowired
    private TaskCenterMapper taskCenterMapper;

    @Autowired
    private WechatUtil wechatUtil;

    @Autowired
    private RestTemplate restTemplate;

    // 微信小程序推送API地址
    private static final String WECHAT_PUSH_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";

    @Override
    @Async
    public PushRecord pushTaskNotification(TaskCenter task, Integer pushType) {
        String title = getNotificationTitle(task, pushType);
        String content = getNotificationContent(task, pushType);
        String templateId = getTemplateId(pushType);
        String pagePath = "/pages/task/detail?id=" + task.getId();
        
        JSONObject data = new JSONObject();
        data.put("task_id", task.getId());
        data.put("task_type", task.getTaskType());
        data.put("plate_number", task.getCustomerPhone());
        data.put("owner_name", task.getCustomerName());
        
        return sendWechatMessage(task.getOpenId(), title, content, templateId, pagePath, data.toString(), task.getId(), pushType);
    }

    @Override
    @Async
    public PushRecord pushTaskCompletionNotification(TaskCenter task) {
        String title = "任务处理完成";
        String content = String.format("您的%s任务已处理完成，关键信息：%s", getTaskTypeName(task.getTaskType()), task.getKeyInfo());
        String templateId = "completion_template";
        String pagePath = "/pages/task/detail?id=" + task.getId();

        JSONObject data = new JSONObject();
        data.put("task_id", task.getId());
        data.put("task_type", task.getTaskType());
        data.put("key_info", task.getKeyInfo());
        data.put("business_type", task.getBusinessType());
        data.put("processor", task.getProcessor());
        data.put("process_time", task.getProcessTime() != null ? task.getProcessTime().toString() : "");

        return sendWechatMessage(task.getOpenId(), title, content, templateId, pagePath, data.toString(), task.getId(), 2);
    }

    @Override
    @Async
    public PushRecord pushTaskReminder(TaskCenter task) {
        String title = "任务处理提醒";
        String content = String.format("您的%s任务尚未处理，关键信息：%s，请及时关注", getTaskTypeName(task.getTaskType()), task.getKeyInfo());
        String templateId = "reminder_template";
        String pagePath = "/pages/task/detail?id=" + task.getId();

        JSONObject data = new JSONObject();
        data.put("task_id", task.getId());
        data.put("task_type", task.getTaskType());
        data.put("key_info", task.getKeyInfo());
        data.put("business_type", task.getBusinessType());

        return sendWechatMessage(task.getOpenId(), title, content, templateId, pagePath, data.toString(), task.getId(), 3);
    }

    @Override
    public int batchPushUnsentTasks() {
        List<TaskCenter> unpushedTasks = taskCenterMapper.selectUnpushedTasks();
        int successCount = 0;
        
        for (TaskCenter task : unpushedTasks) {
            try {
                PushRecord record = pushTaskNotification(task, 1);
                if (record != null && record.getStatus() == 1) {
                    successCount++;
                    // 更新任务推送状态
                    taskCenterMapper.updatePushStatus(task.getId(), 1, task.getPushCount() + 1);
                }
            } catch (Exception e) {
                log.error("批量推送任务失败，任务ID: {}, 错误: {}", task.getId(), e.getMessage(), e);
            }
        }
        
        return successCount;
    }

    @Override
    public int retryFailedPushes() {
        List<PushRecord> failedRecords = pushRecordMapper.selectRetryRecords(3); // 最大重试3次
        int successCount = 0;
        
        for (PushRecord record : failedRecords) {
            try {
                PushRecord retryRecord = sendWechatMessage(
                    record.getReceiverOpenId(),
                    record.getTitle(),
                    record.getContent(),
                    record.getTemplateId(),
                    record.getPagePath(),
                    record.getPushData(),
                    record.getTaskId(),
                    record.getPushType()
                );
                
                if (retryRecord != null && retryRecord.getStatus() == 1) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("重试推送失败，记录ID: {}, 错误: {}", record.getId(), e.getMessage(), e);
            }
        }
        
        return successCount;
    }

    @Override
    public PushRecord sendCustomMessage(String openId, String title, String content, 
                                       String templateId, String pagePath, String data) {
        return sendWechatMessage(openId, title, content, templateId, pagePath, data, null, 1);
    }

    @Override
    public Integer checkPushStatus(Long pushRecordId) {
        PushRecord record = pushRecordMapper.selectById(pushRecordId);
        return record != null ? record.getStatus() : null;
    }

    /**
     * 发送微信消息的核心方法
     */
    private PushRecord sendWechatMessage(String openId, String title, String content, 
                                        String templateId, String pagePath, String data, 
                                        Long taskId, Integer pushType) {
        PushRecord record = new PushRecord();
        record.setTaskId(taskId);
        record.setPushType(pushType);
        record.setTitle(title);
        record.setContent(content);
        record.setReceiverOpenId(openId);
        record.setTemplateId(templateId);
        record.setPagePath(pagePath);
        record.setPushData(data);
        record.setPushTime(new Date());
        
        try {
            // 获取access_token
            String accessToken = wechatUtil.getAccessToken();
            if (!StringUtils.hasText(accessToken)) {
                record.setStatus(0);
                record.setErrorMsg("获取access_token失败");
                pushRecordMapper.insert(record);
                return record;
            }
            
            // 构建推送数据
            JSONObject pushData = buildPushData(openId, templateId, pagePath, title, content, data);
            
            // 发送推送请求
            String url = String.format(WECHAT_PUSH_URL, accessToken);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(pushData.toString(), headers);
            
            String response = restTemplate.postForObject(url, request, String.class);
            JSONObject responseJson = JSONObject.parseObject(response);
            
            if (responseJson != null && responseJson.getInteger("errcode") == 0) {
                record.setStatus(1); // 推送成功
                record.setResponse(response);
            } else {
                record.setStatus(0); // 推送失败
                record.setErrorMsg(responseJson != null ? responseJson.getString("errmsg") : "未知错误");
                record.setResponse(response);
            }
            
        } catch (Exception e) {
            log.error("发送微信推送失败: {}", e.getMessage(), e);
            record.setStatus(0);
            record.setErrorMsg(e.getMessage());
        }
        
        pushRecordMapper.insert(record);
        return record;
    }

    /**
     * 构建微信推送数据
     */
    private JSONObject buildPushData(String openId, String templateId, String pagePath, 
                                    String title, String content, String data) {
        JSONObject pushData = new JSONObject();
        pushData.put("touser", openId);
        pushData.put("template_id", templateId);
        pushData.put("page", pagePath);
        
        JSONObject dataObj = new JSONObject();
        dataObj.put("thing1", createDataItem(title));
        dataObj.put("thing2", createDataItem(content));
        
        pushData.put("data", dataObj);
        
        return pushData;
    }

    /**
     * 创建推送数据项
     */
    private JSONObject createDataItem(String value) {
        JSONObject item = new JSONObject();
        item.put("value", value);
        return item;
    }

    /**
     * 获取通知标题
     */
    private String getNotificationTitle(TaskCenter task, Integer pushType) {
        switch (pushType) {
            case 1: return "新任务通知";
            case 2: return "任务完成通知";
            case 3: return "任务提醒";
            default: return "任务通知";
        }
    }

    /**
     * 获取通知内容
     */
    private String getNotificationContent(TaskCenter task, Integer pushType) {
        String taskTypeName = getTaskTypeName(task.getTaskType());
        String keyInfo = task.getKeyInfo() != null ? task.getKeyInfo() : "无";
        switch (pushType) {
            case 1: return String.format("您有新的%s任务，关键信息：%s", taskTypeName, keyInfo);
            case 2: return String.format("您的%s任务已完成，关键信息：%s", taskTypeName, keyInfo);
            case 3: return String.format("您的%s任务待处理，关键信息：%s", taskTypeName, keyInfo);
            default: return String.format("任务通知，关键信息：%s", keyInfo);
        }
    }

    /**
     * 获取任务类型名称
     */
    private String getTaskTypeName(Integer taskType) {
        switch (taskType) {
            case 1: return "车牌新增";
            case 2: return "车牌删除";
            case 3: return "离所处理";
            case 4: return "会员注册";
            case 5: return "预约处理";
            case 6: return "投诉处理";
            default: return "未知任务";
        }
    }

    /**
     * 获取模板ID
     */
    private String getTemplateId(Integer pushType) {
        switch (pushType) {
            case 1: return "task_create_template";
            case 2: return "task_complete_template";
            case 3: return "task_reminder_template";
            default: return "default_template";
        }
    }
}
