<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dfab.taskCenter.mapper.TaskCenterMapper">

    <resultMap type="com.dfab.taskCenter.entity.TaskCenter" id="TaskCenterResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="taskType" column="task_type"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="keyInfo" column="key_info"/>
        <result property="businessId" column="business_id"/>
        <result property="businessType" column="business_type"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerPhone" column="customer_phone"/>
        <result property="openId" column="open_id"/>
        <result property="userId" column="user_id"/>
        <result property="extendInfo" column="extend_info"/>
        <result property="processor" column="processor"/>
        <result property="processTime" column="process_time"/>
        <result property="processRemark" column="process_remark"/>
        <result property="priority" column="priority"/>
        <result property="isPushed" column="is_pushed"/>
        <result property="pushCount" column="push_count"/>
        <result property="lastPushTime" column="last_push_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteStatus" column="delete_status"/>
    </resultMap>

    <sql id="selectTaskCenterVo">
        select id, title, task_type, status, description, key_info, business_id, business_type,
               customer_name, customer_phone, open_id, user_id, extend_info, processor, process_time,
               process_remark, priority, is_pushed, push_count, last_push_time,
               create_time, update_time, delete_status
        from task_center
    </sql>

    <select id="selectByTaskType" parameterType="Integer" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and task_type = #{taskType}
        order by create_time desc
    </select>

    <select id="selectByStatus" parameterType="Integer" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and status = #{status}
        order by create_time desc
    </select>

    <select id="selectByUserId" parameterType="Long" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectByKeyInfo" parameterType="String" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and key_info = #{keyInfo}
        order by create_time desc
    </select>

    <select id="selectByBusinessType" parameterType="String" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and business_type = #{businessType}
        order by create_time desc
    </select>

    <select id="selectByBusinessId" parameterType="Long" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and business_id = #{businessId}
        order by create_time desc
    </select>

    <select id="countPendingTasks" resultType="int">
        select count(*) from task_center 
        where delete_status = 0 and status = 0
    </select>

    <select id="countTodayTasks" resultType="int">
        select count(*) from task_center 
        where delete_status = 0 and DATE(create_time) = CURDATE()
    </select>

    <select id="selectByPriority" parameterType="Integer" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and priority = #{priority}
        order by create_time desc
    </select>

    <select id="selectUnpushedTasks" resultMap="TaskCenterResult">
        <include refid="selectTaskCenterVo"/>
        where delete_status = 0 and is_pushed = 0
        order by priority desc, create_time asc
    </select>

    <update id="updatePushStatus">
        update task_center 
        set is_pushed = #{isPushed}, 
            push_count = #{pushCount},
            last_push_time = NOW()
        where id = #{taskId}
    </update>

</mapper>
