package com.dfab.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 菜单管理实体类，用于存储系统中的菜单信息，支持菜单的父子结构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("menu")
@Schema(description = "菜单管理实体类")
public class Menu extends BaseEntity {
    // 菜单 ID
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "菜单 ID", example = "1")
    private Long id;

    // 菜单名称
    @Schema(description = "菜单名称", example = "用户管理")
    private String name;

    // 菜单路径
    @Schema(description = "菜单路径", example = "/user")
    private String path;

    // 菜单图标
    @Schema(description = "菜单图标", example = "user")
    private String icon;

    // 父菜单 ID，0 表示顶级菜单
    @Schema(description = "父菜单 ID，0 表示顶级菜单", example = "0")
    private Long parentId;

    // 菜单排序值
    @Schema(description = "菜单排序值", example = "1")
    private Integer sort;

    // 子菜单列表
    @Schema(description = "子菜单列表")
    @TableField(exist = false)
    private List<Menu> children;

    //子菜单列表ID
    @Schema(description = "子菜单列表ID")
    private List<Long> childIds;

    // 新增权限关联字段
    @Schema(description = "关联的权限 ID", example = "1")
    private Long permissionId;

    // 新增权限编码字段
    @Schema(description = "权限编码", example = "user:manage")
    private String permissionCode;
}