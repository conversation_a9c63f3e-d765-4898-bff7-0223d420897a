package com.dfab.carInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.carInfo.entity.CarInfo;

import java.util.List;

/**
 * 车辆信息Service接口
 */
public interface CarService extends IService<CarInfo> {
    /**
     * 保存车辆信息
     * @param car 车辆信息实体
     * @return 保存后的车辆信息
     */
    CarInfo saveCar(CarInfo car);

    /**
     * 创建或更新车辆信息
     * @param car 车辆信息实体
     * @return 创建或更新后的车辆信息
     */
    CarInfo createOrUpdate(CarInfo car);

    /**
     * 根据openId获取车辆信息列表
     * @param openId 微信openId
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByOpenId(String openId);

    /**
     * 根据ID和openId获取车辆信息
     * @param id 车辆ID
     * @param openId 微信openId
     * @return 车辆信息
     */
    CarInfo getCarByIdAndOpenId(Long id, String openId);

    /**
     * 获取所有车辆信息
     * @return 车辆信息列表
     */
    List<CarInfo> getAllCars();

    /**
     * 根据用户ID获取车辆信息
     * @param userId 用户ID
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByUserId(String userId);

    /**
     * 检查车牌号是否已存在
     * @param plateNumber 车牌号
     * @return 是否存在
     */
    boolean isPlateNumberExists(String plateNumber);

    /**
     * 检查车牌号是否已存在（排除指定ID）
     * @param plateNumber 车牌号
     * @param excludeId 排除的车辆ID
     * @return 是否存在
     */
    boolean isPlateNumberExists(String plateNumber, Long excludeId);

    /**
     * 根据ID和openId删除车辆信息
     * @param id 车辆ID
     * @param openId 微信openId（用于权限验证）
     */
    void deleteCarByIdAndOpenId(Long id, String openId);

    /**
     * 根据ID删除车辆信息
     * @param id 车辆ID
     * @param userId 用户ID（用于权限验证）
     */
    void deleteCarById(Long id, String userId);

    /**
     * 根据ID删除车辆信息（不验证用户权限）
     * @param id 车辆ID
     */
    void deleteCarById(Long id);

    /**
     * 根据openId删除所有车辆信息
     * @param openId 微信openId
     * @return 删除的记录数
     */
    int deleteCarsByOpenId(String openId);

    /**
     * 根据手机号获取车辆信息列表
     * @param phoneNumber 手机号
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByPhoneNumber(String phoneNumber);

    /**
     * 根据手机号或openId获取车辆信息列表
     * @param phoneNumber 手机号
     * @param openId 微信openId
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByPhoneNumberOrOpenId(String phoneNumber, String openId);

    /**
     * 根据会员入住ID获取车辆信息列表
     * @param memberCheckinId 会员入住ID
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByMemberCheckinId(Long memberCheckinId);

    /**
     * 根据openId和会员入住ID获取车辆信息列表
     * @param openId 微信openId
     * @param memberCheckinId 会员入住ID
     * @return 车辆信息列表
     */
    List<CarInfo> getCarsByOpenIdAndMemberCheckinId(String openId, Long memberCheckinId);
}