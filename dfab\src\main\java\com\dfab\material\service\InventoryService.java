package com.dfab.material.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.material.entity.Inventory;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.entity.MaterialInfo;

import java.util.List;
import java.util.Map;

/**
 * 库存信息Service接口
 */
public interface InventoryService extends IService<Inventory> {
    
    /**
     * 查询库存信息列表（包含物料信息）
     * @return 库存信息列表
     */
    List<Map<String, Object>> getInventoryList();

    /**
     * 分页查询库存信息列表（包含物料信息）
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页库存信息列表
     */
    Page<Map<String, Object>> getInventoryPage(int pageNum, int pageSize);

    List<MaterialInfo> warnList();


    Inventory getByMaterialId(Long materialId);

    /**
     * 分页查询库存信息列表（包含物料信息，支持查询条件）
     * @param materialInfo 查询条件
     * @return 分页库存信息列表
     */
    Page<Map<String, Object>> getInventoryPage(MaterialInfo materialInfo);

    /**
     * 入库操作
     * @param record 入库记录
     * @return 是否成功
     */
    boolean inbound(InventoryRecord record);

    /**
     * 出库操作
     * @param record 出库记录
     * @return 是否成功
     */
    boolean outbound(InventoryRecord record);
} 