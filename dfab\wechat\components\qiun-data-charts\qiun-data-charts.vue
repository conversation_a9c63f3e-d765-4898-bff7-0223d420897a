<template>
	<view class="chartsjs">
		<canvas v-if="canvasId" class="charts" :id="canvasId" :canvasId="canvasId" :style="{width: cWidth + 'px', height: cHeight + 'px', background: background}"></canvas>
	</view>
</template>

<script>
	// 引入uCharts库
	import uCharts from './u-charts.js';

	export default {
		name: 'qiun-data-charts',
		props: {
			type: {
				type: String,
				default: null
			},
			canvasId: {
				type: String,
				default: null
			},
			canvas2d: {
				type: Boolean,
				default: false
			},
			background: {
				type: String,
				default: '#f8f5f2' // 与首页背景色一致
			},
			animation: {
				type: Boolean,
				default: true
			},
			chartData: {
				type: Object,
				default: null
			},
			opts: {
				type: Object,
				default: null
			},
			loadingType: {
				type: Number,
				default: 2
			},
			errorShow: {
				type: Boolean,
				default: true
			},
			errorReload: {
				type: Boolean,
				default: true
			},
			errorMessage: {
				type: String,
				default: null
			},
			inScrollView: {
				type: <PERSON>olean,
				default: false
			},
			reshow: {
				type: <PERSON>olean,
				default: false
			},
			reload: {
				type: Boolean,
				default: false
			},
			disableScroll: {
				type: <PERSON><PERSON><PERSON>,
				default: false
			},
			optsWatch: {
				type: Boolean,
				default: true
			},
			onzoom: {
				type: Boolean,
				default: false
			},
			ontap: {
				type: Boolean,
				default: true
			},
			ontouch: {
				type: Boolean,
				default: false
			},
			onmouse: {
				type: Boolean,
				default: true
			},
			onmovetip: {
				type: Boolean,
				default: false
			},
			tooltipShow: {
				type: Boolean,
				default: true
			},
			tooltipFormat: {
				type: String,
				default: undefined
			},
			tooltipCustom: {
				type: Object,
				default: undefined
			},
			startDate: {
				type: String,
				default: undefined
			},
			endDate: {
				type: String,
				default: undefined
			},
			textAlign: {
				type: String,
				default: 'center'
			},
			dataLabel: {
				type: Boolean,
				default: true
			},
			dataPointShape: {
				type: Boolean,
				default: true
			},
			dataPointShapeType: {
				type: String,
				default: 'solid'
			},
			tapLegend: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				cWidth: 375,
				cHeight: 250,
				showchart: false,
				mixinDatacomLoading: true,
				mixinDatacomErrorMessage: null,
				uChartsInstance: null
			}
		},
		created() {
			// 获取系统信息，适配不同屏幕宽度
			const systemInfo = uni.getSystemInfoSync();
			// 使用屏幕宽度作为图表宽度，确保在小屏幕上也能完全显示
			this.cWidth = systemInfo.windowWidth - 30; // 减去左右边距
			this.cHeight = uni.upx2px(500);
		},
		mounted() {
			this.$nextTick(() => {
				this.beforeInit();
			});
		},
		watch: {
			chartData: {
				handler(val, oldVal) {
					if(val){
						this.mixinDatacomLoading = false;
						this.showchart = true;
						this.drawCharts();
					}
				},
				deep: true
			}
		},
		methods: {
			beforeInit() {
				if(this.chartData){
					this.mixinDatacomLoading = false;
					this.showchart = true;
					this.drawCharts();
				}
			},
			drawCharts() {
				const cid = this.canvasId;

				if(this.chartData && this.chartData.series && this.chartData.series.length > 0){
					//复位opts或者重新渲染
					if(this.chartData.opts){
						this.opts = this.chartData.opts;
					}

					const newData = JSON.parse(JSON.stringify(this.chartData));

					if(this.type && cid){
						const ctx = uni.createCanvasContext(cid, this);

						this.uChartsInstance = new uCharts({
							type: this.type,
							context: ctx,
							width: this.cWidth,
							height: this.cHeight,
							series: newData.series,
							categories: newData.categories,
							animation: this.animation,
							background: this.background,
							pixelRatio: 1,
							opts: this.opts,
							dataLabel: this.dataLabel,
							dataPointShape: this.dataPointShape,
							dataPointShapeType: this.dataPointShapeType,
							tapLegend: this.tapLegend
						});

						this.uChartsInstance.addEventListener('renderComplete', () => {
							this.$emit('complete', {
								type: "complete",
								complete: true,
								id: cid
							});
						});

						this.uChartsInstance.addEventListener('scrollLeft', () => {
							this.$emit('scrollLeft', {
								type: "scrollLeft",
								scrollLeft: true,
								id: cid
							});
						});

						this.uChartsInstance.addEventListener('scrollRight', () => {
							this.$emit('scrollRight', {
								type: "scrollRight",
								scrollRight: true,
								id: cid
							});
						});
					}
				}
			},
			tap(e) {
				if(this.uChartsInstance){
					const currentIndex = this.uChartsInstance.getCurrentDataIndex(e);
					if(currentIndex !== -1){
						this.$emit('getIndex', {
							type: "getIndex",
							index: currentIndex,
							id: this.canvasId
						});
					}
				}
			},
			touchStart(e) {
				if(this.uChartsInstance){
					this.uChartsInstance.touchStart(e);
				}
			},
			touchMove(e) {
				if(this.uChartsInstance){
					this.uChartsInstance.touchMove(e);
				}
			},
			touchEnd(e) {
				if(this.uChartsInstance){
					this.uChartsInstance.touchEnd(e);
				}
			}
		}
	}
</script>

<style scoped>
	.chartsjs {
		width: 100%;
		height: 100%;
	}

	.charts {
		width: 100%;
		height: 500rpx;
		background-color: #f8f5f2; /* 与首页背景色一致 */
	}
</style>
