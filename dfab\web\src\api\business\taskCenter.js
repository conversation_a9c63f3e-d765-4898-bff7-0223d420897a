import request from '@/utils/request'

// 分页查询任务列表
export function getTasksPage(queryParams) {
  return request({
    url: '/admin/taskCenter/page',
    method: 'post',
    data: queryParams
  })
}

// 查询所有任务列表
export function getTaskList() {
  return request({
    url: '/admin/taskCenter/list',
    method: 'post'
  })
}

// 根据ID获取任务详情
export function getTaskDetail(id) {
  return request({
    url: '/admin/taskCenter/detail',
    method: 'post',
    params: { id }
  })
}

// 处理任务
export function processTask(data) {
  return request({
    url: '/admin/taskCenter/process',
    method: 'post',
    data
  })
}

// 批量处理任务
export function batchProcessTasks(data) {
  return request({
    url: '/admin/taskCenter/batchProcess',
    method: 'post',
    data
  })
}

// 更新任务优先级
export function updateTaskPriority(data) {
  return request({
    url: '/admin/taskCenter/updatePriority',
    method: 'post',
    data
  })
}

// 获取任务统计信息
export function getTaskStatistics() {
  return request({
    url: '/admin/taskCenter/statistics',
    method: 'post'
  })
}

// 更新任务基本信息
export function updateTaskBasicInfo(data) {
  return request({
    url: '/admin/taskCenter/updateBasicInfo',
    method: 'post',
    data
  })
}

// 更新任务客户信息
export function updateTaskCustomerInfo(data) {
  return request({
    url: '/admin/taskCenter/updateCustomerInfo',
    method: 'post',
    data
  })
}
