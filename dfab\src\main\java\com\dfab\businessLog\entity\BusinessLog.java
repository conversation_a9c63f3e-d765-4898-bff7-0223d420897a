package com.dfab.businessLog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务关键日志实体类，用于存储业务关键日志信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("business_log")
@Schema(description = "业务关键日志实体类")
@Builder
public class BusinessLog extends BaseEntity {

    /**
     * 业务日志的唯一标识，系统自动分配的 ID。
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "业务日志 ID", example = "1")
    private Long id;

    /**
     * 微信 openid
     */
    @Schema(description = "微信 openid", example = "wx1234567890")
    private String openId;

    /**
     * 会员入住 ID，关联会员入住表。
     */
    @Schema(description = "会员入住 ID", example = "1")
    private Long memberCheckinId;

    @Schema(description = "业务数据 ID", example = "1")
    private Long businessId;

    /**
     * 业务日志详细描述。
     */
    @Schema(description = "业务日志描述", example = "换房操作")
    private String detail;

    /**
     * 业务日志类型，如：changeRoom 换房
     */
    @Schema(description = "业务日志类型", example = "changeRoom")
    private String type;



}