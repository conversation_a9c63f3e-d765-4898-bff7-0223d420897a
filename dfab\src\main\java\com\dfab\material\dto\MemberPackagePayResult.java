package com.dfab.material.dto;

import com.dfab.material.entity.MaterialInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberPackagePayResult {
    /**
     * 结算金额
     */
    private BigDecimal needPay;
    /**
     * 实际使用金额
     */
    private BigDecimal truePay;
    /**
     * 套餐使用金额
     */
    private BigDecimal packagePay;

    private List<MaterialInfo> packages;

}