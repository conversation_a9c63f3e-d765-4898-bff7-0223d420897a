package com.dfab.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());

        try {
            // 设置创建人
            if (getFieldValByName("creator", metaObject) == null) {
                try {
                    this.strictInsertFill(metaObject, "creator", String.class, SecurityUtils.getLoginUser().getUsername());
                }catch (Exception e){

                }
            }
            if (getFieldValByName("creatorId", metaObject) == null) {
                try {
                    this.strictInsertFill(metaObject, "creatorId", Long.class, SecurityUtils.getLoginUser().getUserId());
                }catch (Exception e){

                }
            }
        }catch (Exception e){
            log.error("补充创建人信息报错",e);
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "modifyTime", Date.class, new Date());

        try {
            // 设置更新人
            if (getFieldValByName("modifier", metaObject) == null) {
                try {
                    this.strictUpdateFill(metaObject, "modifier", String.class, SecurityUtils.getLoginUser().getUsername());
                }catch (Exception e){

                }
            }
            if (getFieldValByName("modifierId", metaObject) == null) {
                try {
                    this.strictUpdateFill(metaObject, "modifierId", Long.class, SecurityUtils.getLoginUser().getUserId());
                }catch (Exception e){

                }
            }
        }catch (Exception e){
            log.error("补充更新人信息报错",e);
        }
    }
}