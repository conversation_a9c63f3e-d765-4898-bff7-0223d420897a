package com.dfab.handicraftClass.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.dfab.handicraftClass.entity.HandicraftClassParticipant;
import com.dfab.handicraftClass.enums.ClassTypeEnum;
import com.dfab.handicraftClass.mapper.HandicraftClassParticipantMapper;
import com.dfab.handicraftClass.service.HandicraftClassParticipantService;
import com.dfab.handicraftClass.service.HandicraftClassService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 手工课参与记录服务实现类
 */
@Service
@Slf4j
public class HandicraftClassParticipantServiceImpl extends ServiceImpl<HandicraftClassParticipantMapper, HandicraftClassParticipant> implements HandicraftClassParticipantService {

    @Autowired
    private AppUserService appUserService;

    @Autowired
    @Lazy
    private MemberCheckinService memberCheckinService;

    @Autowired
    private HandicraftClassService handicraftClassService;

    @Override
    public List<HandicraftClass> getParticipatedClassesByOpenId(String openId) {
        // 获取用户的参与记录
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getOpenId, openId);
        wrapper.orderByDesc(HandicraftClassParticipant::getCreateTime);
        List<HandicraftClassParticipant> participants = list(wrapper);
        
        // 获取对应的课程信息
        if (CollUtil.isEmpty(participants)) {
            return List.of();
        }
        
        List<Long> classIds = participants.stream()
                .map(HandicraftClassParticipant::getClassId)
                .collect(Collectors.toList());
        
        return handicraftClassService.listByIds(classIds);
    }

    @Override
    public List<HandicraftClass> getParticipatedClassesByOpenIdAndType(String openId, Integer classType) {
        List<HandicraftClass> allClasses = getParticipatedClassesByOpenId(openId);
        return allClasses.stream()
                .filter(c -> classType.equals(c.getClassType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<HandicraftClass> getParticipatedClassesByMemberCheckinId(Long memberCheckinId) {
        // 获取该会员的参与记录
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getMemberCheckinId, memberCheckinId);
        wrapper.orderByDesc(HandicraftClassParticipant::getCreateTime);
        List<HandicraftClassParticipant> participants = list(wrapper);
        
        // 获取对应的课程信息
        if (CollUtil.isEmpty(participants)) {
            return List.of();
        }
        
        List<Long> classIds = participants.stream()
                .map(HandicraftClassParticipant::getClassId)
                .collect(Collectors.toList());
        
        return handicraftClassService.listByIds(classIds);
    }

    @Override
    public List<HandicraftClassParticipant> getParticipantsByClassId(Long classId) {
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getClassId, classId);
        wrapper.orderByDesc(HandicraftClassParticipant::getCreateTime);
        return list(wrapper);
    }

    @Override
    public HandicraftClassParticipant joinClass(Long classId, String openId) {
        // 检查是否已经参与
        if (hasJoined(classId, openId)) {
            throw new RuntimeException("您已经参与了这个课程");
        }
        
        // 检查课程是否存在和是否还有名额
        HandicraftClass handicraftClass = handicraftClassService.getById(classId);
        if (handicraftClass == null) {
            throw new RuntimeException("课程不存在");
        }
        
        // 检查名额
        if (handicraftClass.getMaxParticipants() != null) {
            long currentCount = getParticipantsByClassId(classId).size();
            if (currentCount >= handicraftClass.getMaxParticipants()) {
                throw new RuntimeException("课程已满员");
            }
        }
        
        // 创建参与记录
        HandicraftClassParticipant participant = new HandicraftClassParticipant();
        participant.setClassId(classId);
        participant.setOpenId(openId);
        participant.setJoinTime(LocalDateTime.now());
        participant.setStatus(0); // 0-已报名
        
        // 自动填充用户信息
        fillUserInfoFromOpenId(participant);
        
        save(participant);
        
        // 更新课程的当前参与人数
        handicraftClassService.updateCurrentParticipants(classId);
        
        return participant;
    }

    @Override
    public boolean leaveClass(Long classId, String openId) {
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getClassId, classId);
        wrapper.eq(HandicraftClassParticipant::getOpenId, openId);
        
        HandicraftClassParticipant participant = getOne(wrapper);
        if (participant != null) {
            // 删除参与记录
            removeById(participant.getId());
            
            // 更新课程的当前参与人数
            handicraftClassService.updateCurrentParticipants(classId);
            
            return true;
        }
        return false;
    }

    @Override
    public boolean hasJoined(Long classId, String openId) {
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getClassId, classId);
        wrapper.eq(HandicraftClassParticipant::getOpenId, openId);
        return count(wrapper) > 0;
    }

    @Override
    public HandicraftClassParticipant getParticipantRecord(Long classId, String openId) {
        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClassParticipant::getClassId, classId);
        wrapper.eq(HandicraftClassParticipant::getOpenId, openId);
        return getOne(wrapper);
    }

    @Override
    public boolean updateParticipantStatus(Long id, Integer status) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClassParticipant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClassParticipant::getId, id);
        updateWrapper.set(HandicraftClassParticipant::getStatus, status);
        
        return update(updateWrapper);
    }

    @Override
    public boolean cancelParticipation(Long id, String cancelReason) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClassParticipant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClassParticipant::getId, id);
        updateWrapper.set(HandicraftClassParticipant::getStatus, 3); // 3-已取消
        updateWrapper.set(HandicraftClassParticipant::getCancelReason, cancelReason);
        
        return update(updateWrapper);
    }

    @Override
    public boolean rateClass(Long id, Integer rating, String review) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClassParticipant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClassParticipant::getId, id);
        updateWrapper.set(HandicraftClassParticipant::getRating, rating);
        updateWrapper.set(HandicraftClassParticipant::getReview, review);
        
        return update(updateWrapper);
    }

    @Override
    public Map<String, Object> getUserParticipationStatistics(String openId) {
        List<HandicraftClassParticipant> allParticipants = list(
            new LambdaQueryWrapper<HandicraftClassParticipant>()
                .eq(HandicraftClassParticipant::getOpenId, openId)
        );
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", allParticipants.size());
        result.put("joinedCount", allParticipants.stream().filter(p -> p.getStatus() == 0).count());
        result.put("participatedCount", allParticipants.stream().filter(p -> p.getStatus() == 1).count());
        result.put("completedCount", allParticipants.stream().filter(p -> p.getStatus() == 2).count());
        result.put("cancelledCount", allParticipants.stream().filter(p -> p.getStatus() == 3).count());
        
        // 按类型统计
        List<HandicraftClass> participatedClasses = getParticipatedClassesByOpenId(openId);
        Map<Integer, Long> typeCount = new HashMap<>();
        for (ClassTypeEnum type : ClassTypeEnum.values()) {
            long count = participatedClasses.stream()
                .filter(c -> type.getCode().equals(c.getClassType()))
                .count();
            typeCount.put(type.getCode(), count);
        }
        result.put("typeCount", typeCount);
        
        return result;
    }

    @Override
    public PageInfo<HandicraftClassParticipant> getParticipantsPage(int pageNum, int pageSize, HandicraftClassParticipant queryParams) {
        Page<HandicraftClassParticipant> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<HandicraftClassParticipant> wrapper = new LambdaQueryWrapper<>();
        
        if (queryParams != null) {
            if (queryParams.getClassId() != null) {
                wrapper.eq(HandicraftClassParticipant::getClassId, queryParams.getClassId());
            }
            if (StringUtils.hasText(queryParams.getOpenId())) {
                wrapper.eq(HandicraftClassParticipant::getOpenId, queryParams.getOpenId());
            }
            if (queryParams.getStatus() != null) {
                wrapper.eq(HandicraftClassParticipant::getStatus, queryParams.getStatus());
            }
            if (StringUtils.hasText(queryParams.getUserName())) {
                wrapper.like(HandicraftClassParticipant::getUserName, queryParams.getUserName());
            }
            if (queryParams.getMemberCheckinId() != null) {
                wrapper.eq(HandicraftClassParticipant::getMemberCheckinId, queryParams.getMemberCheckinId());
            }
        }

        wrapper.orderByDesc(HandicraftClassParticipant::getCreateTime);

        Page<HandicraftClassParticipant> resultPage = page(page, wrapper);

        PageInfo<HandicraftClassParticipant> pageInfo = new PageInfo<>();
        pageInfo.setList(resultPage.getRecords());
        pageInfo.setTotal(resultPage.getTotal());
        pageInfo.setPageNum((int) resultPage.getCurrent());
        pageInfo.setPageSize((int) resultPage.getSize());
        pageInfo.setPages((int) resultPage.getPages());
        pageInfo.setHasNextPage(resultPage.hasNext());
        pageInfo.setHasPreviousPage(resultPage.hasPrevious());

        return pageInfo;
    }

    /**
     * 根据openId填充用户信息和会员ID
     */
    private void fillUserInfoFromOpenId(HandicraftClassParticipant participant) {
        AppUser appUser = appUserService.getByOpenId(participant.getOpenId());
        if (appUser != null) {
            participant.setUserId(appUser.getId());
            
            if (appUser.getName() != null && !appUser.getName().isEmpty()) {
                participant.setUserName(appUser.getName());
            }
            
            if (appUser.getPhoneNumber() != null && !appUser.getPhoneNumber().isEmpty()) {
                participant.setUserPhone(appUser.getPhoneNumber());
            }
            
            // 查询会员入住信息
            List<MemberCheckin> allMemberCheckinsByOpenId = memberCheckinService.getAllMemberCheckinsByOpenId(appUser.getOpenId());
            if (CollUtil.isNotEmpty(allMemberCheckinsByOpenId)) {
                MemberCheckin memberCheckin = allMemberCheckinsByOpenId.get(0);
                participant.setRoomNumber(memberCheckin.getRoomNumber());
                participant.setMemberCheckinId(memberCheckin.getId());
            }
        }
    }
}
