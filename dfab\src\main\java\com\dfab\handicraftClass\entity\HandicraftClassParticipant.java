package com.dfab.handicraftClass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 手工课参与记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("handicraft_class_participant")
@Schema(description = "手工课参与记录实体类")
@Builder
public class HandicraftClassParticipant extends BaseEntity {
    
    /**
     * 参与记录的唯一标识，系统自动分配的 ID。
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "参与记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 手工课ID，关联handicraft_class表
     */
    @Schema(description = "手工课ID", example = "1", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(name = "手工课ID")
    private Long classId;

    /**
     * 用户的微信 openId
     */
    @Schema(description = "用户的微信 openId", example = "wx1234567890", required = true)
    @Excel(name = "用户的微信 openId")
    private String openId;

    /**
     * 用户id userId
     */
    @Schema(description = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 用户电话
     */
    @Schema(description = "用户电话")
    @Excel(name = "用户电话")
    private String userPhone;

    /**
     * 会员入住记录ID，关联member_checkin表
     */
    @Schema(description = "会员入住记录ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(name = "会员入住记录ID")
    private Long memberCheckinId;

    /**
     * 房间号
     */
    @Schema(description = "房间号", example = "A101")
    @Excel(name = "房间号")
    private String roomNumber;

    /**
     * 参与状态：0-已报名，1-已参与，2-已完成，3-已取消
     */
    @Schema(description = "参与状态：0-已报名，1-已参与，2-已完成，3-已取消", example = "0")
    @Excel(name = "参与状态")
    @Builder.Default
    private Integer status = 0;

    /**
     * 报名时间
     */
    @Schema(description = "报名时间", example = "2024-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "报名时间")
    private LocalDateTime joinTime;

    /**
     * 实际参与时间
     */
    @Schema(description = "实际参与时间", example = "2024-01-01 14:05:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "实际参与时间")
    private LocalDateTime actualJoinTime;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "临时有事")
    @Excel(name = "取消原因")
    private String cancelReason;

    /**
     * 课程评价分数（1-5分）
     */
    @Schema(description = "课程评价分数", example = "5")
    @Excel(name = "课程评价分数")
    private Integer rating;

    /**
     * 课程评价内容
     */
    @Schema(description = "课程评价内容", example = "老师很专业，学到了很多")
    @Excel(name = "课程评价内容")
    private String review;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "第一次参加")
    @Excel(name = "备注信息")
    private String remarks;
}
