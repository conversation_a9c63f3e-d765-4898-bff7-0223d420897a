package com.dfab.taskCenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.taskCenter.entity.PushRecord;
import com.dfab.taskCenter.mapper.PushRecordMapper;
import com.dfab.taskCenter.service.PushRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 推送记录Service实现类
 */
@Slf4j
@Service
public class PushRecordServiceImpl extends ServiceImpl<PushRecordMapper, PushRecord> implements PushRecordService {

    @Override
    public List<PushRecord> getRecordsByTaskId(Long taskId) {
        return baseMapper.selectByTaskId(taskId);
    }

    @Override
    public List<PushRecord> getRecordsByStatus(Integer status) {
        return baseMapper.selectByStatus(status);
    }

    @Override
    public List<PushRecord> getRecordsByReceiverOpenId(String receiverOpenId) {
        return baseMapper.selectByReceiverOpenId(receiverOpenId);
    }

    @Override
    public List<PushRecord> getFailedRecords() {
        return baseMapper.selectFailedRecords();
    }

    @Override
    public List<PushRecord> getRetryRecords(Integer maxRetryCount) {
        return baseMapper.selectRetryRecords(maxRetryCount);
    }

    @Override
    public int getTodayPushCount() {
        return baseMapper.countTodayPushes();
    }

    @Override
    public Double getSuccessRate() {
        return baseMapper.calculateSuccessRate();
    }

    @Override
    public int getCountByPushType(Integer pushType) {
        return baseMapper.countByPushType(pushType);
    }

    @Override
    public boolean createPushRecord(PushRecord record) {
        record.setPushTime(new Date());
        return save(record);
    }

    @Override
    public boolean updatePushStatus(Long recordId, Integer status, String response, String errorMsg) {
        PushRecord record = new PushRecord();
        record.setId(recordId);
        record.setStatus(status);
        record.setResponse(response);
        record.setErrorMsg(errorMsg);
        return updateById(record);
    }

    @Override
    public boolean incrementRetryCount(Long recordId) {
        PushRecord record = getById(recordId);
        if (record != null) {
            record.setRetryCount(record.getRetryCount() + 1);
            return updateById(record);
        }
        return false;
    }
}
