import request from '@/utils/request'

// 通用请求配置，禁用防重复提交检查
const commonConfig = {
  headers: {
    'repeatSubmit': false
  }
};

// 分页查询客户用品补充需求信息
export function pageCustomerSupplyRequest(data) {
  return request({
    url: '/admin/customerSupplyRequest/page',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 查询客户用品补充需求列表
export function listCustomerSupplyRequest(data) {
  return request({
    url: '/admin/customerSupplyRequest/list',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 根据ID查询客户用品补充需求信息
export function getCustomerSupplyRequest(data) {
  return request({
    url: '/admin/customerSupplyRequest/getById',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 处理客户用品补充需求
export function processCustomerSupplyRequest(data) {
  return request({
    url: '/admin/customerSupplyRequest/process',
    method: 'post',
    data: data,
    ...commonConfig
  })
}

// 更新客户用品补充需求信息
export function updateCustomerSupplyRequest(data) {
  return request({
    url: '/admin/customerSupplyRequest/update',
    method: 'post',
    data: data,
    ...commonConfig
  })
}
