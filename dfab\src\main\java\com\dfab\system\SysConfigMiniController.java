package com.dfab.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.ruoyi.system.service.ISysConfigService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/config")
public class SysConfigMiniController{

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 获取参数配置列表
     */
    @PostMapping("/getLuckyCode")
    @Log(title = "获取幸运码", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public String getLuckyCode() {
        return configService.selectConfigByKey("lucky_code");
    }

}
