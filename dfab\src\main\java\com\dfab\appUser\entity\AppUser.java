package com.dfab.appUser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("app_user")
@Schema(description = "App 用户实体类")
@Builder
public class AppUser extends BaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "用户 ID", example = "1")
    private Long id;

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "用户年龄", example = "25")
    private int age;

    // 添加手机号字段
    @Schema(description = "用户手机号", example = "13800138000")
    private String phoneNumber;

    // 微信开放平台唯一标识
    @NotBlank(message = "openId 不能为空")
    @Schema(description = "微信开放平台唯一标识", example = "wx1234567890", required = true)
    private String openId;

    // 微信小程序唯一标识
    @Schema(description = "微信小程序唯一标识", example = "union1234567890")
    private String unionId;

    // 微信昵称
    @Schema(description = "微信昵称", example = "昵称")
    private String wxNickname;

    // 微信头像地址
    @Schema(description = "微信头像地址", example = "https://example.com/avatar.jpg")
    private String wxAvatarUrl;

    // 微信用户性别，0：未知、1：男性、2：女性
    @Schema(description = "微信用户性别，0：未知、1：男性、2：女性", example = "1")
    private Integer wxGender;

    // 微信用户所在国家
    @Schema(description = "微信用户所在国家", example = "中国")
    private String wxCountry;

    // 微信用户所在省份
    @Schema(description = "微信用户所在省份", example = "广东省")
    private String wxProvince;

    // 微信用户所在城市
    @Schema(description = "微信用户所在城市", example = "深圳市")
    private String wxCity;

    // 新增宝爸/宝妈标识，"father" 表示宝爸， "mother" 表示宝妈， "unknown" 表示未知
    @Schema(description = "宝爸/宝妈标识，'father' 表示宝爸， 'mother' 表示宝妈， 'unknown' 表示未知", example = "father")
    private String parentIdentity;

    // 新增最后访问时间字段
    @Schema(description = "最后访问时间", example = "2024-01-01 12:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastAccessTime;

    //类型 用户 user 后台人员 manager
    @Schema(description = "用户类型，用户：user，后台人员：manager", example = "user")
    private String type;
}