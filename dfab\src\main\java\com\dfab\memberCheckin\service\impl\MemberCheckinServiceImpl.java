package com.dfab.memberCheckin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.allergy.entity.Allergy;
import com.dfab.allergy.service.AllergyService;
import com.dfab.appUser.entity.AppUser;
import com.dfab.appUser.service.AppUserService;
import com.dfab.businessLog.entity.BusinessLog;
import com.dfab.businessLog.service.BusinessLogService;
import com.dfab.carInfo.entity.CarInfo;
import com.dfab.carInfo.service.CarService;
import com.dfab.material.entity.InventoryRecord;
import com.dfab.material.service.InventoryRecordService;
import com.dfab.mealReservation.entity.MealReservation;
import com.dfab.mealReservation.service.MealReservationService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.mapper.MemberCheckinMapper;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.dfab.premiumMeal.entity.PremiumMeal;
import com.dfab.premiumMeal.service.PremiumMealService;
import com.dfab.taskCenter.service.TaskCenterService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 入住会员管理 Service 实现类
 */
@Slf4j
@Service
public class MemberCheckinServiceImpl extends ServiceImpl<MemberCheckinMapper, MemberCheckin> implements MemberCheckinService {

    @Resource
    private AppUserService appUserService;

    @Resource
    private AllergyService allergyService;

    @Resource
    private MealReservationService mealReservationService;

    @Resource
    private InventoryRecordService inventoryRecordService;

    @Resource
    private CarService carService;

    @Resource
    private PremiumMealService premiumMealService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private TaskCenterService taskCenterService;

    @Override
    public IPage<MemberCheckin> page(MemberCheckin memberCheckin) {
        this.checkOut();
        // 设置分页参数
        int pageNum = memberCheckin != null && memberCheckin.getPageNum() != null ? memberCheckin.getPageNum() : 1;
        int pageSize = memberCheckin != null && memberCheckin.getPageSize() != null ? memberCheckin.getPageSize() : 10;
        Page<MemberCheckin> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<MemberCheckin> queryWrapper = buildQueryWrapper(memberCheckin);

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);

        IPage<MemberCheckin> result = super.page(page, queryWrapper);

        // 为每个记录关联查询忌口信息和车辆信息，并通过手机号匹配openId
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            for (MemberCheckin record : result.getRecords()) {
                // 通过手机号匹配AppUser获取openId
                if (record.getPhoneNumber() != null && !record.getPhoneNumber().isEmpty() && record.getOpenId() == null) {
                    AppUser appUser = appUserService.getByPhoneNumber(record.getPhoneNumber());
                    if (appUser != null) {
                        record.setOpenId(appUser.getOpenId());
                    }
                }

                setAllergyDetail(record);
                setCarCount(record);
                //高档餐已使用数量
                record.setHaveUsePremiumMealNum(premiumMealService.getByMemberCheckinId(record.getId()).stream().mapToInt(PremiumMeal::getQuantity).sum());
            }
        }

        return result;
    }

    @Override
    public List<MemberCheckin> list(MemberCheckin memberCheckin) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = buildQueryWrapper(memberCheckin);

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);

        List<MemberCheckin> result = super.list(queryWrapper);

        // 为每个记录关联查询忌口信息和车辆信息，并通过手机号匹配openId
        if (result != null && !result.isEmpty()) {
            for (MemberCheckin record : result) {
                // 通过手机号匹配AppUser获取openId
                if (record.getPhoneNumber() != null && !record.getPhoneNumber().isEmpty() && record.getOpenId() == null) {
                    AppUser appUser = appUserService.getByPhoneNumber(record.getPhoneNumber());
                    if (appUser != null) {
                        record.setOpenId(appUser.getOpenId());
                    }
                }

                setAllergyDetail(record);
                setCarCount(record);
            }
        }

        return result;
    }

    @Override
    public MemberCheckin add(MemberCheckin memberCheckin) {

        //一个手机号只有一条数据
        if(StrUtil.isAllBlank(memberCheckin.getMemberName())){
            LambdaQueryWrapper<MemberCheckin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MemberCheckin::getPhoneNumber, memberCheckin.getPhoneNumber());
            MemberCheckin oneByPhone = this.getOne(lambdaQueryWrapper);
            if (oneByPhone != null) {
                throw new RuntimeException("手机号已存在");
            }
        }


        // 如果有openId，获取用户信息并填充相关字段
        if (memberCheckin.getOpenId() != null) {
            AppUser appUser = appUserService.getByOpenId(memberCheckin.getOpenId());
            if (appUser != null) {
                memberCheckin.setUserId(appUser.getId());
                if (appUser.getName() != null && !appUser.getName().isEmpty() && (memberCheckin.getUserName() == null || memberCheckin.getUserName().isEmpty())) {
                    memberCheckin.setUserName(appUser.getName());
                }
                memberCheckin.setUserPhone(appUser.getPhoneNumber());
            }
        }

        // 如果没有设置状态，默认为已入住
        if (memberCheckin.getStatus() == null) {
            memberCheckin.setStatus(0);
        }

        // 确保删除状态字段有默认值
        if (memberCheckin.getDeleteStatus() == null) {
            memberCheckin.setDeleteStatus(0);
        }

        this.save(memberCheckin);
        return memberCheckin;
    }

    @Override
    public Boolean updateByAdmin(MemberCheckin memberCheckin) {
        if (memberCheckin.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        // 获取更新前的会员信息
        MemberCheckin oldMemberCheckin = this.getById(memberCheckin.getId());
        if (oldMemberCheckin == null) {
            throw new RuntimeException("会员信息不存在");
        }

        //一个手机号只有一条数据
        if(StrUtil.isAllBlank(memberCheckin.getMemberName())){
            LambdaQueryWrapper<MemberCheckin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MemberCheckin::getPhoneNumber, memberCheckin.getPhoneNumber());
            lambdaQueryWrapper.ne(MemberCheckin::getId, memberCheckin.getId());
            MemberCheckin oneByPhone = this.getOne(lambdaQueryWrapper);
            if (oneByPhone != null) {
                throw new RuntimeException("手机号已存在");
            }
        }

        // 检查是否状态从非已退住变更为已退住
        boolean isCheckingOut = oldMemberCheckin.getStatus() != 1 && memberCheckin.getStatus() != null && memberCheckin.getStatus() == 1;

        boolean result = this.updateById(memberCheckin);

        // 如果更新成功且会员状态变更为已退住，为关联的车辆生成删除任务
        if (result && isCheckingOut) {
            try {
                createCarDeleteTasksForMember(oldMemberCheckin);
            } catch (Exception e) {
                log.error("为会员{}创建车辆删除任务失败: {}", oldMemberCheckin.getMemberName(), e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * 为会员退住时创建关联车辆的删除任务
     * @param memberCheckin 会员信息
     */
    private void createCarDeleteTasksForMember(MemberCheckin memberCheckin) {
        try {
            // 根据openId和会员ID获取会员关联的所有车辆
            List<CarInfo> cars = carService.getCarsByOpenIdAndMemberCheckinId(
                memberCheckin.getOpenId(),
                memberCheckin.getId()
            );

            if (cars != null && !cars.isEmpty()) {
                log.info("会员{}(ID:{})退住，为{}辆车创建删除任务", memberCheckin.getMemberName(), memberCheckin.getId(), cars.size());

                for (CarInfo car : cars) {
                    try {
                        taskCenterService.createCarDeleteTaskWithMember(
                            car.getId(),
                            car.getPlateNumber(),
                            car.getOwnerName(),
                            car.getPhoneNumber(),
                            car.getOpenId(),
                            car.getUserId(),
                            memberCheckin.getId(),
                            memberCheckin.getMemberName()
                        );
                        log.info("为车辆{}(ID:{})创建删除任务成功", car.getPlateNumber(), car.getId());
                    } catch (Exception e) {
                        log.error("为车辆{}(ID:{})创建删除任务失败: {}", car.getPlateNumber(), car.getId(), e.getMessage(), e);
                    }
                }
            } else {
                log.info("会员{}(ID:{})退住，但未找到关联的车辆", memberCheckin.getMemberName(), memberCheckin.getId());
            }
        } catch (Exception e) {
            log.error("查询会员{}(ID:{})关联车辆失败: {}", memberCheckin.getMemberName(), memberCheckin.getId(), e.getMessage(), e);
        }
    }

    @Override
    public void createCarDeleteTasksForDeletedMember(MemberCheckin memberCheckin) {
        try {
            // 根据openId和会员ID获取会员关联的所有车辆
            List<CarInfo> cars = carService.getCarsByOpenIdAndMemberCheckinId(
                memberCheckin.getOpenId(),
                memberCheckin.getId()
            );

            if (cars != null && !cars.isEmpty()) {
                log.info("会员{}(ID:{})已删除，为{}辆车创建删除任务", memberCheckin.getMemberName(), memberCheckin.getId(), cars.size());

                for (CarInfo car : cars) {
                    try {
                        taskCenterService.createCarDeleteTaskForDeletedMember(
                            car.getId(),
                            car.getPlateNumber(),
                            car.getOwnerName(),
                            car.getPhoneNumber(),
                            car.getOpenId(),
                            car.getUserId(),
                            memberCheckin.getId(),
                            memberCheckin.getMemberName()
                        );

                        log.info("为车辆{}(ID:{})创建删除任务成功", car.getPlateNumber(), car.getId());
                    } catch (Exception e) {
                        log.error("为车辆{}(ID:{})创建删除任务失败: {}", car.getPlateNumber(), car.getId(), e.getMessage(), e);
                    }
                }
            } else {
                log.info("会员{}(ID:{})已删除，但未找到关联的车辆", memberCheckin.getMemberName(), memberCheckin.getId());
            }
        } catch (Exception e) {
            log.error("查询会员{}(ID:{})关联车辆失败: {}", memberCheckin.getMemberName(), memberCheckin.getId(), e.getMessage(), e);
        }
    }

    @Override
    public List<MemberCheckin> getByPhoneNumber(String phoneNumber) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getPhoneNumber, phoneNumber);
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<MemberCheckin> getByRoomNumber(String roomNumber) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getRoomNumber, roomNumber);
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<MemberCheckin> getByUserId(Long userId) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getUserId, userId);
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<MemberCheckin> getByCheckinDateRange(LocalDate startDate, LocalDate endDate) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            queryWrapper.ge(MemberCheckin::getCheckinDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.le(MemberCheckin::getCheckinDate, endDate);
        }
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<MemberCheckin> getByStatus(Integer status) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getStatus, status);
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        List<MemberCheckin> list = this.list(queryWrapper);
        List<Long> idList = list.stream()
                .map(MemberCheckin::getId)
                .toList();

        LambdaQueryWrapper<BusinessLog> businessLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
        businessLogLambdaQueryWrapper.in(BusinessLog::getMemberCheckinId, idList);
        businessLogLambdaQueryWrapper.eq(BusinessLog::getType, "allergyWrong");
        List<BusinessLog> businessLogs = businessLogService.list(businessLogLambdaQueryWrapper);

        LambdaQueryWrapper<PremiumMeal> premiumMealLambdaQueryWrapper = new LambdaQueryWrapper<>();
        premiumMealLambdaQueryWrapper.in(PremiumMeal::getMemberCheckinId, idList);
        premiumMealLambdaQueryWrapper.orderByDesc(PremiumMeal::getConsumeTime);
        premiumMealLambdaQueryWrapper.orderByDesc(PremiumMeal::getType);
        List<PremiumMeal> premiumMeals = premiumMealService.list(premiumMealLambdaQueryWrapper);
        for (MemberCheckin record : list) {
            // 通过手机号匹配AppUser获取openId
            if (record.getPhoneNumber() != null && !record.getPhoneNumber().isEmpty() && record.getOpenId() == null) {
                AppUser appUser = appUserService.getByPhoneNumber(record.getPhoneNumber());
                if (appUser != null) {
                    record.setOpenId(appUser.getOpenId());
                }
            }

            try {
                List<BusinessLog> complaintList = businessLogs.stream().filter(l -> l.getMemberCheckinId().equals(record.getId()) && "allergyWrong".equals(l.getType()) ).toList();

                record.setAllergyComplaintCount(complaintList != null ? complaintList.size() : 0);
            }catch (Exception e){
                log.error("查询投诉次数失败",e);
            }

            setAllergyDetail(record);
            setCarCount(record);
            //高档餐已使用数量
            List<PremiumMeal> byMemberCheckinId = premiumMeals.stream().filter(p -> p.getMemberCheckinId().equals(record.getId())).toList();
            record.setHaveUsePremiumMealList(byMemberCheckinId);
            record.setHaveUsePremiumMealNum(byMemberCheckinId.stream().mapToInt(PremiumMeal::getQuantity).sum());
        }
        return list;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<MemberCheckin> buildQueryWrapper(MemberCheckin memberCheckin) {
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();

        // 添加删除状态条件：查询delete_status为null或0的数据
        queryWrapper.and(wrapper -> wrapper.isNull(MemberCheckin::getDeleteStatus)
                .or().eq(MemberCheckin::getDeleteStatus, 0));

        if (memberCheckin == null) {
            return queryWrapper;
        }

        if (StringUtils.hasText(memberCheckin.getMemberName())) {
            queryWrapper.like(MemberCheckin::getMemberName, memberCheckin.getMemberName());
        }
        
        if (StringUtils.hasText(memberCheckin.getPhoneNumber())) {
            queryWrapper.like(MemberCheckin::getPhoneNumber, memberCheckin.getPhoneNumber());
        }
        
        if (StringUtils.hasText(memberCheckin.getRoomNumber())) {
            queryWrapper.like(MemberCheckin::getRoomNumber, memberCheckin.getRoomNumber());
        }
        
        if (memberCheckin.getCheckinDate() != null) {
            queryWrapper.eq(MemberCheckin::getCheckinDate, memberCheckin.getCheckinDate());
        }
        
        if (memberCheckin.getCheckoutDate() != null) {
            queryWrapper.eq(MemberCheckin::getCheckoutDate, memberCheckin.getCheckoutDate());
        }
        
        if (memberCheckin.getStatus() != null) {
            queryWrapper.eq(MemberCheckin::getStatus, memberCheckin.getStatus());
        }
        
        return queryWrapper;
    }

    public void checkOut(){
        /*try {
            List<MemberCheckin> list = this.list();
            for (MemberCheckin memberCheckin : list){
                if(memberCheckin.getCheckoutDate() != null && memberCheckin.getCheckoutDate().isBefore(LocalDate.now())){
                    if(memberCheckin.getStatus() == 1){
                        continue;
                    }
                    memberCheckin.setStatus(1);
                    this.updateById(MemberCheckin.builder().id(memberCheckin.getId()).status(1).build());
                }else{
                    if(memberCheckin.getStatus() == 0){
                        continue;
                    }
                    memberCheckin.setStatus(0);
                    this.updateById(MemberCheckin.builder()id(memberCheckin.getId()).status(0).build());
                }
            }
        }catch (Exception e){
            log.error("会员自动更新状态失败",e);
        }*/

    }

    @Override
    public List<MemberCheckin> getAllMemberCheckinsByOpenId(String openId) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getOpenId, openId);
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<MemberCheckin> getCurrentMemberCheckinsByOpenId(String openId) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getOpenId, openId);
        queryWrapper.eq(MemberCheckin::getStatus, 0); // 0表示已入住
        queryWrapper.orderByDesc(MemberCheckin::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public MemberCheckin get(MemberCheckin memberCheckin) {
        this.checkOut();
        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getOpenId, memberCheckin.getOpenId());
//        queryWrapper.eq(MemberCheckin::getId, memberCheckin.getId());
        MemberCheckin one = this.getOne(queryWrapper);
        //继续根据手机号查询并补充openid
        if(one == null){
            AppUser appUser = appUserService.getByOpenId(memberCheckin.getOpenId());
            LambdaQueryWrapper<MemberCheckin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MemberCheckin::getPhoneNumber, appUser.getPhoneNumber());
            MemberCheckin oneByPhone = this.getOne(lambdaQueryWrapper);
            if(oneByPhone == null){
                return null;
            }else{
                this.updateById(MemberCheckin.builder().openId(appUser.getOpenId()).id(oneByPhone.getId()).build());
                return oneByPhone;
            }
        }else {
            return one;
        }
    }

    @Override
    public Boolean remove(MemberCheckin memberCheckin) {
        if (memberCheckin.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getOpenId, memberCheckin.getOpenId());
        queryWrapper.eq(MemberCheckin::getId, memberCheckin.getId());
        MemberCheckin one = getOne(queryWrapper);
        if (one != null) {
            // 删除前为关联车辆创建删除任务
            createCarDeleteTasksForDeletedMember(one);
            return this.removeById(one.getId());
        } else {
            log.error("不能删除别人数据");
            return false;
        }
    }

    @Override
    public Boolean update(MemberCheckin memberCheckin) {
        if (memberCheckin.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        LambdaQueryWrapper<MemberCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCheckin::getOpenId, memberCheckin.getOpenId());
        queryWrapper.eq(MemberCheckin::getId, memberCheckin.getId());
        MemberCheckin one = getOne(queryWrapper);
        if (one != null) {
            return this.updateById(memberCheckin);
        } else {
            log.error("不能修改别人数据");
            return false;
        }
    }

    /**
     * 为入住会员记录设置忌口信息
     * @param memberCheckin 入住会员记录
     */
    private void setAllergyDetail(MemberCheckin memberCheckin) {
        if (memberCheckin == null) {
            return;
        }

        try {

            Allergy allergyByMemberId = allergyService.getAllergyByMemberId(memberCheckin.getId());

            if (allergyByMemberId != null && allergyByMemberId.getAllergyDetail() != null) {
                memberCheckin.setAllergyDetail(allergyByMemberId.getAllergyDetail());
                return;
            }

            // 通过手机号或openId查询忌口信息
            Allergy allergy = allergyService.getAllergyByPhoneNumberOrOpenId(
                memberCheckin.getPhoneNumber(),
                memberCheckin.getOpenId()
            );

            if (allergy != null && allergy.getAllergyDetail() != null) {
                memberCheckin.setAllergyDetail(allergy.getAllergyDetail());
            }
        } catch (Exception e) {
            log.warn("查询忌口信息失败，会员ID: {}, 手机号: {}, 错误: {}",
                memberCheckin.getId(), memberCheckin.getPhoneNumber(), e.getMessage());
        }
    }

    /**
     * 为入住会员记录设置车辆数量
     * @param memberCheckin 入住会员记录
     */
    private void setCarCount(MemberCheckin memberCheckin) {
        if (memberCheckin == null) {
            return;
        }

        try {
            // 通过手机号或openId查询车辆信息
            List<CarInfo> cars = carService.getCarsByPhoneNumberOrOpenId(
                memberCheckin.getPhoneNumber(),
                memberCheckin.getOpenId()
            );

            memberCheckin.setCarCount(cars != null ? cars.size() : 0);
        } catch (Exception e) {
            log.warn("查询车辆信息失败，会员ID: {}, 手机号: {}, 错误: {}",
                memberCheckin.getId(), memberCheckin.getPhoneNumber(), e.getMessage());
            memberCheckin.setCarCount(0);
        }
    }


    @Transactional
    @Override
    public Boolean updateRoomNumber(MemberCheckin memberCheckin){
        log.info("开始房号变更 - 接收到的数据: ID={}, 新房号={}", memberCheckin.getId(), memberCheckin.getRoomNumber());

        if (memberCheckin.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        if (memberCheckin.getRoomNumber() == null || memberCheckin.getRoomNumber().trim().isEmpty()) {
            throw new RuntimeException("房号不能为空");
        }

        MemberCheckin oldMember = this.getById(memberCheckin.getId());
        if(oldMember == null){
            throw new RuntimeException("记录不存在");
        }

        // 检查房号是否有变化
        String newRoomNumber = memberCheckin.getRoomNumber().trim();
        String oldRoomNumber = oldMember.getRoomNumber() != null ? oldMember.getRoomNumber().trim() : null;

        log.info("房号变更检查 - 会员ID: {}, 会员姓名: {}, 原房号: [{}], 新房号: [{}]",
            oldMember.getId(), oldMember.getMemberName(), oldRoomNumber, newRoomNumber);

        // 暂时注释掉房号相同检查，先看看能否正常更新
         if(oldRoomNumber != null && oldRoomNumber.equals(newRoomNumber)){
             log.warn("房号未变更 - 原房号和新房号相同: [{}]", oldRoomNumber);
             throw new RuntimeException("请输入新的房间号");
         }

        // 只更新房号
        boolean result = this.updateById(MemberCheckin.builder()
                .id(memberCheckin.getId())
                .roomNumber(newRoomNumber)
                .build());

        if (!result) {
            return false;
        }

        // 如果有用户ID，同时更新相关表中的房号信息
        if (oldMember.getRoomNumber() != null) {
            try {
                // 更新忌口表
                LambdaUpdateWrapper<Allergy> allergyUpdateWrapper = new LambdaUpdateWrapper<>();
                allergyUpdateWrapper.eq(Allergy::getMemberCheckinId, oldMember.getId());
                allergyUpdateWrapper.set(Allergy::getRoomNumber, newRoomNumber);
                allergyService.update(allergyUpdateWrapper);

                // 更新餐食预约表
                LambdaUpdateWrapper<MealReservation> mealReservationLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                mealReservationLambdaUpdateWrapper.eq(MealReservation::getMemberCheckinId, oldMember.getId());
                mealReservationLambdaUpdateWrapper.set(MealReservation::getRoomNumber, newRoomNumber);
                mealReservationService.update(mealReservationLambdaUpdateWrapper);
            } catch (Exception e) {
                log.warn("更新相关表房号失败，用户ID: {}, 新房号: {}, 错误: {}",
                    oldMember.getUserId(), newRoomNumber, e.getMessage());
            }
        }

        // 更新物料记录表（按原房号更新）
        try {
            LambdaUpdateWrapper<InventoryRecord> inventoryRecordLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            inventoryRecordLambdaUpdateWrapper.eq(InventoryRecord::getMemberCheckinId, oldMember.getId());
            inventoryRecordLambdaUpdateWrapper.set(InventoryRecord::getMemberRoomNumber, newRoomNumber);
            inventoryRecordService.update(inventoryRecordLambdaUpdateWrapper);
        } catch (Exception e) {
            log.warn("更新物料记录房号失败，原房号: {}, 新房号: {}, 错误: {}",
                oldMember.getRoomNumber(), newRoomNumber, e.getMessage());
        }

        //记录更改房号记录
        try {
            businessLogService.createByAdmin(BusinessLog.builder()
                .type("changeRoom")
                .detail("原房号: " + oldMember.getRoomNumber() + ", 新房号: " + newRoomNumber)
                .memberCheckinId(oldMember.getId())
                .businessId(oldMember.getId())
                .openId(oldMember.getOpenId())
                .build());
        } catch (Exception e) {
            log.warn("记录房号变更失败，会员ID: {}, 原房号: {}, 新房号: {}, 错误: {}",
                oldMember.getId(), oldMember.getRoomNumber(), newRoomNumber, e.getMessage());
        }

        return true;
    }
}
