<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          style="width: 160px"
          @input="handleQuery"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="queryParams.roomNumber"
          placeholder="请输入房间号"
          clearable
          style="width: 160px"
          @input="handleQuery"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="是否废弃" prop="isOut">
        <el-select v-model="queryParams.isOut" placeholder="请选择是否废弃" clearable style="width: 160px"
                   @change="handleQuery">
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:allergy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:allergy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:allergy:remove']"
        >删除</el-button>
      </el-col>
      <!-- 导出按钮已隐藏 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:mealReservation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="allergyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column label="用户姓名" align="center" prop="userName" width="120" />
      <el-table-column label="联系电话" align="center" prop="userPhone" width="120" />
      <el-table-column label="房间号" align="center" prop="roomNumber" width="100" />
      <el-table-column label="用餐忌口" align="center" prop="allergyDetail" min-width="300">
        <template #default="scope">
          <div v-if="scope.row.allergyDetail">
            <el-tooltip
              :content="getPlainText(scope.row.allergyDetail)"
              placement="top"
              :disabled="getPlainText(scope.row.allergyDetail).length <= 25"
              effect="dark"
            >
              <span class="allergy-text ellipsis-text" v-html="scope.row.allergyDetail"></span>
            </el-tooltip>
          </div>
          <span v-else class="no-allergy">无</span>
        </template>
      </el-table-column>
      <el-table-column label="投诉次数" align="center" prop="complaintCount" width="100">
        <template #default="scope">
          <el-button
              v-if="scope.row.complaintCount > 0"
              type="warning"
              link
              @click="showComplaintList(scope.row)"
              class="complaint-count-btn"
          >
            {{ scope.row.complaintCount }}
          </el-button>
          <span v-else class="no-complaints">0</span>
        </template>
      </el-table-column>
      <el-table-column label="是否废弃" align="center" prop="isOut" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isOut === 1 ? 'danger' : 'success'">
            {{ scope.row.isOut === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:allergy:edit']"
          >修改</el-button>
          <el-button
            v-if="scope.row.isOut !== 1"
            link
            type="warning"
            icon="Warning"
            @click="handleComplaint(scope.row)"
            v-hasPermi="['business:allergy:edit']"
          >投诉</el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:allergy:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
      <el-pagination
        v-show="total > 0"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加或修改用户忌口对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="userAllergyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="选择会员" prop="selectedMember">
          <el-select
            v-model="form.selectedMember"
            placeholder="请选择入住会员"
            style="width: 100%"
            @change="handleMemberChange"
            filterable
          >
            <el-option
              v-for="member in memberList"
              :key="member.id"
              :label="`${member.memberName} - ${member.phoneNumber} - ${member.roomNumber}`"
              :value="member.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="联系电话" prop="userPhone">
          <el-input v-model="form.userPhone" placeholder="自动填充" readonly />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="form.roomNumber" placeholder="自动填充" readonly />
        </el-form-item>
         <el-form-item label="用餐忌口" prop="allergyDetail">
            <ColorTextEditor
              v-model="form.allergyDetail"
              placeholder="请输入用餐忌口"
              :height="120"
            />
        </el-form-item>
<!--        <el-form-item label="是否废弃" prop="isOut">
          <el-select v-model="form.isOut" placeholder="请选择是否废弃" style="width: 100%">
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
          </el-select>
        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 投诉弹窗 -->
    <el-dialog title="投诉忌口信息" v-model="complaintDialogVisible" width="500px" append-to-body>
      <div class="complaint-info">
        <p><strong>用户姓名：</strong>{{ currentComplaintAllergy.userName }}</p>
        <p><strong>房间号：</strong>{{ currentComplaintAllergy.roomNumber }}</p>
        <p><strong>忌口内容：</strong>{{ currentComplaintAllergy.allergyDetail }}</p>
      </div>
      <el-form ref="complaintFormRef" :model="complaintForm" :rules="complaintRules" label-width="80px">
        <el-form-item label="投诉内容" prop="complaintContent">
          <el-input
            v-model="complaintForm.complaintContent"
            type="textarea"
            :rows="4"
            placeholder="请详细描述投诉内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitComplaint">确 定</el-button>
          <el-button @click="cancelComplaint">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 投诉记录列表弹窗 -->
    <el-dialog title="投诉记录" v-model="complaintListVisible" width="800px" append-to-body>
      <div class="complaint-header">
        <h4>{{ currentComplaintAllergy.userName }} - {{ currentComplaintAllergy.roomNumber }}</h4>
        <p>忌口内容：{{ currentComplaintAllergy.allergyDetail }}</p>
      </div>
      <el-table :data="complaintList" style="width: 100%" v-loading="complaintLoading">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="投诉内容" align="center" prop="detail" min-width="300" />
        <el-table-column label="投诉时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="creator" width="120" />
      </el-table>
      <div v-if="complaintList.length === 0 && !complaintLoading" class="no-data">
        <el-empty description="暂无投诉记录" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="complaintListVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { listAllergy, getAllergy, addAllergy, updateAllergy, delAllergy, addComplaint, getComplaintList } from "@/api/business/allergy";
import { getAllMemberCheckins } from "@/api/business/memberCheckin";
import { parseTime } from "@/utils/ruoyi.js";
import ColorTextEditor from "@/components/ColorTextEditor/index.vue";

const { proxy } = getCurrentInstance();

const allergyList = ref([]);
const memberList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 投诉相关变量
const complaintDialogVisible = ref(false);
const complaintListVisible = ref(false);
const complaintLoading = ref(false);
const complaintList = ref([]);
const currentComplaintAllergy = ref({});
const complaintForm = ref({
  complaintContent: ''
});
const complaintRules = ref({
  complaintContent: [
    { required: true, message: "投诉内容不能为空", trigger: "blur" }
  ]
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    roomNumber: undefined,
    isOut: undefined
  },
  rules: {
    selectedMember: [
      { required: true, message: "请选择入住会员", trigger: "change" }
    ],
    allergyDetail: [
      { required: true, message: "用餐忌口不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户忌口列表 */
function getList() {
  loading.value = true;
  listAllergy(queryParams.value).then(response => {
    allergyList.value = response.rows;
    total.value = parseInt(response.total) || 0;
    loading.value = false;
  });
}

/** 处理页面大小变化 */
function handleSizeChange(val) {
  queryParams.value.pageSize = val
  queryParams.value.pageNum = 1
  getList()
}

/** 处理当前页变化 */
function handleCurrentChange(val) {
  queryParams.value.pageNum = val
  getList()
}



/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    selectedMember: undefined,
    userName: undefined,
    userPhone: undefined,
    roomNumber: undefined,
    allergyDetail: undefined,
    openId: undefined,
    memberCheckinId: undefined,
    isOut: 0
  };
  proxy.resetForm("userAllergyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 获取入住会员列表 */
function getMemberList() {
  getAllMemberCheckins().then(response => {
    if (response.code === 200) {
      memberList.value = response.data || [];
    } else {
      proxy.$modal.msgError(response.msg || "获取会员列表失败");
    }
  }).catch(error => {
    console.error('获取会员列表失败:', error);
    proxy.$modal.msgError("获取会员列表失败");
  });
}

/** 会员选择变化处理 */
function handleMemberChange(memberId) {
  if (memberId) {
    const selectedMember = memberList.value.find(member => member.id === memberId);
    if (selectedMember) {
      form.value.userName = selectedMember.memberName;
      form.value.userPhone = selectedMember.phoneNumber;
      form.value.roomNumber = selectedMember.roomNumber;
      form.value.openId = selectedMember.openId;
      form.value.memberCheckinId = selectedMember.id;
    }
  } else {
    // 清空相关字段
    form.value.userName = undefined;
    form.value.userPhone = undefined;
    form.value.roomNumber = undefined;
    form.value.openId = undefined;
    form.value.memberCheckinId = undefined;
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getMemberList(); // 获取会员列表
  open.value = true;
  title.value = "添加用户忌口信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getMemberList(); // 获取会员列表
  const id = row.id || ids.value[0];
  getAllergy({ id: id }).then(response => {
    form.value = response;

    // 根据memberCheckinId设置选中的会员
    if (form.value.memberCheckinId) {
      form.value.selectedMember = form.value.memberCheckinId;
    } else if (form.value.userPhone) {
      // 如果没有memberCheckinId，则根据用户信息找到对应的会员并设置选中
      const matchedMember = memberList.value.find(member =>
        member.phoneNumber === form.value.userPhone
      );
      if (matchedMember) {
        form.value.selectedMember = matchedMember.id;
      }
    }

    open.value = true;
    title.value = "修改用户忌口信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["userAllergyRef"].validate(valid => {
    if (valid) {
      // 创建提交数据的副本
      const submitData = { ...form.value };

      // 确保memberCheckinId被正确设置
      if (submitData.selectedMember) {
        submitData.memberCheckinId = submitData.selectedMember;
      }

      // 移除selectedMember字段
      delete submitData.selectedMember;

      if (form.value.id != undefined) {
        updateAllergy(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAllergy(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const allergyIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除编号为"' + allergyIds + '"的用户忌口信息?').then(function() {
    return delAllergy({ id: allergyIds });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('admin/allergy/export', {
    ...queryParams.value
  }, `用户忌口_${new Date().getTime()}.xlsx`);
}

/** 投诉按钮操作 */
function handleComplaint(row) {
  currentComplaintAllergy.value = row;
  complaintForm.value.complaintContent = '';
  complaintDialogVisible.value = true;
}

/** 提交投诉 */
function submitComplaint() {
  proxy.$refs["complaintFormRef"].validate(valid => {
    if (valid) {
      const data = {
        allergyId: currentComplaintAllergy.value.id,
        complaintContent: complaintForm.value.complaintContent
      };

      addComplaint(data).then(response => {
        proxy.$modal.msgSuccess("投诉提交成功");
        complaintDialogVisible.value = false;
        getList(); // 刷新列表以更新投诉次数
      }).catch(error => {
        console.error('投诉提交失败:', error);
        proxy.$modal.msgError("投诉提交失败");
      });
    }
  });
}

/** 取消投诉 */
function cancelComplaint() {
  complaintDialogVisible.value = false;
  complaintForm.value.complaintContent = '';
  proxy.resetForm("complaintFormRef");
}

/** 显示投诉记录列表 */
function showComplaintList(row) {
  currentComplaintAllergy.value = row;
  complaintListVisible.value = true;
  complaintLoading.value = true;
  complaintList.value = [];

  // 调用API获取投诉记录
  getComplaintList({ allergyId: row.id }).then(response => {
    complaintList.value = response || [];
    complaintLoading.value = false;
  }).catch(error => {
    console.error('获取投诉记录失败:', error);
    proxy.$modal.msgError("获取投诉记录失败");
    complaintLoading.value = false;
  });
}

// 提取HTML中的纯文本内容
const getPlainText = (htmlString) => {
  if (!htmlString) return ''
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlString
  return tempDiv.textContent || tempDiv.innerText || ''
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.allergy-text {
  /* color: #e6a23c; */
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
}

.ellipsis-text {
  display: inline-block;
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.no-allergy {
  color: #909399;
  font-style: italic;
}

.complaint-count-btn {
  font-weight: bold;
  font-size: 14px;
  color: #e6a23c;
}

.no-complaints {
  color: #909399;
  font-style: italic;
}

.complaint-info {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #e6a23c;
}

.complaint-info p {
  margin: 8px 0;
  color: #303133;
}

.complaint-header {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #e6a23c;
}

.complaint-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.complaint-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
</style>