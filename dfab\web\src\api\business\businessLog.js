import request from '@/utils/request'

// 查询业务日志列表
export function listBusinessLog(query) {
  return request({
    url: '/admin/businessLog/list',
    method: 'post',
    data: query
  })
}

// 分页查询业务日志列表
export function pageBusinessLog(query) {
  return request({
    url: '/admin/businessLog/page',
    method: 'post',
    data: query
  })
}

// 查询业务日志详细
export function getBusinessLog(id) {
  return request({
    url: '/admin/businessLog/get',
    method: 'post',
    data: { id }
  })
}

// 新增业务日志
export function addBusinessLog(data) {
  return request({
    url: '/admin/businessLog/save',
    method: 'post',
    data: data
  })
}

// 修改业务日志
export function updateBusinessLog(data) {
  return request({
    url: '/admin/businessLog/update',
    method: 'post',
    data: data
  })
}

// 删除业务日志
export function delBusinessLog(id) {
  return request({
    url: '/admin/businessLog/delete',
    method: 'post',
    data: { id }
  })
}

// 根据会员入住ID和类型查询换房记录
export function getRoomChangeRecords(memberCheckinId) {
  return request({
    url: '/admin/businessLog/listByMemberCheckinIdAndType',
    method: 'post',
    data: {
      type: 'changeRoom',
      memberCheckinId: memberCheckinId
    }
  })
}
